#!/usr/bin/env python3
"""
Comprehensive Screens Localization Analysis Tool
Analyzes the screens_analysis.json file to provide actionable insights for i18n implementation
"""

import json
import sys
from collections import defaultdict, Counter
from pathlib import Path

def load_analysis_data(file_path):
    """Load the screens analysis JSON data"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: {file_path} not found")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {e}")
        sys.exit(1)

def filter_meaningful_strings(strings_data):
    """Filter out technical strings and keep only user-facing content"""
    meaningful_categories = ['text_widgets', 'app_bar_titles', 'form_labels', 'button_labels']
    filtered_data = {}
    
    for file_path, categories in strings_data.items():
        filtered_data[file_path] = {}
        for category in meaningful_categories:
            if category in categories:
                # Filter out technical strings
                filtered_strings = []
                for string_obj in categories[category]:
                    text = string_obj['text']
                    # Skip technical strings
                    if not is_technical_string(text):
                        filtered_strings.append(string_obj)
                filtered_data[file_path][category] = filtered_strings
    
    return filtered_data

def is_technical_string(text):
    """Determine if a string is technical and shouldn't be localized"""
    technical_indicators = [
        'dart:', 'package:', 'import ', 'export ',
        '.dart', '.json', '.yaml', '.xml',
        'http://', 'https://', 'file://',
        'insideTrainNumber', 'coachNumbers', 'date',
        'station_code', 'arrival_time', 'noemail',
        'railway admin', 'war room user', 's2 admin', 'railway officer',
        'self', 'other', 'NA"', 'parts[', '${', 'toString()',
        'padLeft(', 'format(', 'join(', '.length',
    ]
    
    # Skip very short strings (likely technical)
    if len(text.strip()) <= 2:
        return True
    
    # Skip strings that are clearly technical
    for indicator in technical_indicators:
        if indicator in text.lower():
            return True
    
    # Skip strings that are mostly variable interpolation
    if text.count('$') > 1 or text.count('{') > 1:
        return True
    
    return False

def analyze_file_priorities(filtered_data):
    """Analyze files by localization priority"""
    file_stats = []
    
    for file_path, categories in filtered_data.items():
        total_strings = sum(len(strings) for strings in categories.values())
        if total_strings > 0:
            file_stats.append({
                'file': file_path,
                'total_strings': total_strings,
                'text_widgets': len(categories.get('text_widgets', [])),
                'form_labels': len(categories.get('form_labels', [])),
                'button_labels': len(categories.get('button_labels', [])),
                'app_bar_titles': len(categories.get('app_bar_titles', [])),
            })
    
    # Sort by total strings (highest priority first)
    file_stats.sort(key=lambda x: x['total_strings'], reverse=True)
    return file_stats

def categorize_by_screen_type(file_stats):
    """Categorize files by screen type for better organization"""
    categories = {
        'Attendance & Location': [],
        'User Management': [],
        'Train Management': [],
        'Forms & Input': [],
        'Feedback & Communication': [],
        'File Management': [],
        'Other Screens': []
    }
    
    for stat in file_stats:
        file_path = stat['file']
        
        if 'attendance' in file_path or 'map_screen' in file_path:
            categories['Attendance & Location'].append(stat)
        elif any(x in file_path for x in ['user_screen', 'add_user', 'update_user', 'enable_disable', 'request_user']):
            categories['User Management'].append(stat)
        elif any(x in file_path for x in ['train_details', 'edit_train', 'assign_ehk', 'assign_obhs']):
            categories['Train Management'].append(stat)
        elif any(x in file_path for x in ['form', 'signup', 'login', 'otp']):
            categories['Forms & Input'].append(stat)
        elif any(x in file_path for x in ['feedback', 'rail_sathi', 'customer_care']):
            categories['Feedback & Communication'].append(stat)
        elif any(x in file_path for x in ['upload', 'pdf', 'trip_report', 'handover']):
            categories['File Management'].append(stat)
        else:
            categories['Other Screens'].append(stat)
    
    return categories

def generate_summary_report(data, filtered_data, file_stats, categorized_stats):
    """Generate comprehensive summary report"""
    total_files = len([f for f in filtered_data.keys() if any(len(cats) > 0 for cats in filtered_data[f].values())])
    total_meaningful_strings = sum(stat['total_strings'] for stat in file_stats)
    
    # Calculate localization percentage (files already using AppLocalizations)
    already_localized = data['summary']['total_files_scanned'] - total_files
    localization_percentage = (already_localized / data['summary']['total_files_scanned']) * 100
    
    print("=" * 80)
    print("🚀 COMPREHENSIVE SCREENS LOCALIZATION ANALYSIS")
    print("=" * 80)
    print()
    
    print("📊 OVERALL STATISTICS")
    print("-" * 40)
    print(f"Total files scanned: {data['summary']['total_files_scanned']}")
    print(f"Files already localized: {already_localized}")
    print(f"Files needing localization: {total_files}")
    print(f"Current localization coverage: {localization_percentage:.1f}%")
    print(f"Total meaningful strings to localize: {total_meaningful_strings}")
    print()
    
    print("🎯 STRING BREAKDOWN BY CATEGORY")
    print("-" * 40)
    category_totals = defaultdict(int)
    for stat in file_stats:
        for category in ['text_widgets', 'form_labels', 'button_labels', 'app_bar_titles']:
            category_totals[category] += stat[category]
    
    for category, count in category_totals.items():
        print(f"{category.replace('_', ' ').title()}: {count} strings")
    print()
    
    print("🏆 TOP 15 PRIORITY FILES (Most strings to localize)")
    print("-" * 60)
    for i, stat in enumerate(file_stats[:15], 1):
        file_name = Path(stat['file']).name
        print(f"{i:2d}. {file_name:<35} {stat['total_strings']:3d} strings")
        print(f"    📍 {stat['file']}")
        print(f"    📝 Text: {stat['text_widgets']}, Forms: {stat['form_labels']}, Buttons: {stat['button_labels']}")
        print()
    
    print("📂 LOCALIZATION WORKLOAD BY SCREEN CATEGORY")
    print("-" * 50)
    for category, files in categorized_stats.items():
        if files:
            total_strings = sum(f['total_strings'] for f in files)
            print(f"\n{category}: {len(files)} files, {total_strings} strings")
            for file_stat in files[:3]:  # Show top 3 files per category
                file_name = Path(file_stat['file']).name
                print(f"  • {file_name}: {file_stat['total_strings']} strings")
            if len(files) > 3:
                print(f"  ... and {len(files) - 3} more files")
    
    print("\n" + "=" * 80)
    print("📋 IMPLEMENTATION RECOMMENDATIONS")
    print("=" * 80)
    
    # Phase recommendations
    high_priority = [f for f in file_stats if f['total_strings'] >= 20]
    medium_priority = [f for f in file_stats if 10 <= f['total_strings'] < 20]
    low_priority = [f for f in file_stats if f['total_strings'] < 10]
    
    print(f"\n🔥 PHASE 1 - High Priority ({len(high_priority)} files, {sum(f['total_strings'] for f in high_priority)} strings)")
    print("Files with 20+ strings each - Core user-facing screens")
    for f in high_priority[:5]:
        print(f"  • {Path(f['file']).name}")
    
    print(f"\n⚡ PHASE 2 - Medium Priority ({len(medium_priority)} files, {sum(f['total_strings'] for f in medium_priority)} strings)")
    print("Files with 10-19 strings each - Secondary screens")
    
    print(f"\n✅ PHASE 3 - Low Priority ({len(low_priority)} files, {sum(f['total_strings'] for f in low_priority)} strings)")
    print("Files with <10 strings each - Minor screens and widgets")
    
    print(f"\n⏱️  ESTIMATED EFFORT")
    print(f"High Priority: ~{len(high_priority) * 2} hours (2 hours per file)")
    print(f"Medium Priority: ~{len(medium_priority) * 1} hours (1 hour per file)")
    print(f"Low Priority: ~{len(low_priority) * 0.5:.1f} hours (30 min per file)")
    print(f"Total estimated effort: ~{(len(high_priority) * 2) + (len(medium_priority) * 1) + (len(low_priority) * 0.5):.1f} hours")

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 screens_localization_analysis.py <screens_analysis.json>")
        sys.exit(1)
    
    analysis_file = sys.argv[1]
    data = load_analysis_data(analysis_file)
    
    # Filter meaningful strings
    filtered_data = filter_meaningful_strings(data['detailed_results'])
    
    # Analyze file priorities
    file_stats = analyze_file_priorities(filtered_data)
    
    # Categorize by screen type
    categorized_stats = categorize_by_screen_type(file_stats)
    
    # Generate comprehensive report
    generate_summary_report(data, filtered_data, file_stats, categorized_stats)

if __name__ == "__main__":
    main()
