#!/usr/bin/env dart

import 'dart:convert';
import 'dart:io';
import 'package:csv/csv.dart';
import 'package:excel/excel.dart';
import 'package:path/path.dart' as path;

/// Script to generate translation template from app_en.arb file
/// Outputs UTF-8 CSV and optionally XLSX format with Indian language columns
/// 
/// Usage:
/// dart scripts/generate_translation_template.dart [options]
/// 
/// Options:
/// --output=<path>    Specify output file path (default: docs/translation_template.csv)
/// --xlsx              Also generate XLSX format

void main(List<String> arguments) async {
  print('🚀 Starting Translation Template Generator...');
  
  try {
    // Parse command line arguments
    final config = _parseArguments(arguments);
    
    // Validate environment
    await _validateEnvironment();
    
    // Read ARB file
    final arbData = await _readArbFile();
    
    // Extract translation entries (preserving order)
    final entries = _extractTranslationEntries(arbData);
    
    // Generate CSV
    final csvPath = await _generateCsv(entries, config.outputPath);
    print('✅ CSV generated: $csvPath');
    
    // Generate XLSX if requested
    if (config.generateXlsx) {
      final xlsxPath = await _generateXlsx(entries, config.outputPath);
      print('✅ XLSX generated: $xlsxPath');
    }
    
    print('🎉 Translation template generation completed!');
    _printUsageInstructions(csvPath, config.generateXlsx);
    
  } catch (e) {
    print('❌ Error: $e');
    exit(1);
  }
}

/// Configuration class for command line arguments
class Config {
  final String outputPath;
  final bool generateXlsx;
  
  Config({required this.outputPath, required this.generateXlsx});
}

/// Parses command line arguments
Config _parseArguments(List<String> arguments) {
  String outputPath = 'docs/translation_template.csv';
  bool generateXlsx = false;
  
  for (final arg in arguments) {
    if (arg.startsWith('--output=')) {
      outputPath = arg.substring('--output='.length);
    } else if (arg == '--xlsx') {
      generateXlsx = true;
    }
  }
  
  return Config(outputPath: outputPath, generateXlsx: generateXlsx);
}

/// Validates that required files and directories exist
Future<void> _validateEnvironment() async {
  final arbFile = File('lib/l10n/app_en.arb');
  if (!await arbFile.exists()) {
    throw Exception('ARB file not found: lib/l10n/app_en.arb');
  }
  
  final docsDir = Directory('docs');
  if (!await docsDir.exists()) {
    await docsDir.create(recursive: true);
    print('📁 Created docs directory');
  }
}

/// Reads and parses the ARB file
Future<Map<String, dynamic>> _readArbFile() async {
  print('📖 Reading ARB file...');
  
  final arbFile = File('lib/l10n/app_en.arb');
  final content = await arbFile.readAsString();
  
  try {
    return json.decode(content) as Map<String, dynamic>;
  } catch (e) {
    throw Exception('Invalid JSON in ARB file: $e');
  }
}

/// Extracts translation entries from ARB data (preserving order)
List<TranslationEntry> _extractTranslationEntries(Map<String, dynamic> arbData) {
  print('🔍 Extracting translation entries...');
  
  final entries = <TranslationEntry>[];
  
  // First, add the @@locale row as a reference entry
  final locale = arbData['@@locale'] as String?;
  if (locale != null) {
    entries.add(TranslationEntry(
      key: '@@locale',
      sourceText: locale,
      description: 'Locale metadata - reference only',
      context: 'metadata',
    ));
  }
  
  // Extract translation keys in order (skip metadata entries)
  for (final entry in arbData.entries) {
    final key = entry.key;
    
    // Skip locale marker and metadata entries
    if (key == '@@locale' || key.startsWith('@')) {
      continue;
    }
    
    final value = entry.value;
    if (value is! String) {
      continue;
    }
    
    // Get metadata if available
    final metadataKey = '@$key';
    final metadata = arbData[metadataKey] as Map<String, dynamic>?;
    
    entries.add(TranslationEntry(
      key: key,
      sourceText: value,
      description: metadata?['description'] as String?,
      context: metadata?['context'] as String?,
    ));
  }
  
  print('📊 Found ${entries.length} translation entries');
  return entries;
}

/// Generates CSV file with translation template
Future<String> _generateCsv(List<TranslationEntry> entries, String outputPath) async {
  print('📝 Generating CSV file...');
  
  // Define the exact column structure required
  final headers = [
    'key',
    'english', 
    'context',
    'description',
    'hi',  // Hindi
    'bn',  // Bengali
    'as',  // Assamese
    'pa',  // Punjabi
    'mr',  // Marathi
    'kn',  // Kannada
    'ta',  // Tamil
    'te',  // Telugu
    'ml',  // Malayalam
  ];
  
  // Create CSV data
  final csvData = <List<String>>[];
  
  // Add header row
  csvData.add(headers);
  
  // Add data rows
  for (final entry in entries) {
    final row = [
      entry.key,
      entry.sourceText,
      entry.context ?? '',
      entry.description ?? '',
      '', // hi - empty for translation
      '', // bn - empty for translation
      '', // as - empty for translation
      '', // pa - empty for translation
      '', // mr - empty for translation
      '', // kn - empty for translation
      '', // ta - empty for translation
      '', // te - empty for translation
      '', // ml - empty for translation
    ];
    csvData.add(row);
  }
  
  // Convert to CSV string using csv package
  final csvString = const ListToCsvConverter().convert(csvData);
  
  // Write to file with UTF-8 encoding
  final file = File(outputPath);
  await file.writeAsString('\uFEFF$csvString', encoding: utf8);
  
  return outputPath;
}

/// Generates XLSX file using the excel package
Future<String> _generateXlsx(List<TranslationEntry> entries, String csvPath) async {
  print('📊 Generating XLSX file...');
  
  // Create Excel workbook
  final excel = Excel.createExcel();
  final sheet = excel['Sheet1'];
  
  // Define the exact column structure required
  final headers = [
    'key',
    'english', 
    'context',
    'description',
    'hi',  // Hindi
    'bn',  // Bengali
    'as',  // Assamese
    'pa',  // Punjabi
    'mr',  // Marathi
    'kn',  // Kannada
    'ta',  // Tamil
    'te',  // Telugu
    'ml',  // Malayalam
  ];
  
  // Add header row
  for (int i = 0; i < headers.length; i++) {
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0)).value = headers[i];
  }
  
  // Add data rows
  for (int rowIndex = 0; rowIndex < entries.length; rowIndex++) {
    final entry = entries[rowIndex];
    final dataRow = rowIndex + 1; // +1 because row 0 is headers
    
    final rowData = [
      entry.key,
      entry.sourceText,
      entry.context ?? '',
      entry.description ?? '',
      '', // hi - empty for translation
      '', // bn - empty for translation
      '', // as - empty for translation
      '', // pa - empty for translation
      '', // mr - empty for translation
      '', // kn - empty for translation
      '', // ta - empty for translation
      '', // te - empty for translation
      '', // ml - empty for translation
    ];
    
    for (int colIndex = 0; colIndex < rowData.length; colIndex++) {
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: colIndex, rowIndex: dataRow)).value = rowData[colIndex];
    }
  }
  
  // Save XLSX file
  final xlsxPath = csvPath.replaceAll('.csv', '.xlsx');
  final xlsxBytes = excel.encode();
  final xlsxFile = File(xlsxPath);
  await xlsxFile.writeAsBytes(xlsxBytes!);
  
  return xlsxPath;
}


/// Prints usage instructions
void _printUsageInstructions(String csvPath, bool xlsxGenerated) {
  print('\n📋 Usage Instructions:');
  print('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  print('1. Share the generated file with translators: $csvPath');
  if (xlsxGenerated) {
    print('   Excel format: ${csvPath.replaceAll('.csv', '.xlsx')}');
  }
  print('2. Translators should fill the language columns:');
  print('   • hi (Hindi) • bn (Bengali) • as (Assamese) • pa (Punjabi)');
  print('   • mr (Marathi) • kn (Kannada) • ta (Tamil) • te (Telugu) • ml (Malayalam)');
  print('3. The @@locale row is for reference - preserve it');
  print('4. DO NOT MODIFY: key, english, context, description columns');
  print('5. Variables like \${variable} should be kept as-is in translations');
  print('6. Return the completed file for ARB file generation');
  print('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
}

/// Represents a translation entry
class TranslationEntry {
  final String key;
  final String sourceText;
  final String? description;
  final String? context;
  
  TranslationEntry({
    required this.key,
    required this.sourceText,
    this.description,
    this.context,
  });
  
  @override
  String toString() {
    return 'TranslationEntry(key: $key, sourceText: $sourceText, context: $context)';
  }
}
