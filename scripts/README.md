# Translation Template Generator

This script generates translation templates from the Flutter ARB localization file.

## Purpose

The `generate_translation_template.dart` script reads the `lib/l10n/app_en.arb` file and creates translation templates in CSV and optionally XLSX format that can be shared with translators.

## Features

- ✅ Reads ARB file and extracts all translatable strings
- ✅ Generates UTF-8 CSV with proper escaping
- ✅ Optional XLSX generation for Excel compatibility
- ✅ Preserves metadata (context, descriptions)
- ✅ Includes empty columns for target translations
- ✅ Proper handling of variables and special characters
- ✅ Automated file naming with timestamps

## Usage

### Basic CSV Generation
```bash
dart scripts/generate_translation_template.dart
```

### CSV + XLSX Generation
```bash
dart scripts/generate_translation_template.dart --xlsx
```

## Output

The script creates files in the `translations/` directory:

- `translation_template_YYYY-MM-DD.csv` - UTF-8 CSV file
- `translation_template_YYYY-MM-DD.xlsx` - Excel file (if --xlsx flag used)

## CSV Structure

| Column | Description |
|--------|-------------|
| Key | Unique identifier for the translation (DO NOT MODIFY) |
| Context | Context category (e.g., text_widgets, form_labels) |
| Source Text (English) | Original English text |
| Target Text | **Fill this column with translations** |
| Description | Additional context/description |
| Notes | Space for translator comments |

## For Translators

1. **Fill the "Target Text" column** with translations
2. **Keep variables intact**: `${variable}` should remain `${variable}` in translations
3. **Use the "Notes" column** for any questions or comments
4. **DO NOT modify** the Key or Context columns
5. **Preserve line breaks** and special formatting where present

## Example Workflow

1. **Generate template:**
   ```bash
   dart scripts/generate_translation_template.dart --xlsx
   ```

2. **Share with translator:**
   - Send `translations/translation_template_YYYY-MM-DD.xlsx`
   - Include instructions from this README

3. **Receive completed file:**
   - Translator fills "Target Text" column
   - Returns file with translations

4. **Process translations:**
   - Use returned file to generate new ARB files for target languages
   - Integrate with Flutter localization system

## Script Details

- **Input:** `lib/l10n/app_en.arb`
- **Output:** `translations/translation_template_*.{csv,xlsx}`
- **Encoding:** UTF-8 with BOM for Excel compatibility
- **Dependencies:** Built-in Dart libraries only

## Notes

- The script automatically creates the `translations/` directory if it doesn't exist
- Files are timestamped to avoid conflicts
- CSV uses proper escaping for fields containing commas, quotes, or newlines
- XLSX generation is simplified - for production use, consider a dedicated XLSX library

## Integration with Existing Tools

This script complements the existing ARB processing tools in the `tools/` directory and follows the same conventions for localization file management.
