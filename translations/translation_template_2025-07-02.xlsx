Key	Context	Source Text (English)	Target Text	Description	Notes
text_reenter_otp	text_widgets	Re-enter OTP		Text from text_widgets: Re-enter OTP	
text_deny	text_widgets	Deny		Text from text_widgets: Deny	
text_enable	text_widgets	Enable		Text from text_widgets: Enable	
text_location_access_required	text_widgets	Location Access Required		Text from text_widgets: Location Access Required	
text_decline	text_widgets	Decline		Text from text_widgets: Decline	
text_accept	text_widgets	Accept		Text from text_widgets: Accept	
text_confirm_delete	text_widgets	Confirm Delete		Text from text_widgets: Confirm Delete	
text_cancel	text_widgets	Cancel		Text from text_widgets: Cancel	
text_delete	text_widgets	Delete		Text from text_widgets: Delete	
text_storage_permission_is	text_widgets	Storage permission is required to download files		Text from text_widgets: Storage permission is required to download files	
text_please_select_a	text_widgets	Please select a train first		Text from text_widgets: Please select a train first	
text_update_required	text_widgets	Update Required		Text from text_widgets: Update Required	
text_update_now	text_widgets	Update Now		Text from text_widgets: Update Now	
text_please_select_a_1	text_widgets	Please select a train number and date.		Text from text_widgets: Please select a train number and date.	
text_all_details_updated	text_widgets	All details updated successfully.		Text from text_widgets: All details updated successfully.	
text_failed_to_update	text_widgets	Failed to update details: $e		Text from text_widgets: Failed to update details: $e	
text_data_refreshed_successfully	text_widgets	Data refreshed successfully		Text from text_widgets: Data refreshed successfully	
text_train_location_saved	text_widgets	Train Location Saved Successfully		Text from text_widgets: Train Location Saved Successfully	
text_self	text_widgets	Self		Text from text_widgets: Self	
text_other_ca	text_widgets	Other CA		Text from text_widgets: Other CA	
text_other_ehkobhs	text_widgets	Other EHK/OBHS		Text from text_widgets: Other EHK/OBHS	
text_chart_has_not	text_widgets	Chart has not been prepared for this station		Text from text_widgets: Chart has not been prepared for this station	
text_camera	text_widgets	Camera		Text from text_widgets: Camera	
text_gallery	text_widgets	Gallery		Text from text_widgets: Gallery	
text_getting_location	text_widgets	Getting location...		Text from text_widgets: Getting location...	
text_attendance_marked_successfully	text_widgets	Attendance marked successfully!		Text from text_widgets: Attendance marked successfully!	
text_error_message	text_widgets	Error: $message		Text from text_widgets: Error: $message	
text_failed_to_get	text_widgets	Failed to get location: $e		Text from text_widgets: Failed to get location: $e	
text_error_fetching_images	text_widgets	Error fetching images: $e		Text from text_widgets: Error fetching images: $e	
text_attendance_already_submitted	text_widgets	Attendance Already Submitted		Text from text_widgets: Attendance Already Submitted	
text_back_to_all	text_widgets	Back to all users		Text from text_widgets: Back to all users	
text_submit	text_widgets	Submit		Text from text_widgets: Submit	
text_upload_status	text_widgets	Upload Status		Text from text_widgets: Upload Status	
text_compressing_image	text_widgets	Compressing image		Text from text_widgets: Compressing image	
text_upload_entrykeysubstring0_6	text_widgets	"Upload ${entry.key.substring(0	 6)}..."		"Text from text_widgets: Upload ${entry.key.substring(0	 6)}..."	
text_close	text_widgets	Close		Text from text_widgets: Close	
text_image_uploading	text_widgets	Image Uploading		Text from text_widgets: Image Uploading	
text_no_attendance_found	text_widgets	No attendance found.		Text from text_widgets: No attendance found.	
text_error_snapshoterror	text_widgets	Error: ${snapshot.error}		Text from text_widgets: Error: ${snapshot.error}	
text_latitude_entrylatitude	text_widgets	latitude: ${entry.latitude}		Text from text_widgets: latitude: ${entry.latitude}	
text_longitude_entrylongitude	text_widgets	longitude: ${entry.longitude}		Text from text_widgets: longitude: ${entry.longitude}	
text_distance_entrydistance_km	text_widgets	Distance: ${entry.distance} km		Text from text_widgets: Distance: ${entry.distance} km	
text_updated_by_entryupdatedby	text_widgets	Updated By: ${entry.updatedBy}		Text from text_widgets: Updated By: ${entry.updatedBy}	
text_no_data_available	text_widgets	No data available.		Text from text_widgets: No data available.	
text_show_more	text_widgets	Show More		Text from text_widgets: Show More	
text_show_less	text_widgets	Show Less		Text from text_widgets: Show Less	
text_could_not_open	text_widgets	Could not open file: ${result.message}		Text from text_widgets: Could not open file: ${result.message}	
text_download_started	text_widgets	Download Started!		Text from text_widgets: Download Started!	
text_pdf_downloaded_successfully	text_widgets	PDF downloaded successfully to ${path}		Text from text_widgets: PDF downloaded successfully to ${path}	
text_download	text_widgets	Download		Text from text_widgets: Download	
text_get_in_email	text_widgets	Get in Email		Text from text_widgets: Get in Email	
text_could_not_launch	text_widgets	Could not launch the link		Text from text_widgets: Could not launch the link	
text_permission_denied	text_widgets	Permission Denied		Text from text_widgets: Permission Denied	
text_requested_users	text_widgets	Requested Users		Text from text_widgets: Requested Users	
text_end_date_cannot	text_widgets	End date cannot be before start date		Text from text_widgets: End date cannot be before start date	
text_please_select_both	text_widgets	Please select both suspension dates		Text from text_widgets: Please select both suspension dates	
text_update_user_details	text_widgets	Update User Details		Text from text_widgets: Update User Details	
text_request_for_update	text_widgets	Request For Update User Details		Text from text_widgets: Request For Update User Details	
text_i_dont_have	text_widgets	I don't have an email		Text from text_widgets: I don't have an email	
text_information	text_widgets	Information		Text from text_widgets: Information	
text_request_for_sign	text_widgets	Request For Sign Up		Text from text_widgets: Request For Sign Up	
text_forgot_password	text_widgets	Forgot Password		Text from text_widgets: Forgot Password	
text_error	text_widgets	Error		Text from text_widgets: Error	
text_send_otp	text_widgets	Send OTP		Text from text_widgets: Send OTP	
text_verify_otp	text_widgets	Verify OTP		Text from text_widgets: Verify OTP	
text_sign_in_with	text_widgets	Sign in with Google		Text from text_widgets: Sign in with Google	
text_new_user_sign	text_widgets	New User? Sign Up Here		Text from text_widgets: New User? Sign Up Here	
text_resend_otp	text_widgets	Resend OTP		Text from text_widgets: Resend OTP	
text_please_enter_a	text_widgets	Please enter a valid 10-digit PNR number		Text from text_widgets: Please enter a valid 10-digit PNR number	
text_failed_to_fetch	text_widgets	Failed to fetch PNR data. Please try again.		Text from text_widgets: Failed to fetch PNR data. Please try again.	
text_check_pnr_status	text_widgets	Check PNR Status		Text from text_widgets: Check PNR Status	
text_pnr_number	text_widgets	PNR Number		Text from text_widgets: PNR Number	
text_no_pnr_data	text_widgets	No PNR Data Found		Text from text_widgets: No PNR Data Found	
text_please_turn_on	text_widgets	Please turn on location services		Text from text_widgets: Please turn on location services	
text_an_error_occurred	text_widgets	An error occurred: $e		Text from text_widgets: An error occurred: $e	
text_your_current_location	text_widgets	Your current location		Text from text_widgets: Your current location	
text_location_services_disabled	text_widgets	Location Services Disabled		Text from text_widgets: Location Services Disabled	
text_please_enable_location	text_widgets	Please enable location services to proceed.		Text from text_widgets: Please enable location services to proceed.	
text_location_permission_denied	text_widgets	Location Permission Denied		Text from text_widgets: Location Permission Denied	
text_open_settings	text_widgets	Open Settings		Text from text_widgets: Open Settings	
text_location_permission_denied_1	text_widgets	Location Permission Denied Forever		Text from text_widgets: Location Permission Denied Forever	
text_refresh_failed_e	text_widgets	Refresh failed: ${e}		Text from text_widgets: Refresh failed: ${e}	
text_no_location_data	text_widgets	No location data available.		Text from text_widgets: No location data available.	
text_no_data_available_1	text_widgets	No data available		Text from text_widgets: No data available	
text_no_train_details	text_widgets	No train details available.		Text from text_widgets: No train details available.	
text_none	text_widgets	None		Text from text_widgets: None	
text_download_pdf_for	text_widgets	Download PDF for all stations		Text from text_widgets: Download PDF for all stations	
text_mail_pdf_for	text_widgets	Mail PDF for all stations		Text from text_widgets: Mail PDF for all stations	
text_add_configuration	text_widgets	Add Configuration		Text from text_widgets: Add Configuration	
text_select_charting_day	text_widgets	Select Charting Day		Text from text_widgets: Select Charting Day	
text_submitting	text_widgets	Submitting...		Text from text_widgets: Submitting...	
text_return_gap_updated	text_widgets	Return gap updated successfully		Text from text_widgets: Return gap updated successfully	
text_data_not_refreshed	text_widgets	Data not refreshed: $e		Text from text_widgets: Data not refreshed: $e	
text_edit_configuration	text_widgets	Edit Configuration		Text from text_widgets: Edit Configuration	
text_no_coaches_available	text_widgets	No coaches available		Text from text_widgets: No coaches available	
text_invalid_response_format	text_widgets	Invalid response format from server		Text from text_widgets: Invalid response format from server	
text_please_select_a_2	text_widgets	Please select a train and date first		Text from text_widgets: Please select a train and date first	
text_coach_handover_report	text_widgets	Coach Handover Report		Text from text_widgets: Coach Handover Report	
text_save_selection	text_widgets	Save Selection		Text from text_widgets: Save Selection	
text_select_media_type	text_widgets	Select Media Type		Text from text_widgets: Select Media Type	
text_image	text_widgets	Image		Text from text_widgets: Image	
text_video	text_widgets	Video		Text from text_widgets: Video	
text_please_select_images	text_widgets	Please select images to upload		Text from text_widgets: Please select images to upload	
text_please_select_at	text_widgets	Please select at least one issue		Text from text_widgets: Please select at least one issue	
text_failed_to_upload	text_widgets	Failed to upload images		Text from text_widgets: Failed to upload images	
text_error_updating_issue	text_widgets	Error updating issue: $e		Text from text_widgets: Error updating issue: $e	
text_statustype_by	text_widgets	$statusType By		Text from text_widgets: $statusType By	
text_no_images_available	text_widgets	No images available		Text from text_widgets: No images available	
text_issuesubissue	text_widgets	Issue/Subissue		Text from text_widgets: Issue/Subissue	
text_status	text_widgets	Status		Text from text_widgets: Status	
text_submitupdate	text_widgets	Submit/Update		Text from text_widgets: Submit/Update	
text_reportedby	text_widgets	Reported_by		Text from text_widgets: Reported_by	
text_fixedby	text_widgets	Fixed_by		Text from text_widgets: Fixed_by	
text_resolvedby	text_widgets	Resolved_by		Text from text_widgets: Resolved_by	
text_pick_images	text_widgets	Pick Images		Text from text_widgets: Pick Images	
text_uploading	text_widgets	Uploading...		Text from text_widgets: Uploading...	
text_submit_upload	text_widgets	Submit & Upload		Text from text_widgets: Submit & Upload	
text_retry	text_widgets	Retry		Text from text_widgets: Retry	
text_approve	text_widgets	Approve		Text from text_widgets: Approve	
text_no_requests_selected	text_widgets	No requests selected		Text from text_widgets: No requests selected	
text_are_you_sure	text_widgets	Are you sure you want to approve these users:		Text from text_widgets: Are you sure you want to approve these users:	
text_requeststoprocesslength_users_approved	text_widgets	${requestsToProcess.length} users approved successfully		Text from text_widgets: ${requestsToProcess.length} users approved success...	
text_confirm_denial	text_widgets	Confirm Denial		Text from text_widgets: Confirm Denial	
text_deny_request	text_widgets	Deny Request		Text from text_widgets: Deny Request	
text_approve_selected	text_widgets	Approve Selected		Text from text_widgets: Approve Selected	
text_approve_all	text_widgets	Approve All		Text from text_widgets: Approve All	
text_processing_requests	text_widgets	Processing requests...		Text from text_widgets: Processing requests...	
text_clear_search	text_widgets	Clear search		Text from text_widgets: Clear search	
text_failed_to_fetch_1	text_widgets	Failed to fetch complaints		Text from text_widgets: Failed to fetch complaints	
text_error_fetching_trains	text_widgets	Error fetching trains: $e		Text from text_widgets: Error fetching trains: $e	
text_traintrainno_traintrainname	text_widgets	${train.trainNo} - ${train.trainName}		Text from text_widgets: ${train.trainNo} - ${train.trainName}	
text_other	text_widgets	Other		Text from text_widgets: Other	
text_existing_images	text_widgets	Existing Images:		Text from text_widgets: Existing Images:	
text_delete_image	text_widgets	Delete Image		Text from text_widgets: Delete Image	
text_image_deleted	text_widgets	Image deleted		Text from text_widgets: Image deleted	
text_newly_selected_images	text_widgets	Newly Selected Images:		Text from text_widgets: Newly Selected Images:	
text_add_image	text_widgets	Add Image		Text from text_widgets: Add Image	
text_complaint_updated	text_widgets	Complaint updated		Text from text_widgets: Complaint updated	
text_update_failed	text_widgets	Update failed		Text from text_widgets: Update failed	
text_save_changes	text_widgets	Save Changes		Text from text_widgets: Save Changes	
text_delete_complaint	text_widgets	Delete Complaint		Text from text_widgets: Delete Complaint	
text_are_you_sure_1	text_widgets	Are you sure you want to delete this complaint?		Text from text_widgets: Are you sure you want to delete this complaint?	
text_complaint_deleted_successfully	text_widgets	Complaint deleted successfully		Text from text_widgets: Complaint deleted successfully	
text_failed_to_delete	text_widgets	Failed to delete complaint		Text from text_widgets: Failed to delete complaint	
text_select_date	text_widgets	Select Date		Text from text_widgets: Select Date	
text_no_complaints_found	text_widgets	No complaints found		Text from text_widgets: No complaints found	
text_train_no_complainttrainnumber	text_widgets	Train No: ${complaint.trainNumber}		Text from text_widgets: Train No: ${complaint.trainNumber}	
text_date_complaintcomplaindate	text_widgets	Date: ${complaint.complainDate}		Text from text_widgets: Date: ${complaint.complainDate}	
text_pnr_complaintpnrnumber	text_widgets	PNR: ${complaint.pnrNumber}		Text from text_widgets: PNR: ${complaint.pnrNumber}	
text_edit	text_widgets	Edit		Text from text_widgets: Edit	
text_success	text_widgets	Success		Text from text_widgets: Success	
text_complaint_submitted_successfully	text_widgets	Complaint submitted successfully!		Text from text_widgets: Complaint submitted successfully!	
text_failed_to_submit	text_widgets	Failed to submit complaint		Text from text_widgets: Failed to submit complaint	
text_error_e	text_widgets	Error: $e		Text from text_widgets: Error: $e	
text_ehk_ehkdisplay	text_widgets	EHK: $ehkDisplay		Text from text_widgets: EHK: $ehkDisplay	
text_pending	text_widgets	Pending		Text from text_widgets: Pending	
text_completed	text_widgets	Completed		Text from text_widgets: Completed	
text_upload_imagevideo	text_widgets	Upload Image/Video		Text from text_widgets: Upload Image/Video	
text_submit_issue	text_widgets	Submit Issue		Text from text_widgets: Submit Issue	
text_validate	text_widgets	Validate		Text from text_widgets: Validate	
text_next	text_widgets	Next		Text from text_widgets: Next	
text_error_loading_authentication	text_widgets	Error loading authentication state		Text from text_widgets: Error loading authentication state	
text_storage_permission_required	text_widgets	Storage Permission Required		Text from text_widgets: Storage Permission Required	
text_please_select_a_3	text_widgets	Please select a JSON file to submit.		Text from text_widgets: Please select a JSON file to submit.	
text_error_json_file	text_widgets	Error: Json file is not in the correct format		Text from text_widgets: Error: Json file is not in the correct format	
text_selection_cleared	text_widgets	Selection cleared.		Text from text_widgets: Selection cleared.	
text_upload_json_data	text_widgets	Upload Json Data		Text from text_widgets: Upload Json Data	
text_update	text_widgets	Update		Text from text_widgets: Update	
text_add_issue	text_widgets	Add Issue		Text from text_widgets: Add Issue	
text_no_subissues	text_widgets	No subissues		Text from text_widgets: No subissues	
text_select_issue	text_widgets	Select Issue		Text from text_widgets: Select Issue	
text_add_subissue	text_widgets	Add Subissue		Text from text_widgets: Add Subissue	
text_add_new_item	text_widgets	Add New Item		Text from text_widgets: Add New Item	
text_images_or_videos	text_widgets	Images or Videos upload initiated successfully		Text from text_widgets: Images or Videos upload initiated successfully	
text_please_select_an	text_widgets	Please select an image and issue to upload		Text from text_widgets: Please select an image and issue to upload	
text_issues_saved_upload	text_widgets	Issues saved upload Images/Videos		Text from text_widgets: Issues saved upload Images/Videos	
text_select_issues_for	text_widgets	Select Issues for Coach $coach		Text from text_widgets: Select Issues for Coach $coach	
text_pick_imagesvideos	text_widgets	Pick Images/Videos		Text from text_widgets: Pick Images/Videos	
text_please_select_imagevideo	text_widgets	Please select image/video to upload		Text from text_widgets: Please select image/video to upload	
text_confirm_deletion	text_widgets	Confirm Deletion		Text from text_widgets: Confirm Deletion	
text_delete_report	text_widgets	Delete Report		Text from text_widgets: Delete Report	
text_coach_issue_status	text_widgets	Coach Issue Status		Text from text_widgets: Coach Issue Status	
text_both_person_and	text_widgets	Both person and date/time are required.		Text from text_widgets: Both person and date/time are required.	
text_select_widgetstatustype_by	text_widgets	Select ${widget.statusType} By & Date		Text from text_widgets: Select ${widget.statusType} By & Date	
text_subissue_widgetname	text_widgets	SubIssue : ${widget.name} 		Text from text_widgets: SubIssue : ${widget.name} 	
text_issue_widgetname	text_widgets	Issue : ${widget.name} 		Text from text_widgets: Issue : ${widget.name} 	
text_confirm	text_widgets	Confirm		Text from text_widgets: Confirm	
text_manage_issues	text_widgets	Manage Issues		Text from text_widgets: Manage Issues	
text_rake_deficiency_report	text_widgets	Rake Deficiency Report Issues		Text from text_widgets: Rake Deficiency Report Issues	
text_upload_pnr_image	text_widgets	Upload PNR Image		Text from text_widgets: Upload PNR Image	
text_pick_imagesvideos_for	text_widgets	Pick Images/Videos for Feedback		Text from text_widgets: Pick Images/Videos for Feedback	
text_please_wait_until	text_widgets	Please wait until the upload is complete		Text from text_widgets: Please wait until the upload is complete	
text_submit_feedback	text_widgets	Submit Feedback		Text from text_widgets: Submit Feedback	
text_verify_email	text_widgets	Verify Email		Text from text_widgets: Verify Email	
text_check_your_inbox	text_widgets	• Check your inbox first		Text from text_widgets: • Check your inbox first	
text_if_not_found	text_widgets	"• If not found	 check spam/junk folder"		"Text from text_widgets: • If not found	 check spam/junk folder"	
text_add_our_domain	text_widgets	• Add our domain to your safe sender list		Text from text_widgets: • Add our domain to your safe sender list	
text_i_understand	text_widgets	I Understand		Text from text_widgets: I Understand	
text_nonac	text_widgets	NONAC		Text from text_widgets: NONAC	
text_select	text_widgets	Select		Text from text_widgets: Select	
text_failed_to_load	text_widgets	Failed to load image		Text from text_widgets: Failed to load image	
text_review_feedback	text_widgets	Review Feedback		Text from text_widgets: Review Feedback	
text_deleting_feedback	text_widgets	Deleting feedback...		Text from text_widgets: Deleting feedback...	
text_feedback_deleted_successfully	text_widgets	Feedback deleted successfully		Text from text_widgets: Feedback deleted successfully	
text_error_deleting_feedback	text_widgets	Error deleting feedback: $e		Text from text_widgets: Error deleting feedback: $e	
text_no_feedback_available	text_widgets	No feedback available for this train.		Text from text_widgets: No feedback available for this train.	
text_train_no_trainnumber	text_widgets	Train No: $trainNumber		Text from text_widgets: Train No: $trainNumber	
text_non_ac	text_widgets	Non AC		Text from text_widgets: Non AC	
text_message	text_widgets	Message		Text from text_widgets: Message	
text_job_chart_status	text_widgets	Job Chart Status Added		Text from text_widgets: Job Chart Status Added	
text_please_select_all	text_widgets	Please select all fields before submitting.		Text from text_widgets: Please select all fields before submitting.	
text_update_amount_for	text_widgets	Update Amount for $userId		Text from text_widgets: Update Amount for $userId	
text_assigned	text_widgets	Assigned		Text from text_widgets: Assigned	
text_amount	text_widgets	Amount		Text from text_widgets: Amount	
text_no_image_url	text_widgets	No image URL provided		Text from text_widgets: No image URL provided	
text_image_downloaded_successfully	text_widgets	Image downloaded successfully!		Text from text_widgets: Image downloaded successfully!	
text_failed_to_download	text_widgets	Failed to download image		Text from text_widgets: Failed to download image	
text_failed_to_download_1	text_widgets	Failed to download image: $error		Text from text_widgets: Failed to download image: $error	
text_image_detail	text_widgets	Image Detail		Text from text_widgets: Image Detail	
text_download_image	text_widgets	Download Image		Text from text_widgets: Download Image	
text_train_trainnumber_details	text_widgets	Train $trainNumber details deleted successfully		Text from text_widgets: Train $trainNumber details deleted successfully	
text_confirm_deactivation	text_widgets	Confirm Deactivation		Text from text_widgets: Confirm Deactivation	
text_proceed	text_widgets	Proceed		Text from text_widgets: Proceed	
text_add_email	text_widgets	Add Email		Text from text_widgets: Add Email	
text_back	text_widgets	Back		Text from text_widgets: Back	
text_email_verification	text_widgets	Email Verification		Text from text_widgets: Email Verification	
text_phone_verification	text_widgets	Phone Verification		Text from text_widgets: Phone Verification	
text_logout_confirmation	text_widgets	Logout Confirmation		Text from text_widgets: Logout Confirmation	
text_do_you_want	text_widgets	Do you want to logout now?		Text from text_widgets: Do you want to logout now?	
text_addupdate	text_widgets	Add/Update		Text from text_widgets: Add/Update	
text_alert	text_widgets	Alert		Text from text_widgets: Alert	
text_generate_otp	text_widgets	Generate OTP		Text from text_widgets: Generate OTP	
text_otp_sent_successfully	text_widgets	OTP sent successfully!		Text from text_widgets: OTP sent successfully!	
text_failed_to_send	text_widgets	Failed to send OTP: $e		Text from text_widgets: Failed to send OTP: $e	
text_please_enter_the	text_widgets	Please enter the OTP		Text from text_widgets: Please enter the OTP	
text_email_saved_successfully	text_widgets	Email saved successfully!		Text from text_widgets: Email saved successfully!	
text_failed_to_verify	text_widgets	Failed to verify OTP: $e		Text from text_widgets: Failed to verify OTP: $e	
text_send_mobile_otp	text_widgets	Send Mobile OTP		Text from text_widgets: Send Mobile OTP	
text_send_email_otp	text_widgets	Send Email OTP		Text from text_widgets: Send Email OTP	
text_uploaded_at_formatteddate	text_widgets	Uploaded at: $formattedDate		Text from text_widgets: Uploaded at: $formattedDate	
text_uploaded_by_widgetimageresponsecreatedby	text_widgets	Uploaded by: ${widget.imageResponse.createdBy}		Text from text_widgets: Uploaded by: ${widget.imageResponse.createdBy}	
text_id_widgetimageresponseid	text_widgets	id: ${widget.imageResponse.id}		Text from text_widgets: id: ${widget.imageResponse.id}	
text_coach_widgetimageresponsecoach	text_widgets	Coach: ${widget.imageResponse.coach}		Text from text_widgets: Coach: ${widget.imageResponse.coach}	
text_issue_widgetimageresponseissue	text_widgets	Issue: ${widget.imageResponse.issue}		Text from text_widgets: Issue: ${widget.imageResponse.issue}	
text_delete_confirmation	text_widgets	Delete Confirmation		Text from text_widgets: Delete Confirmation	
text_are_you_sure_2	text_widgets	Are you sure you want to delete this image?		Text from text_widgets: Are you sure you want to delete this image?	
text_save	text_widgets	Save		Text from text_widgets: Save	
text_ehkca	text_widgets	EHK/CA		Text from text_widgets: EHK/CA	
text_image_upload_initiated	text_widgets	Image upload initiated successfully		Text from text_widgets: Image upload initiated successfully	
text_failed_to_upload_1	text_widgets	Failed to upload image		Text from text_widgets: Failed to upload image	
text_please_select_an_1	text_widgets	Please select an image to upload		Text from text_widgets: Please select an image to upload	
text_jobchart_deleted_successfully	text_widgets	JobChart deleted successfully		Text from text_widgets: JobChart deleted successfully	
text_failed_to_delete_1	text_widgets	Failed to delete JobChart		Text from text_widgets: Failed to delete JobChart	
text_pick_image	text_widgets	Pick Image		Text from text_widgets: Pick Image	
text_upload_image	text_widgets	Upload Image		Text from text_widgets: Upload Image	
text_failed_to_load_1	text_widgets	Failed to load train numbers: $e		Text from text_widgets: Failed to load train numbers: $e	
text_language	text_widgets	Language		Text from text_widgets: Language	
text_select_language	text_widgets	Select Language		Text from text_widgets: Select Language	
text_train_tracker	text_widgets	Train Tracker		Text from text_widgets: Train Tracker	
text_assign_ca	text_widgets	Assign CA		Text from text_widgets: Assign CA	
text_assign_cs	text_widgets	Assign CS		Text from text_widgets: Assign CS	
text_pnr_details	text_widgets	PNR Details		Text from text_widgets: PNR Details	
text_rail_sathi	text_widgets	Rail Sathi		Text from text_widgets: Rail Sathi	
text_passenger_chart	text_widgets	Passenger Chart		Text from text_widgets: Passenger Chart	
text_map_screen	text_widgets	Map Screen		Text from text_widgets: Map Screen	
text_configuration	text_widgets	Configuration		Text from text_widgets: Configuration	
text_reports	text_widgets	Reports		Text from text_widgets: Reports	
text_passenger_feedback	text_widgets	Passenger Feedback		Text from text_widgets: Passenger Feedback	
text_rake_deficiency_report_1	text_widgets	Rake Deficiency Report		Text from text_widgets: Rake Deficiency Report	
text_obhs_to_mcc	text_widgets	OBHS to MCC Handover		Text from text_widgets: OBHS to MCC Handover	
text_mcc_to_obhs	text_widgets	MCC to OBHS Handover		Text from text_widgets: MCC to OBHS Handover	
text_upload_data	text_widgets	Upload data		Text from text_widgets: Upload data	
text_user_management	text_widgets	User Management		Text from text_widgets: User Management	
text_issue_management	text_widgets	Issue Management		Text from text_widgets: Issue Management	
text_rail_sathi_qr	text_widgets	Rail Sathi Qr		Text from text_widgets: Rail Sathi Qr	
text_customer_care	text_widgets	Customer Care		Text from text_widgets: Customer Care	
title_location_access_required	app_bar_titles	Location Access Required		Text from app_bar_titles: Location Access Required	
title_upload_status	app_bar_titles	Upload Status		Text from app_bar_titles: Upload Status	
title_compressing_image	app_bar_titles	Compressing image		Text from app_bar_titles: Compressing image	
title_upload_entrykeysubstring0_6	app_bar_titles	"Upload ${entry.key.substring(0	 6)}..."		"Text from app_bar_titles: Upload ${entry.key.substring(0	 6)}..."	
title_permission_denied	app_bar_titles	Permission Denied		Text from app_bar_titles: Permission Denied	
title_confirm_delete	app_bar_titles	Confirm Delete		Text from app_bar_titles: Confirm Delete	
title_add_new_item	app_bar_titles	Add New Item		Text from app_bar_titles: Add New Item	
title_add_issue	app_bar_titles	Add Issue		Text from app_bar_titles: Add Issue	
title_add_subissue	app_bar_titles	Add Subissue		Text from app_bar_titles: Add Subissue	
title_select_widgetstatustype_by	app_bar_titles	Select ${widget.statusType} By & Date		Text from app_bar_titles: Select ${widget.statusType} By & Date	
title_update_amount_for	app_bar_titles	Update Amount for $userId		Text from app_bar_titles: Update Amount for $userId	
form_train_number	form_labels	Train Number		Text from form_labels: Train Number	
form_date	form_labels	Date		Text from form_labels: Date	
form_select_date_ddmmmyyyy	form_labels	Select Date (DD-MMM-YYYY)		Text from form_labels: Select Date (DD-MMM-YYYY)	
form_first_name	form_labels	First Name *		Text from form_labels: First Name *	
form_enter_first_name	form_labels	Enter first name		Text from form_labels: Enter first name	
form_middle_name_optional	form_labels	Middle Name (Optional)		Text from form_labels: Middle Name (Optional)	
form_enter_middle_name	form_labels	Enter middle name		Text from form_labels: Enter middle name	
form_last_name	form_labels	Last Name *		Text from form_labels: Last Name *	
form_enter_last_name	form_labels	Enter last name		Text from form_labels: Enter last name	
form_phone_number	form_labels	Phone Number		Text from form_labels: Phone Number	
form_secondary_phone_number	form_labels	Secondary Phone Number (Optional)		Text from form_labels: Secondary Phone Number (Optional)	
form_whatsapp_number	form_labels	WhatsApp Number		Text from form_labels: WhatsApp Number	
form_enter_10digit_whatsapp	form_labels	Enter 10-digit WhatsApp number		Text from form_labels: Enter 10-digit WhatsApp number	
form_email	form_labels	Email *		Text from form_labels: Email *	
form_enter_your_email	form_labels	Enter your email address		Text from form_labels: Enter your email address	
form_enter_your_first	form_labels	Enter your first name		Text from form_labels: Enter your first name	
form_enter_your_middle	form_labels	Enter your middle name		Text from form_labels: Enter your middle name	
form_enter_your_last	form_labels	Enter your last name		Text from form_labels: Enter your last name	
form_enter_10digit_secondary	form_labels	Enter 10-digit secondary phone number		Text from form_labels: Enter 10-digit secondary phone number	
form_email_1	form_labels	Email		Text from form_labels: Email	
form_mobile_number	form_labels	Mobile Number		Text from form_labels: Mobile Number	
form_enter_your_10digit	form_labels	Enter your 10-digit mobile number only		Text from form_labels: Enter your 10-digit mobile number only	
form_password	form_labels	Password *		Text from form_labels: Password *	
form_select_train_numbers	form_labels	Select Train Numbers		Text from form_labels: Select Train Numbers	
form_zone	form_labels	Zone		Text from form_labels: Zone	
form_zone_1	form_labels	Zone *		Text from form_labels: Zone *	
form_employee_id	form_labels	Employee Id *		Text from form_labels: Employee Id *	
form_depot	form_labels	Depot *		Text from form_labels: Depot *	
form_reenter_password	form_labels	Re-enter Password *		Text from form_labels: Re-enter Password *	
form_divisions	form_labels	Divisions		Text from form_labels: Divisions	
form_divisions_1	form_labels	Divisions *		Text from form_labels: Divisions *	
form_select_coaches	form_labels	Select Coaches		Text from form_labels: Select Coaches	
form_middle_name	form_labels	Middle Name		Text from form_labels: Middle Name	
form_select_train_number	form_labels	Select Train Number		Text from form_labels: Select Train Number	
form_train_name	form_labels	Train Name		Text from form_labels: Train Name	
form_select_stations	form_labels	Select Stations		Text from form_labels: Select Stations	
form_select_date	form_labels	Select Date		Text from form_labels: Select Date	
form_whatsapp_number_1	form_labels	WhatsApp Number *		Text from form_labels: WhatsApp Number *	
form_enter_secondary_phone	form_labels	Enter secondary phone number (Optional)		Text from form_labels: Enter secondary phone number (Optional)	
form_mobile_number_1	form_labels	Mobile Number *		Text from form_labels: Mobile Number *	
form_enter_mobile_number	form_labels	Enter mobile number to fetch details		Text from form_labels: Enter mobile number to fetch details	
form_enter_mobile_number_1	form_labels	Enter Mobile Number		Text from form_labels: Enter Mobile Number	
form_related_train	form_labels	Related Train		Text from form_labels: Related Train	
form_division	form_labels	Division		Text from form_labels: Division	
form_depot_1	form_labels	Depot		Text from form_labels: Depot	
form_charting_day	form_labels	Charting Day		Text from form_labels: Charting Day	
form_from_station	form_labels	From Station		Text from form_labels: From Station	
form_to_station	form_labels	To Station		Text from form_labels: To Station	
form_direction_updown	form_labels	Direction (Up/Down)		Text from form_labels: Direction (Up/Down)	
form_start_time	form_labels	Start Time		Text from form_labels: Start Time	
form_eg_0900_am	form_labels	e.g. 09:00 AM		Text from form_labels: e.g. 09:00 AM	
form_end_time	form_labels	End Time		Text from form_labels: End Time	
form_eg_0500_pm	form_labels	e.g. 05:00 PM		Text from form_labels: e.g. 05:00 PM	
form_charting_time	form_labels	Charting Time		Text from form_labels: Charting Time	
form_return_gap_days	form_labels	Return Gap (Days)		Text from form_labels: Return Gap (Days)	
form_inout	form_labels	In/Out		Text from form_labels: In/Out	
form_related_train_number	form_labels	Related Train Number		Text from form_labels: Related Train Number	
form_updown	form_labels	Up/Down		Text from form_labels: Up/Down	
form_train_type	form_labels	Train Type		Text from form_labels: Train Type	
form_search_train_number	form_labels	Search Train Number		Text from form_labels: Search Train Number	
form_coaches_comma_separated	form_labels	Coaches (comma separated)		Text from form_labels: Coaches (comma separated)	
form_eg_h_gsl	form_labels	"E.g.	 H	 GSL	 A	 B	 M"		"Text from form_labels: E.g.	 H	 GSL	 A	 B	 M"	
form_enter_coach_names	form_labels	Enter coach names separated by commas		Text from form_labels: Enter coach names separated by commas	
form_select_days	form_labels	Select Days		Text from form_labels: Select Days	
form_add_stoppage	form_labels	Add Stoppage		Text from form_labels: Add Stoppage	
form_type_a_station	form_labels	Type a station name and press Enter or Space		Text from form_labels: Type a station name and press Enter or Space	
form_search	form_labels	Search		Text from form_labels: Search	
form_stoppages_in_sequence	form_labels	Stoppages in Sequence		Text from form_labels: Stoppages in Sequence	
form_type_a_station_1	form_labels	Type a Station and hit space or Enter		Text from form_labels: Type a Station and hit space or Enter	
form_frequency	form_labels	Frequency		Text from form_labels: Frequency	
form_enter_new_coach	form_labels	Enter new coach		Text from form_labels: Enter new coach	
form_use_comma_to	form_labels	Use comma to add multiple coaches		Text from form_labels: Use comma to add multiple coaches	
form_add_your_comments	form_labels	Add your comments here (max 250 characters)...		Text from form_labels: Add your comments here (max 250 characters)...	
form_search_by_name	form_labels	"Search by name	 email	 phone or depot"		"Text from form_labels: Search by name	 email	 phone or depot"	
form_train	form_labels	Train		Text from form_labels: Train	
form_complaint_type	form_labels	Complaint Type		Text from form_labels: Complaint Type	
form_status	form_labels	Status		Text from form_labels: Status	
form_write_your_issue	form_labels	Write your issue		Text from form_labels: Write your issue	
form_issue_status	form_labels	Issue Status		Text from form_labels: Issue Status	
form_name	form_labels	Name		Text from form_labels: Name	
form_search_by_train	form_labels	Search by train number or name		Text from form_labels: Search by train number or name	
form_train_selection	form_labels	Train Selection		Text from form_labels: Train Selection	
form_journey_start_date	form_labels	Journey Start Date		Text from form_labels: Journey Start Date	
form_ddmmyyyy	form_labels	DD/MM/YYYY		Text from form_labels: DD/MM/YYYY	
form_coach	form_labels	Coach		Text from form_labels: Coach	
form_berth	form_labels	Berth		Text from form_labels: Berth	
form_issue_name	form_labels	Issue Name		Text from form_labels: Issue Name	
form_subissue_name	form_labels	Subissue Name		Text from form_labels: Subissue Name	
form_search_1	form_labels	Search...		Text from form_labels: Search...	
form_widgetstatustype_by	form_labels	${widget.statusType} by		Text from form_labels: ${widget.statusType} by	
form_select_date_time	form_labels	Select Date & Time		Text from form_labels: Select Date & Time	
form_add_your_feedback	form_labels	Add your feedback here...		Text from form_labels: Add your feedback here...	
form_task_status	form_labels	Task Status *		Text from form_labels: Task Status *	
form_search_train_number_1	form_labels	Search train number		Text from form_labels: Search train number	
form_train_number_1	form_labels	Train Number *		Text from form_labels: Train Number *	
form_pnr_number	form_labels	PNR Number *		Text from form_labels: PNR Number *	
form_passenger_name	form_labels	Passenger Name *		Text from form_labels: Passenger Name *	
form_coach_no	form_labels	Coach No *		Text from form_labels: Coach No *	
form_berth_no	form_labels	Berth No *		Text from form_labels: Berth No *	
form_email_id	form_labels	Email ID		Text from form_labels: Email ID	
form_enter_otp	form_labels	Enter OTP		Text from form_labels: Enter OTP	
form_issue_type	form_labels	Issue Type		Text from form_labels: Issue Type	
form_sub_issue_type	form_labels	Sub Issue Type		Text from form_labels: Sub Issue Type	
form_resolved_yesno	form_labels	Resolved (Yes/No) *		Text from form_labels: Resolved (Yes/No) *	
form_crn_number	form_labels	CRN Number*		Text from form_labels: CRN Number*	
form_train_no	form_labels	Train No *		Text from form_labels: Train No *	
form_train_name_1	form_labels	Train Name *		Text from form_labels: Train Name *	
form_marks_1_to	form_labels	Marks (1 to 10) *		Text from form_labels: Marks (1 to 10) *	
form_remarks_by_passenger	form_labels	Remarks by Passenger		Text from form_labels: Remarks by Passenger	
form_passenger_name_1	form_labels	Passenger Name		Text from form_labels: Passenger Name	
form_pnr_number_1	form_labels	PNR Number		Text from form_labels: PNR Number	
form_crn_number_1	form_labels	CRN Number		Text from form_labels: CRN Number	
form_coach_no_1	form_labels	Coach No		Text from form_labels: Coach No	
form_berth_no_1	form_labels	Berth No		Text from form_labels: Berth No	
form_remarks	form_labels	Remarks		Text from form_labels: Remarks	
form_task_status_1	form_labels	Task Status		Text from form_labels: Task Status	
form_feedback	form_labels	Feedback		Text from form_labels: Feedback	
form_amount_in_hand	form_labels	Amount in Hand (₹)		Text from form_labels: Amount in Hand (₹)	
form_select_user	form_labels	Select User		Text from form_labels: Select User	
form_enter_email_otp	form_labels	Enter Email OTP		Text from form_labels: Enter Email OTP	
form_enter_phone_otp	form_labels	Enter Phone OTP		Text from form_labels: Enter Phone OTP	
form_select_coaches_optional	form_labels	Select Coaches (optional)		Text from form_labels: Select Coaches (optional)	
btn_deny	button_labels	Deny		Text from button_labels: Deny	
btn_enable	button_labels	Enable		Text from button_labels: Enable	
btn_decline	button_labels	Decline		Text from button_labels: Decline	
btn_accept	button_labels	Accept		Text from button_labels: Accept	
btn_self	button_labels	Self		Text from button_labels: Self	
btn_other_ca	button_labels	Other CA		Text from button_labels: Other CA	
btn_other_ehkobhs	button_labels	Other EHK/OBHS		Text from button_labels: Other EHK/OBHS	
btn_close	button_labels	Close		Text from button_labels: Close	
btn_no_attendance_found	button_labels	No attendance found.		Text from button_labels: No attendance found.	
btn_error_snapshoterror	button_labels	Error: ${snapshot.error}		Text from button_labels: Error: ${snapshot.error}	
btn_no_data_available	button_labels	No data available.		Text from button_labels: No data available.	
btn_your_current_location	button_labels	Your current location		Text from button_labels: Your current location	
btn_no_location_data	button_labels	No location data available.		Text from button_labels: No location data available.	
btn_no_data_available_1	button_labels	No data available		Text from button_labels: No data available	
btn_add_configuration	button_labels	Add Configuration		Text from button_labels: Add Configuration	
btn_select_charting_day	button_labels	Select Charting Day		Text from button_labels: Select Charting Day	
btn_edit_configuration	button_labels	Edit Configuration		Text from button_labels: Edit Configuration	
btn_no_coaches_available	button_labels	No coaches available		Text from button_labels: No coaches available	
btn_coach_handover_report	button_labels	Coach Handover Report		Text from button_labels: Coach Handover Report	
btn_statustype_by	button_labels	$statusType By		Text from button_labels: $statusType By	
btn_traintrainno_traintrainname	button_labels	${train.trainNo} - ${train.trainName}		Text from button_labels: ${train.trainNo} - ${train.trainName}	
btn_other	button_labels	Other		Text from button_labels: Other	
btn_no_complaints_found	button_labels	No complaints found		Text from button_labels: No complaints found	
btn_pending	button_labels	Pending		Text from button_labels: Pending	
btn_completed	button_labels	Completed		Text from button_labels: Completed	
btn_upload_json_data	button_labels	Upload Json Data		Text from button_labels: Upload Json Data	
btn_cancel	button_labels	Cancel		Text from button_labels: Cancel	
btn_delete	button_labels	Delete		Text from button_labels: Delete	
btn_save_selection	button_labels	Save Selection		Text from button_labels: Save Selection	
btn_coach_issue_status	button_labels	Coach Issue Status		Text from button_labels: Coach Issue Status	
btn_rake_deficiency_report	button_labels	Rake Deficiency Report Issues		Text from button_labels: Rake Deficiency Report Issues	
btn_no_feedback_available	button_labels	No feedback available for this train.		Text from button_labels: No feedback available for this train.	
btn_save	button_labels	Save		Text from button_labels: Save	
snackbar_please_select_a	snackbar_messages	Please select a train first		Text from snackbar_messages: Please select a train first	
snackbar_data_refreshed_successfully	snackbar_messages	Data refreshed successfully		Text from snackbar_messages: Data refreshed successfully	
snackbar_train_location_saved	snackbar_messages	Train Location Saved Successfully		Text from snackbar_messages: Train Location Saved Successfully	
snackbar_error_fetching_images	snackbar_messages	Error fetching images: $e		Text from snackbar_messages: Error fetching images: $e	
snackbar_download_started	snackbar_messages	Download Started!		Text from snackbar_messages: Download Started!	
snackbar_pdf_downloaded_successfully	snackbar_messages	PDF downloaded successfully to ${path}		Text from snackbar_messages: PDF downloaded successfully to ${path}	
snackbar_could_not_launch	snackbar_messages	Could not launch the link		Text from snackbar_messages: Could not launch the link	
snackbar_refresh_failed_e	snackbar_messages	Refresh failed: ${e}		Text from snackbar_messages: Refresh failed: ${e}	
snackbar_return_gap_updated	snackbar_messages	Return gap updated successfully		Text from snackbar_messages: Return gap updated successfully	
snackbar_data_not_refreshed	snackbar_messages	Data not refreshed: $e		Text from snackbar_messages: Data not refreshed: $e	
snackbar_invalid_response_format	snackbar_messages	Invalid response format from server		Text from snackbar_messages: Invalid response format from server	
snackbar_please_select_a_1	snackbar_messages	Please select a train and date first		Text from snackbar_messages: Please select a train and date first	
snackbar_please_select_images	snackbar_messages	Please select images to upload		Text from snackbar_messages: Please select images to upload	
snackbar_please_select_at	snackbar_messages	Please select at least one issue		Text from snackbar_messages: Please select at least one issue	
snackbar_failed_to_upload	snackbar_messages	Failed to upload images		Text from snackbar_messages: Failed to upload images	
snackbar_error_updating_issue	snackbar_messages	Error updating issue: $e		Text from snackbar_messages: Error updating issue: $e	
snackbar_no_images_available	snackbar_messages	No images available		Text from snackbar_messages: No images available	
snackbar_failed_to_fetch	snackbar_messages	Failed to fetch complaints		Text from snackbar_messages: Failed to fetch complaints	
snackbar_error_fetching_trains	snackbar_messages	Error fetching trains: $e		Text from snackbar_messages: Error fetching trains: $e	
snackbar_complaint_updated	snackbar_messages	Complaint updated		Text from snackbar_messages: Complaint updated	
snackbar_update_failed	snackbar_messages	Update failed		Text from snackbar_messages: Update failed	
snackbar_complaint_deleted_successfully	snackbar_messages	Complaint deleted successfully		Text from snackbar_messages: Complaint deleted successfully	
snackbar_failed_to_delete	snackbar_messages	Failed to delete complaint		Text from snackbar_messages: Failed to delete complaint	
snackbar_failed_to_submit	snackbar_messages	Failed to submit complaint		Text from snackbar_messages: Failed to submit complaint	
snackbar_error_e	snackbar_messages	Error: $e		Text from snackbar_messages: Error: $e	
snackbar_issues_saved_upload	snackbar_messages	Issues saved upload Images/Videos		Text from snackbar_messages: Issues saved upload Images/Videos	
snackbar_feedback_deleted_successfully	snackbar_messages	Feedback deleted successfully		Text from snackbar_messages: Feedback deleted successfully	
snackbar_error_deleting_feedback	snackbar_messages	Error deleting feedback: $e		Text from snackbar_messages: Error deleting feedback: $e	
snackbar_job_chart_status	snackbar_messages	Job Chart Status Added		Text from snackbar_messages: Job Chart Status Added	
snackbar_no_image_url	snackbar_messages	No image URL provided		Text from snackbar_messages: No image URL provided	
snackbar_image_downloaded_successfully	snackbar_messages	Image downloaded successfully!		Text from snackbar_messages: Image downloaded successfully!	
snackbar_failed_to_download	snackbar_messages	Failed to download image		Text from snackbar_messages: Failed to download image	
snackbar_failed_to_download_1	snackbar_messages	Failed to download image: $error		Text from snackbar_messages: Failed to download image: $error	
snackbar_otp_sent_successfully	snackbar_messages	OTP sent successfully!		Text from snackbar_messages: OTP sent successfully!	
snackbar_failed_to_send	snackbar_messages	Failed to send OTP: $e		Text from snackbar_messages: Failed to send OTP: $e	
snackbar_please_enter_the	snackbar_messages	Please enter the OTP		Text from snackbar_messages: Please enter the OTP	
snackbar_email_saved_successfully	snackbar_messages	Email saved successfully!		Text from snackbar_messages: Email saved successfully!	
snackbar_failed_to_verify	snackbar_messages	Failed to verify OTP: $e		Text from snackbar_messages: Failed to verify OTP: $e	
snackbar_jobchart_deleted_successfully	snackbar_messages	JobChart deleted successfully		Text from snackbar_messages: JobChart deleted successfully	
snackbar_failed_to_delete_1	snackbar_messages	Failed to delete JobChart		Text from snackbar_messages: Failed to delete JobChart	
snackbar_failed_to_load	snackbar_messages	Failed to load train numbers: $e		Text from snackbar_messages: Failed to load train numbers: $e	
