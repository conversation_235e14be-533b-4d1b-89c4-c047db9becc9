{"project_info": {"project_number": "513557807469", "project_id": "railwaysapp-prod", "storage_bucket": "railwaysapp-prod.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:513557807469:android:0f06856533db70111e5a64", "android_client_info": {"package_name": "com.biputri.railops"}}, "oauth_client": [{"client_id": "513557807469-2j3ukikfe7leuqr6u5vhrac47vjtj9lg.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.biputri.railops", "certificate_hash": "c815262a09cb733b2390b75c96fd5f0d5a6fc058"}}, {"client_id": "513557807469-tk3lpasolj8kumr05s39e53725trkp5i.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCEjC5KzouydftXlurNrxYMnJJEzAHDaYY"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "513557807469-tk3lpasolj8kumr05s39e53725trkp5i.apps.googleusercontent.com", "client_type": 3}, {"client_id": "513557807469-k91bmql2hqr2034kvf2dep4vh0afqj9t.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.biputri.railops"}}]}}}], "configuration_version": "1"}