pluginManagement {
    def flutterSdkPath = {
        def properties = new Properties()
        def flutterSdkFile = file("local.properties")
        assert flutterSdkFile.exists(), "local.properties file is missing"
        flutterSdkFile.withInputStream { properties.load(it) }
        def sdkPath = properties.getProperty("flutter.sdk")
        assert sdkPath != null, "flutter.sdk is not set in local.properties"
        return sdkPath
    }()

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.android.application" version "8.7.0" apply false
    // START: FlutterFire Configuration
    id "com.google.gms.google-services" version "4.3.15" apply false
    // END: FlutterFire Configuration
    id "org.jetbrains.kotlin.android" version "2.2.0" apply false
}

include ":app"
