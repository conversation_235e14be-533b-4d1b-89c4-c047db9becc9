{"summary": {"total_files_scanned": 38, "total_strings_found": 573, "unique_strings_by_category": {"text_widgets": 40, "app_bar_titles": 0, "form_labels": 24, "button_labels": 11, "general_strings": 233}, "strings_by_category": {"text_widgets": 54, "app_bar_titles": 0, "form_labels": 30, "button_labels": 13, "general_strings": 476}, "extraction_timestamp": "2025-07-07T23:57:47.184200"}, "detailed_results": {"lib/screens/user_screen/form/signup_form.dart": {"text_widgets": [{"text": "Verified", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 577, "category": "text_widgets", "suggested_key": "text_verified"}, {"text": "Request For Sign Up", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 800, "category": "text_widgets", "suggested_key": "text_request_for_sign"}, {"text": "Information", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 810, "category": "text_widgets", "suggested_key": "text_information"}, {"text": "Please complete all the fields in order. Each field will be enabled only after the previous field is properly filled and validated.", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 811, "category": "text_widgets", "suggested_key": "text_please_complete_all"}, {"text": "Already have an account? login", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 858, "category": "text_widgets", "suggested_key": "text_already_have_an"}, {"text": "I don't have an email", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 437, "category": "text_widgets", "suggested_key": "text_i_dont_have"}], "app_bar_titles": [], "form_labels": [{"text": "First Name *", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 355, "category": "form_labels", "suggested_key": "form_first_name"}, {"text": "Enter your first name", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 356, "category": "form_labels", "suggested_key": "form_enter_your_first"}, {"text": "Middle Name (Optional)", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 370, "category": "form_labels", "suggested_key": "form_middle_name_optional"}, {"text": "Enter your middle name", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 371, "category": "form_labels", "suggested_key": "form_enter_your_middle"}, {"text": "Last Name *", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 382, "category": "form_labels", "suggested_key": "form_last_name"}, {"text": "Enter your last name", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 383, "category": "form_labels", "suggested_key": "form_enter_your_last"}, {"text": "Secondary Phone Number (Optional)", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 490, "category": "form_labels", "suggested_key": "form_secondary_phone_number"}, {"text": "Enter 10-digit secondary phone number", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 491, "category": "form_labels", "suggested_key": "form_enter_10digit_secondary"}, {"text": "WhatsApp Number", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 598, "category": "form_labels", "suggested_key": "form_whatsapp_number"}, {"text": "Enter 10-digit WhatsApp number", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 599, "category": "form_labels", "suggested_key": "form_enter_10digit_whatsapp"}], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:railops/routes.dart", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagerailopsroutesdart"}, {"text": "package:railops/screens/user_screen/widgets/index.dart", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packagerailopsscreensuserscreenwidgetsindexdart"}, {"text": "package:railops/screens/user_screen/widgets/signup_page/signup_error.dart", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 4, "category": "general_strings", "suggested_key": "str_packagerailopsscreensuserscreenwidgetssignuppagesignuperrordart"}, {"text": "package:railops/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 5, "category": "general_strings", "suggested_key": "str_packagerailopsscreensuserscreenwidgetssignuppagezonedropdowndart"}, {"text": "package:railops/services/authentication_services/auth_service.dart", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 6, "category": "general_strings", "suggested_key": "str_packagerailopsservicesauthenticationservicesauthservicedart"}, {"text": "package:railops/services/train_services/train_service_signup.dart", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 7, "category": "general_strings", "suggested_key": "str_packagerailopsservicestrainservicestrainservicesignupdart"}, {"text": "package:railops/types/train_types/zone_division_type.dart", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 8, "category": "general_strings", "suggested_key": "str_packagerailopstypestraintypeszonedivisiontypedart"}, {"text": "package:railops/widgets/index.dart", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 9, "category": "general_strings", "suggested_key": "str_packagerailopswidgetsindexdart"}, {"text": "package:railops/screens/add_user/widget/new_user_mobile_field.dart", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 10, "category": "general_strings", "suggested_key": "str_packagerailopsscreensadduserwidgetnewusermobilefielddart"}, {"text": "package:flutter/services.dart", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 11, "category": "general_strings", "suggested_key": "str_packageflutterservicesdart"}, {"text": "coach attendent", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 36, "category": "general_strings", "suggested_key": "str_coach_attendent"}, {"text": "passenger", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 77, "category": "general_strings", "suggested_key": "str_passenger"}, {"text": "^[0-9]+$", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 152, "category": "general_strings", "suggested_key": "str_09"}, {"text": "Secondary phone number must be exactly 10 digits", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 209, "category": "general_strings", "suggested_key": "str_secondary_phone_number"}, {"text": "^[0-9]+$", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 212, "category": "general_strings", "suggested_key": "str_09"}, {"text": "Please enter only numbers", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 213, "category": "general_strings", "suggested_key": "str_please_enter_only"}, {"text": "Phone number and Secondary phone number must be different", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 217, "category": "general_strings", "suggested_key": "str_phone_number_and"}, {"text": "mounted", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 231, "category": "general_strings", "suggested_key": "str_mounted"}, {"text": "Widget disposed before operation completes", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 232, "category": "general_strings", "suggested_key": "str_widget_disposed_before"}, {"text": "Get Depot Failed : $e", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 234, "category": "general_strings", "suggested_key": "str_get_depot_failed"}, {"text": "trains", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 243, "category": "general_strings", "suggested_key": "str_trains"}, {"text": "emp_numbers", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 244, "category": "general_strings", "suggested_key": "str_empnumbers"}, {"text": "mounted", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 248, "category": "general_strings", "suggested_key": "str_mounted"}, {"text": "Widget disposed before operation completes", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 249, "category": "general_strings", "suggested_key": "str_widget_disposed_before"}, {"text": "Get Train List Failed : $e", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 251, "category": "general_strings", "suggested_key": "str_get_train_list"}, {"text": "mounted", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 265, "category": "general_strings", "suggested_key": "str_mounted"}, {"text": "Get Coach List Failed : $e", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 266, "category": "general_strings", "suggested_key": "str_get_coach_list"}, {"text": "Get Coach List Failed : $e", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 268, "category": "general_strings", "suggested_key": "str_get_coach_list"}, {"text": "First Name *", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 355, "category": "general_strings", "suggested_key": "str_first_name"}, {"text": "Enter your first name", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 356, "category": "general_strings", "suggested_key": "str_enter_your_first"}, {"text": "Please enter your first name", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 361, "category": "general_strings", "suggested_key": "str_please_enter_your"}, {"text": "Middle Name (Optional)", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 370, "category": "general_strings", "suggested_key": "str_middle_name_optional"}, {"text": "Enter your middle name", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 371, "category": "general_strings", "suggested_key": "str_enter_your_middle"}, {"text": "Last Name *", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 382, "category": "general_strings", "suggested_key": "str_last_name"}, {"text": "Enter your last name", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 383, "category": "general_strings", "suggested_key": "str_enter_your_last"}, {"text": "Please enter your last name", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 388, "category": "general_strings", "suggested_key": "str_please_enter_your"}, {"text": "t have email\", don", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 398, "category": "general_strings", "suggested_key": "str_t_have_email"}, {"text": "Secondary Phone Number (Optional)", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 490, "category": "general_strings", "suggested_key": "str_secondary_phone_number"}, {"text": "Enter 10-digit secondary phone number", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 491, "category": "general_strings", "suggested_key": "str_enter_10digit_secondary"}, {"text": "WhatsApp number is same as phone number", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 544, "category": "general_strings", "suggested_key": "str_whatsapp_number_is"}, {"text": "Use same number for WhatsApp?", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 545, "category": "general_strings", "suggested_key": "str_use_same_number"}, {"text": "Verified", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 578, "category": "general_strings", "suggested_key": "str_verified"}, {"text": "WhatsApp Number", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 598, "category": "general_strings", "suggested_key": "str_whatsapp_number"}, {"text": "Enter 10-digit WhatsApp number", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 599, "category": "general_strings", "suggested_key": "str_enter_10digit_whatsapp"}, {"text": "Please enter WhatsApp number", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 605, "category": "general_strings", "suggested_key": "str_please_enter_whatsapp"}, {"text": "WhatsApp number must be exactly 10 digits", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 608, "category": "general_strings", "suggested_key": "str_whatsapp_number_must"}, {"text": "^[0-9]+$", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 610, "category": "general_strings", "suggested_key": "str_09"}, {"text": "Please enter only numbers", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 611, "category": "general_strings", "suggested_key": "str_please_enter_only"}, {"text": "^[0-9]+$", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 623, "category": "general_strings", "suggested_key": "str_09"}, {"text": "Request For Sign Up", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 800, "category": "general_strings", "suggested_key": "str_request_for_sign"}, {"text": "Information", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 810, "category": "general_strings", "suggested_key": "str_information"}, {"text": "Please complete all the fields in order. Each field will be enabled only after the previous field is properly filled and validated.", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 812, "category": "general_strings", "suggested_key": "str_please_complete_all"}, {"text": "Already have an account? login", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 859, "category": "general_strings", "suggested_key": "str_already_have_an"}, {"text": "Error", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 230, "category": "general_strings", "suggested_key": "str_error"}, {"text": "Error", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 247, "category": "general_strings", "suggested_key": "str_error"}, {"text": "Error", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 264, "category": "general_strings", "suggested_key": "str_error"}, {"text": "Error fetching divisions: $e", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 291, "category": "general_strings", "suggested_key": "str_error_fetching_divisions"}, {"text": "Submitting Data. Please Wait!", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 302, "category": "general_strings", "suggested_key": "str_submitting_data_please"}, {"text": "Success", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 329, "category": "general_strings", "suggested_key": "str_success"}, {"text": "Error", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 335, "category": "general_strings", "suggested_key": "str_error"}, {"text": "Please complete all required fields correctly", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 344, "category": "general_strings", "suggested_key": "str_please_complete_all"}, {"text": "Form Incomplete", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 345, "category": "general_strings", "suggested_key": "str_form_incomplete"}, {"text": "I don't have email", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 398, "category": "general_strings", "suggested_key": "str_i_dont_have"}, {"text": "I don't have an email", "file": "lib/screens/user_screen/form/signup_form.dart", "line_number": 437, "category": "general_strings", "suggested_key": "str_i_dont_have"}]}, "lib/screens/user_screen/form/forgot_password_form.dart": {"text_widgets": [{"text": "Close", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 61, "category": "text_widgets", "suggested_key": "text_close"}, {"text": "Send Verification Mail", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 126, "category": "text_widgets", "suggested_key": "text_send_verification_mail"}, {"text": "Forgotten your password? Enter your email address below, and we'll email instructions for setting a new one.", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 89, "category": "text_widgets", "suggested_key": "text_forgotten_your_password"}], "app_bar_titles": [], "form_labels": [{"text": "Email", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 98, "category": "form_labels", "suggested_key": "form_email"}], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:railops/routes.dart", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagerailopsroutesdart"}, {"text": "package:railops/services/otp_services/index.dart", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packagerailopsservicesotpservicesindexdart"}, {"text": "forgot_password_otp", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 14, "category": "general_strings", "suggested_key": "str_forgotpasswordotp"}, {"text": "Password reset email sent successfully", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 42, "category": "general_strings", "suggested_key": "str_password_reset_email"}, {"text": "Success", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 43, "category": "general_strings", "suggested_key": "str_success"}, {"text": "Unexpected null value.", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 45, "category": "general_strings", "suggested_key": "str_unexpected_null_value"}, {"text": "Error", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 46, "category": "general_strings", "suggested_key": "str_error"}, {"text": "Enter a valid email address", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 47, "category": "general_strings", "suggested_key": "str_enter_a_valid"}, {"text": "Error", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 49, "category": "general_strings", "suggested_key": "str_error"}, {"text": "Close", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 61, "category": "general_strings", "suggested_key": "str_close"}, {"text": "Email", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 98, "category": "general_strings", "suggested_key": "str_email"}, {"text": "Send Verification Mail", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 127, "category": "general_strings", "suggested_key": "str_send_verification_mail"}, {"text": "Forgotten your password? Enter your email address below, and we'll email instructions for setting a new one.", "file": "lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 90, "category": "general_strings", "suggested_key": "str_forgotten_your_password"}]}, "lib/screens/user_screen/form/login_form.dart": {"text_widgets": [{"text": "Forgot Password", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 113, "category": "text_widgets", "suggested_key": "text_forgot_password"}, {"text": "Log in", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 130, "category": "text_widgets", "suggested_key": "text_log_in"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:provider/provider.dart", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packageproviderproviderdart"}, {"text": "package:railops/models/index.dart", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packagerailopsmodelsindexdart"}, {"text": "package:railops/routes.dart", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 4, "category": "general_strings", "suggested_key": "str_packagerailopsroutesdart"}, {"text": "package:railops/screens/user_screen/auth_provider.dart", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 5, "category": "general_strings", "suggested_key": "str_packagerailopsscreensuserscreenauthproviderdart"}, {"text": "package:railops/screens/user_screen/widgets/index.dart", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 6, "category": "general_strings", "suggested_key": "str_packagerailopsscreensuserscreenwidgetsindexdart"}, {"text": "package:railops/services/authentication_services/auth_service.dart", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 7, "category": "general_strings", "suggested_key": "str_packagerailopsservicesauthenticationservicesauthservicedart"}, {"text": "package:railops/widgets/index.dart", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 8, "category": "general_strings", "suggested_key": "str_packagerailopswidgetsindexdart"}, {"text": "package:railops/utils/permission_handler_service.dart", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 9, "category": "general_strings", "suggested_key": "str_packagerailopsutilspermissionhandlerservicedart"}, {"text": "package:railops/utils/show_location_permission_disclosure.dart", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 10, "category": "general_strings", "suggested_key": "str_packagerailopsutilsshowlocationpermissiondisclosuredart"}, {"text": "package:shared_preferences/shared_preferences.dart", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 11, "category": "general_strings", "suggested_key": "str_packagesharedpreferencessharedpreferencesdart"}, {"text": "Location permission is required to use this app.", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 43, "category": "general_strings", "suggested_key": "str_location_permission_is"}, {"text": "mounted", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 77, "category": "general_strings", "suggested_key": "str_mounted"}, {"text": "Forgot Password", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 113, "category": "general_strings", "suggested_key": "str_forgot_password"}, {"text": "Log in", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 131, "category": "general_strings", "suggested_key": "str_log_in"}, {"text": "Permission Denied", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 43, "category": "general_strings", "suggested_key": "str_permission_denied"}, {"text": "Logging in... Please wait.", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 56, "category": "general_strings", "suggested_key": "str_logging_in_please"}, {"text": "Error", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 78, "category": "general_strings", "suggested_key": "str_error"}, {"text": "Sign in with Google", "file": "lib/screens/user_screen/form/login_form.dart", "line_number": 187, "category": "general_strings", "suggested_key": "str_sign_in_with"}]}, "lib/screens/user_screen/form/otp_form.dart": {"text_widgets": [{"text": "Error", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 211, "category": "text_widgets", "suggested_key": "text_error"}, {"text": "Close", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 215, "category": "text_widgets", "suggested_key": "text_close"}, {"text": "Resend ($_start)", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 309, "category": "text_widgets", "suggested_key": "text_resend_start"}, {"text": "Resend", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 319, "category": "text_widgets", "suggested_key": "text_resend"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "general_strings": [{"text": "dart:async", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_dartasync"}, {"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:flutter/services.dart", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packageflutterservicesdart"}, {"text": "package:provider/provider.dart", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 4, "category": "general_strings", "suggested_key": "str_packageproviderproviderdart"}, {"text": "package:railops/models/index.dart", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 5, "category": "general_strings", "suggested_key": "str_packagerailopsmodelsindexdart"}, {"text": "package:railops/routes.dart", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 6, "category": "general_strings", "suggested_key": "str_packagerailopsroutesdart"}, {"text": "package:railops/screens/user_screen/widgets/index.dart", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 7, "category": "general_strings", "suggested_key": "str_packagerailopsscreensuserscreenwidgetsindexdart"}, {"text": "package:railops/services/authentication_services/auth_service.dart", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 8, "category": "general_strings", "suggested_key": "str_packagerailopsservicesauthenticationservicesauthservicedart"}, {"text": "package:railops/services/otp_services/index.dart", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 9, "category": "general_strings", "suggested_key": "str_packagerailopsservicesotpservicesindexdart"}, {"text": "package:railops/widgets/success_modal.dart", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 10, "category": "general_strings", "suggested_key": "str_packagerailopswidgetssuccessmodaldart"}, {"text": "phone_number", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 196, "category": "general_strings", "suggested_key": "str_phonenumber"}, {"text": "OTP resent successfully", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 200, "category": "general_strings", "suggested_key": "str_otp_resent_successfully"}, {"text": "Failed to resend OTP: $e", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 202, "category": "general_strings", "suggested_key": "str_failed_to_resend"}, {"text": "Error", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 211, "category": "general_strings", "suggested_key": "str_error"}, {"text": "Close", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 215, "category": "general_strings", "suggested_key": "str_close"}, {"text": "mounted", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 247, "category": "general_strings", "suggested_key": "str_mounted"}, {"text": "Widget disposed before operation completes", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 248, "category": "general_strings", "suggested_key": "str_widget_disposed_before"}, {"text": "Login Error: $e", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 250, "category": "general_strings", "suggested_key": "str_login_error_e"}, {"text": "Resend ($_start)", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 310, "category": "general_strings", "suggested_key": "str_resend_start"}, {"text": "Resend", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 320, "category": "general_strings", "suggested_key": "str_resend"}, {"text": "Success", "file": "lib/screens/user_screen/form/otp_form.dart", "line_number": 200, "category": "general_strings", "suggested_key": "str_success"}]}, "lib/screens/user_screen/auth_provider.dart": {"text_widgets": [{"text": "Login Successful", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 118, "category": "text_widgets", "suggested_key": "text_login_successful"}, {"text": "Invalid PIN", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 122, "category": "text_widgets", "suggested_key": "text_invalid_pin"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "general_strings": [{"text": "dart:convert", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_dartconvert"}, {"text": "package:firebase_auth/firebase_auth.dart", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packagefirebaseauthfirebaseauthdart"}, {"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 4, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:google_sign_in/google_sign_in.dart", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 5, "category": "general_strings", "suggested_key": "str_packagegooglesigningooglesignindart"}, {"text": "package:local_auth/local_auth.dart", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 6, "category": "general_strings", "suggested_key": "str_packagelocalauthlocalauthdart"}, {"text": "package:railops/routes.dart", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 7, "category": "general_strings", "suggested_key": "str_packagerailopsroutesdart"}, {"text": "package:railops/services/authentication_services/auth_service.dart", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 8, "category": "general_strings", "suggested_key": "str_packagerailopsservicesauthenticationservicesauthservicedart"}, {"text": "package:railops/types/profile_types/profile_response.dart", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 9, "category": "general_strings", "suggested_key": "str_packagerailopstypesprofiletypesprofileresponsedart"}, {"text": "package:shared_preferences/shared_preferences.dart", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 10, "category": "general_strings", "suggested_key": "str_packagesharedpreferencessharedpreferencesdart"}, {"text": "use_fingerprint", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 37, "category": "general_strings", "suggested_key": "str_usefingerprint"}, {"text": "use_fingerprint", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 44, "category": "general_strings", "suggested_key": "str_usefingerprint"}, {"text": "user_name", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 87, "category": "general_strings", "suggested_key": "str_username"}, {"text": "Login Successful", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 118, "category": "general_strings", "suggested_key": "str_login_successful"}, {"text": "Invalid PIN", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 122, "category": "general_strings", "suggested_key": "str_invalid_pin"}, {"text": "remember_me", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 137, "category": "general_strings", "suggested_key": "str_rememberme"}, {"text": "user_name", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 170, "category": "general_strings", "suggested_key": "str_username"}, {"text": "Google sign-in cancelled", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 66, "category": "general_strings", "suggested_key": "str_google_signin_cancelled"}, {"text": "User", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 84, "category": "general_strings", "suggested_key": "str_user"}, {"text": "Signed in as $displayName", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 94, "category": "general_strings", "suggested_key": "str_signed_in_as"}, {"text": "Google sign-in failed", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 99, "category": "general_strings", "suggested_key": "str_google_signin_failed"}, {"text": "Google sign-in error: ${e.toString()}", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 104, "category": "general_strings", "suggested_key": "str_google_signin_error"}, {"text": "Google sign-in cancelled", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 149, "category": "general_strings", "suggested_key": "str_google_signin_cancelled"}, {"text": "User", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 167, "category": "general_strings", "suggested_key": "str_user"}, {"text": "Signed in as $displayName", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 177, "category": "general_strings", "suggested_key": "str_signed_in_as"}, {"text": "Google sign-in failed", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 182, "category": "general_strings", "suggested_key": "str_google_signin_failed"}, {"text": "Google sign-in error: ${e.toString()}", "file": "lib/screens/user_screen/auth_provider.dart", "line_number": 187, "category": "general_strings", "suggested_key": "str_google_signin_error"}]}, "lib/screens/user_screen/sign_up_screen.dart": {"text_widgets": [{"text": "Sign Up to RailOps", "file": "lib/screens/user_screen/sign_up_screen.dart", "line_number": 52, "category": "text_widgets", "suggested_key": "text_sign_up_to"}, {"text": "v$appVersion", "file": "lib/screens/user_screen/sign_up_screen.dart", "line_number": 87, "category": "text_widgets", "suggested_key": "text_vappversion"}], "app_bar_titles": [], "form_labels": [], "button_labels": [{"text": "Sign Up to RailOps", "file": "lib/screens/user_screen/sign_up_screen.dart", "line_number": 52, "category": "button_labels", "suggested_key": "btn_sign_up_to"}, {"text": "v$appVersion", "file": "lib/screens/user_screen/sign_up_screen.dart", "line_number": 87, "category": "button_labels", "suggested_key": "btn_vappversion"}], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/sign_up_screen.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:package_info_plus/package_info_plus.dart", "file": "lib/screens/user_screen/sign_up_screen.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagepackageinfopluspackageinfoplusdart"}, {"text": "package:provider/provider.dart", "file": "lib/screens/user_screen/sign_up_screen.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packageproviderproviderdart"}, {"text": "package:railops/models/index.dart", "file": "lib/screens/user_screen/sign_up_screen.dart", "line_number": 4, "category": "general_strings", "suggested_key": "str_packagerailopsmodelsindexdart"}, {"text": "package:railops/routes.dart", "file": "lib/screens/user_screen/sign_up_screen.dart", "line_number": 5, "category": "general_strings", "suggested_key": "str_packagerailopsroutesdart"}, {"text": "package:railops/screens/user_screen/form/login_form.dart", "file": "lib/screens/user_screen/sign_up_screen.dart", "line_number": 6, "category": "general_strings", "suggested_key": "str_packagerailopsscreensuserscreenformloginformdart"}, {"text": "package:railops/screens/user_screen/form/signup_form.dart", "file": "lib/screens/user_screen/sign_up_screen.dart", "line_number": 7, "category": "general_strings", "suggested_key": "str_packagerailopsscreensuserscreenformsignupformdart"}, {"text": "Sign Up to RailOps", "file": "lib/screens/user_screen/sign_up_screen.dart", "line_number": 53, "category": "general_strings", "suggested_key": "str_sign_up_to"}, {"text": "v$appVersion", "file": "lib/screens/user_screen/sign_up_screen.dart", "line_number": 88, "category": "general_strings", "suggested_key": "str_vappversion"}]}, "lib/screens/user_screen/login_screen.dart": {"text_widgets": [{"text": "Welcome to RailOps", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 92, "category": "text_widgets", "suggested_key": "text_welcome_to_railops"}, {"text": "v$appVersion", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 127, "category": "text_widgets", "suggested_key": "text_vappversion"}], "app_bar_titles": [], "form_labels": [], "button_labels": [{"text": "Welcome to RailOps", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 92, "category": "button_labels", "suggested_key": "btn_welcome_to_railops"}, {"text": "v$appVersion", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 127, "category": "button_labels", "suggested_key": "btn_vappversion"}], "general_strings": [{"text": "dart:convert", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_dartconvert"}, {"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:flutter/services.dart", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 4, "category": "general_strings", "suggested_key": "str_packageflutterservicesdart"}, {"text": "package:local_auth/local_auth.dart", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 5, "category": "general_strings", "suggested_key": "str_packagelocalauthlocalauthdart"}, {"text": "package:package_info_plus/package_info_plus.dart", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 6, "category": "general_strings", "suggested_key": "str_packagepackageinfopluspackageinfoplusdart"}, {"text": "package:provider/provider.dart", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 7, "category": "general_strings", "suggested_key": "str_packageproviderproviderdart"}, {"text": "package:railops/models/index.dart", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 8, "category": "general_strings", "suggested_key": "str_packagerailopsmodelsindexdart"}, {"text": "package:railops/routes.dart", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 9, "category": "general_strings", "suggested_key": "str_packagerailopsroutesdart"}, {"text": "package:railops/screens/user_screen/auth_provider.dart", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 10, "category": "general_strings", "suggested_key": "str_packagerailopsscreensuserscreenauthproviderdart"}, {"text": "package:railops/screens/user_screen/form/login_form.dart", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 11, "category": "general_strings", "suggested_key": "str_packagerailopsscreensuserscreenformloginformdart"}, {"text": "package:shared_preferences/shared_preferences.dart", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 12, "category": "general_strings", "suggested_key": "str_packagesharedpreferencessharedpreferencesdart"}, {"text": "use_fingerprint", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 30, "category": "general_strings", "suggested_key": "str_usefingerprint"}, {"text": "loginResponse", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 31, "category": "general_strings", "suggested_key": "str_loginresponse"}, {"text": "Login using fingerprint", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 49, "category": "general_strings", "suggested_key": "str_login_using_fingerprint"}, {"text": "⚠️ Biometric auth error: ${e.message}", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 59, "category": "general_strings", "suggested_key": "str_biometric_auth_error"}, {"text": "❌ Biometrics not supported or not set up.", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 63, "category": "general_strings", "suggested_key": "str_biometrics_not_supported"}, {"text": "Welcome to RailOps", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 93, "category": "general_strings", "suggested_key": "str_welcome_to_railops"}, {"text": "v$appVersion", "file": "lib/screens/user_screen/login_screen.dart", "line_number": 128, "category": "general_strings", "suggested_key": "str_vappversion"}]}, "lib/screens/user_screen/forgot_password_screen.dart": {"text_widgets": [{"text": "Forgotten Password", "file": "lib/screens/user_screen/forgot_password_screen.dart", "line_number": 11, "category": "text_widgets", "suggested_key": "text_forgotten_password"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/forgot_password_screen.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "form/forgot_password_form.dart", "file": "lib/screens/user_screen/forgot_password_screen.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_formforgotpasswordformdart"}, {"text": "Forgotten Password", "file": "lib/screens/user_screen/forgot_password_screen.dart", "line_number": 12, "category": "general_strings", "suggested_key": "str_forgotten_password"}]}, "lib/screens/user_screen/login_mobile_screen.dart": {"text_widgets": [{"text": "Error", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 83, "category": "text_widgets", "suggested_key": "text_error"}, {"text": "Close", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 87, "category": "text_widgets", "suggested_key": "text_close"}, {"text": "Mobile OTP Login", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 114, "category": "text_widgets", "suggested_key": "text_mobile_otp_login"}, {"text": "v$appVersion", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 139, "category": "text_widgets", "suggested_key": "text_vappversion"}, {"text": "Please enter your phone number", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 168, "category": "text_widgets", "suggested_key": "text_please_enter_your"}, {"text": "<PERSON><PERSON>", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 159, "category": "text_widgets", "suggested_key": "text_login"}], "app_bar_titles": [], "form_labels": [{"text": "Mobile Number", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 184, "category": "form_labels", "suggested_key": "form_mobile_number"}, {"text": "Enter your 10-digit mobile number only", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 185, "category": "form_labels", "suggested_key": "form_enter_your_10digit"}], "button_labels": [{"text": "Mobile OTP Login", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 114, "category": "button_labels", "suggested_key": "btn_mobile_otp_login"}], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:flutter/services.dart", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packageflutterservicesdart"}, {"text": "package:package_info_plus/package_info_plus.dart", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packagepackageinfopluspackageinfoplusdart"}, {"text": "package:railops/routes.dart", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 4, "category": "general_strings", "suggested_key": "str_packagerailopsroutesdart"}, {"text": "package:railops/screens/user_screen/widgets/index.dart", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 5, "category": "general_strings", "suggested_key": "str_packagerailopsscreensuserscreenwidgetsindexdart"}, {"text": "package:railops/services/otp_services/index.dart", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 6, "category": "general_strings", "suggested_key": "str_packagerailopsservicesotpservicesindexdart"}, {"text": "phone_number", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 57, "category": "general_strings", "suggested_key": "str_phonenumber"}, {"text": "mounted", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 65, "category": "general_strings", "suggested_key": "str_mounted"}, {"text": "Widget disposed before operation completes", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 66, "category": "general_strings", "suggested_key": "str_widget_disposed_before"}, {"text": "Send Otp Failed: $e", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 68, "category": "general_strings", "suggested_key": "str_send_otp_failed"}, {"text": "Error", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 83, "category": "general_strings", "suggested_key": "str_error"}, {"text": "Close", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 87, "category": "general_strings", "suggested_key": "str_close"}, {"text": "Mobile OTP Login", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 115, "category": "general_strings", "suggested_key": "str_mobile_otp_login"}, {"text": "v$appVersion", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 140, "category": "general_strings", "suggested_key": "str_vappversion"}, {"text": "Please enter your phone number", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 169, "category": "general_strings", "suggested_key": "str_please_enter_your"}, {"text": "Mobile Number", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 184, "category": "general_strings", "suggested_key": "str_mobile_number"}, {"text": "Enter your 10-digit mobile number only", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 185, "category": "general_strings", "suggested_key": "str_enter_your_10digit"}, {"text": "<PERSON><PERSON>", "file": "lib/screens/user_screen/login_mobile_screen.dart", "line_number": 160, "category": "general_strings", "suggested_key": "str_login"}]}, "lib/screens/user_screen/widgets/index.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [], "button_labels": [], "general_strings": [{"text": "login_page/mobile_number_field.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_loginpagemobilenumberfielddart"}, {"text": "login_page/password_field.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_loginpagepasswordfielddart"}, {"text": "signup_page/first_name_field.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_signuppagefirstnamefielddart"}, {"text": "signup_page/middle_name_field.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 4, "category": "general_strings", "suggested_key": "str_signuppagemiddlenamefielddart"}, {"text": "signup_page/last_name_field.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 5, "category": "general_strings", "suggested_key": "str_signuppagelastnamefielddart"}, {"text": "signup_page/email_field.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 6, "category": "general_strings", "suggested_key": "str_signuppageemailfielddart"}, {"text": "signup_page/roles_dropdown.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 7, "category": "general_strings", "suggested_key": "str_signuppagerolesdropdowndart"}, {"text": "signup_page/division_dropdown.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 8, "category": "general_strings", "suggested_key": "str_signuppagedivisiondropdowndart"}, {"text": "login_page/google_login_button.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 9, "category": "general_strings", "suggested_key": "str_loginpagegoogleloginbuttondart"}, {"text": "login_page/mobile_login_button.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 10, "category": "general_strings", "suggested_key": "str_loginpagemobileloginbuttondart"}, {"text": "login_page/signup_button.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 11, "category": "general_strings", "suggested_key": "str_loginpagesignupbuttondart"}, {"text": "login_page/privacy_policies.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 12, "category": "general_strings", "suggested_key": "str_loginpageprivacypoliciesdart"}, {"text": "signup_page/depot_dropdown.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 13, "category": "general_strings", "suggested_key": "str_signuppagedepotdropdowndart"}, {"text": "signup_page/train_number_dropdown.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 14, "category": "general_strings", "suggested_key": "str_signuppagetrainnumberdropdowndart"}, {"text": "signup_page/coach_dropdown.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 15, "category": "general_strings", "suggested_key": "str_signuppagecoachdropdowndart"}, {"text": "otp/otp_boxes.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 16, "category": "general_strings", "suggested_key": "str_otpotpboxesdart"}, {"text": "otp/verify_button.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 17, "category": "general_strings", "suggested_key": "str_otpverifybuttondart"}, {"text": "otp/send_otp_button.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 18, "category": "general_strings", "suggested_key": "str_otpsendotpbuttondart"}, {"text": "signup_page/virified_tag.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 19, "category": "general_strings", "suggested_key": "str_signuppagevirifiedtagdart"}, {"text": "signup_page/mobile_field_signup.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 20, "category": "general_strings", "suggested_key": "str_signuppagemobilefieldsignupdart"}, {"text": "signup_page/re_password.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 21, "category": "general_strings", "suggested_key": "str_signuppagerepassworddart"}, {"text": "mobile_login/mobile_field.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 22, "category": "general_strings", "suggested_key": "str_mobileloginmobilefielddart"}, {"text": "mobile_login/otp_boxes.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 23, "category": "general_strings", "suggested_key": "str_mobileloginotpboxesdart"}, {"text": "mobile_login/otp_data.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 24, "category": "general_strings", "suggested_key": "str_mobileloginotpdatadart"}, {"text": "signup_page/emp_number_field.dart", "file": "lib/screens/user_screen/widgets/index.dart", "line_number": 25, "category": "general_strings", "suggested_key": "str_signuppageempnumberfielddart"}]}, "lib/screens/user_screen/widgets/otp/send_otp_button.dart": {"text_widgets": [{"text": "Send OTP", "file": "lib/screens/user_screen/widgets/otp/send_otp_button.dart", "line_number": 42, "category": "text_widgets", "suggested_key": "text_send_otp"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/otp/send_otp_button.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:pinput/pinput.dart", "file": "lib/screens/user_screen/widgets/otp/send_otp_button.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagepinputpinputdart"}, {"text": "phone", "file": "lib/screens/user_screen/widgets/otp/send_otp_button.dart", "line_number": 35, "category": "general_strings", "suggested_key": "str_phone"}, {"text": "email", "file": "lib/screens/user_screen/widgets/otp/send_otp_button.dart", "line_number": 37, "category": "general_strings", "suggested_key": "str_email"}, {"text": "Send OTP", "file": "lib/screens/user_screen/widgets/otp/send_otp_button.dart", "line_number": 42, "category": "general_strings", "suggested_key": "str_send_otp"}]}, "lib/screens/user_screen/widgets/otp/otp_boxes.dart": {"text_widgets": [{"text": "Re-enter OTP", "file": "lib/screens/user_screen/widgets/otp/otp_boxes.dart", "line_number": 86, "category": "text_widgets", "suggested_key": "text_reenter_otp"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/otp/otp_boxes.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:flutter/services.dart", "file": "lib/screens/user_screen/widgets/otp/otp_boxes.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packageflutterservicesdart"}, {"text": "package:pinput/pinput.dart", "file": "lib/screens/user_screen/widgets/otp/otp_boxes.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packagepinputpinputdart"}, {"text": "Re-enter OTP", "file": "lib/screens/user_screen/widgets/otp/otp_boxes.dart", "line_number": 86, "category": "general_strings", "suggested_key": "str_reenter_otp"}]}, "lib/screens/user_screen/widgets/otp/verify_button.dart": {"text_widgets": [{"text": "Verify OTP", "file": "lib/screens/user_screen/widgets/otp/verify_button.dart", "line_number": 41, "category": "text_widgets", "suggested_key": "text_verify_otp"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/otp/verify_button.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "phone", "file": "lib/screens/user_screen/widgets/otp/verify_button.dart", "line_number": 34, "category": "general_strings", "suggested_key": "str_phone"}, {"text": "email", "file": "lib/screens/user_screen/widgets/otp/verify_button.dart", "line_number": 36, "category": "general_strings", "suggested_key": "str_email"}, {"text": "Verify OTP", "file": "lib/screens/user_screen/widgets/otp/verify_button.dart", "line_number": 41, "category": "general_strings", "suggested_key": "str_verify_otp"}]}, "lib/screens/user_screen/widgets/mobile_login/mobile_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Mobile Number", "file": "lib/screens/user_screen/widgets/mobile_login/mobile_field.dart", "line_number": 21, "category": "form_labels", "suggested_key": "form_mobile_number"}], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/mobile_login/mobile_field.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "Please enter your mobile number", "file": "lib/screens/user_screen/widgets/mobile_login/mobile_field.dart", "line_number": 13, "category": "general_strings", "suggested_key": "str_please_enter_your"}, {"text": "Mobile Number", "file": "lib/screens/user_screen/widgets/mobile_login/mobile_field.dart", "line_number": 21, "category": "general_strings", "suggested_key": "str_mobile_number"}]}, "lib/screens/user_screen/widgets/mobile_login/otp_boxes.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/mobile_login/otp_boxes.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:flutter/services.dart", "file": "lib/screens/user_screen/widgets/mobile_login/otp_boxes.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packageflutterservicesdart"}, {"text": "Please Enter The Otp Digit", "file": "lib/screens/user_screen/widgets/mobile_login/otp_boxes.dart", "line_number": 18, "category": "general_strings", "suggested_key": "str_please_enter_the"}]}, "lib/screens/user_screen/widgets/mobile_login/otp_data.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/mobile_login/otp_data.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:pinput/pinput.dart", "file": "lib/screens/user_screen/widgets/mobile_login/otp_data.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagepinputpinputdart"}]}, "lib/screens/user_screen/widgets/login_page/google_login_button.dart": {"text_widgets": [{"text": "Sign in with Google", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 76, "category": "text_widgets", "suggested_key": "text_sign_in_with"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:google_sign_in/google_sign_in.dart", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagegooglesigningooglesignindart"}, {"text": "package:provider/provider.dart", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packageproviderproviderdart"}, {"text": "package:railops/models/index.dart", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 4, "category": "general_strings", "suggested_key": "str_packagerailopsmodelsindexdart"}, {"text": "package:railops/routes.dart", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 5, "category": "general_strings", "suggested_key": "str_packagerailopsroutesdart"}, {"text": "package:railops/services/authentication_services/auth_service.dart", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 6, "category": "general_strings", "suggested_key": "str_packagerailopsservicesauthenticationservicesauthservicedart"}, {"text": "package:railops/widgets/index.dart", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 7, "category": "general_strings", "suggested_key": "str_packagerailopswidgetsindexdart"}, {"text": "673131287683-5jjd1052v51h3de340oo2g7ampsbln2b.apps.googleusercontent.com", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 13, "category": "general_strings", "suggested_key": "str_6731312876835jjd1052v51h3de340oo2g7ampsbln2bappsgoogleusercontentcom"}, {"text": "673131287683-5jjd1052v51h3de340oo2g7ampsbln2b.apps.googleusercontent.com", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 16, "category": "general_strings", "suggested_key": "str_6731312876835jjd1052v51h3de340oo2g7ampsbln2bappsgoogleusercontentcom"}, {"text": "email", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 18, "category": "general_strings", "suggested_key": "str_email"}, {"text": "mounted", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 45, "category": "general_strings", "suggested_key": "str_mounted"}, {"text": "Widget disposed before operation completes", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 46, "category": "general_strings", "suggested_key": "str_widget_disposed_before"}, {"text": "$error", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 54, "category": "general_strings", "suggested_key": "str_error"}, {"text": "Sign in with Google", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 76, "category": "general_strings", "suggested_key": "str_sign_in_with"}, {"text": "Error", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 48, "category": "general_strings", "suggested_key": "str_error"}, {"text": "Error", "file": "lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 54, "category": "general_strings", "suggested_key": "str_error"}]}, "lib/screens/user_screen/widgets/login_page/password_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Password *", "file": "lib/screens/user_screen/widgets/login_page/password_field.dart", "line_number": 25, "category": "form_labels", "suggested_key": "form_password"}], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/login_page/password_field.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "Password *", "file": "lib/screens/user_screen/widgets/login_page/password_field.dart", "line_number": 25, "category": "general_strings", "suggested_key": "str_password"}, {"text": "Please enter your password", "file": "lib/screens/user_screen/widgets/login_page/password_field.dart", "line_number": 59, "category": "general_strings", "suggested_key": "str_please_enter_your"}]}, "lib/screens/user_screen/widgets/login_page/privacy_policies.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [], "button_labels": [], "general_strings": [{"text": "package:flutter/gestures.dart", "file": "lib/screens/user_screen/widgets/login_page/privacy_policies.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttergesturesdart"}, {"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/login_page/privacy_policies.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:url_launcher/url_launcher.dart", "file": "lib/screens/user_screen/widgets/login_page/privacy_policies.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packageur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"text": "Could not launch $urlString", "file": "lib/screens/user_screen/widgets/login_page/privacy_policies.dart", "line_number": 12, "category": "general_strings", "suggested_key": "str_could_not_launch"}, {"text": "Privacy Policy", "file": "lib/screens/user_screen/widgets/login_page/privacy_policies.dart", "line_number": 30, "category": "general_strings", "suggested_key": "str_privacy_policy"}, {"text": "Term & Condition", "file": "lib/screens/user_screen/widgets/login_page/privacy_policies.dart", "line_number": 51, "category": "general_strings", "suggested_key": "str_term_condition"}, {"text": "Know about this app", "file": "lib/screens/user_screen/widgets/login_page/privacy_policies.dart", "line_number": 71, "category": "general_strings", "suggested_key": "str_know_about_this"}]}, "lib/screens/user_screen/widgets/login_page/mobile_login_button.dart": {"text_widgets": [{"text": "Log in with Mobile Number", "file": "lib/screens/user_screen/widgets/login_page/mobile_login_button.dart", "line_number": 20, "category": "text_widgets", "suggested_key": "text_log_in_with"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/login_page/mobile_login_button.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:railops/routes.dart", "file": "lib/screens/user_screen/widgets/login_page/mobile_login_button.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagerailopsroutesdart"}, {"text": "Log in with Mobile Number", "file": "lib/screens/user_screen/widgets/login_page/mobile_login_button.dart", "line_number": 21, "category": "general_strings", "suggested_key": "str_log_in_with"}]}, "lib/screens/user_screen/widgets/login_page/mobile_number_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Mobile Number", "file": "lib/screens/user_screen/widgets/login_page/mobile_number_field.dart", "line_number": 18, "category": "form_labels", "suggested_key": "form_mobile_number"}], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/login_page/mobile_number_field.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:flutter/services.dart", "file": "lib/screens/user_screen/widgets/login_page/mobile_number_field.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packageflutterservicesdart"}, {"text": "Mobile Number", "file": "lib/screens/user_screen/widgets/login_page/mobile_number_field.dart", "line_number": 18, "category": "general_strings", "suggested_key": "str_mobile_number"}, {"text": "Please enter your mobile number", "file": "lib/screens/user_screen/widgets/login_page/mobile_number_field.dart", "line_number": 48, "category": "general_strings", "suggested_key": "str_please_enter_your"}, {"text": "Mobile number must be 10 digits", "file": "lib/screens/user_screen/widgets/login_page/mobile_number_field.dart", "line_number": 51, "category": "general_strings", "suggested_key": "str_mobile_number_must"}]}, "lib/screens/user_screen/widgets/login_page/signup_button.dart": {"text_widgets": [{"text": "New User? Sign Up Here", "file": "lib/screens/user_screen/widgets/login_page/signup_button.dart", "line_number": 21, "category": "text_widgets", "suggested_key": "text_new_user_sign"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/login_page/signup_button.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:railops/routes.dart", "file": "lib/screens/user_screen/widgets/login_page/signup_button.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagerailopsroutesdart"}, {"text": "New User? Sign Up Here", "file": "lib/screens/user_screen/widgets/login_page/signup_button.dart", "line_number": 21, "category": "general_strings", "suggested_key": "str_new_user_sign"}]}, "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart": {"text_widgets": [{"text": "Select Trains from the list", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 134, "category": "text_widgets", "suggested_key": "text_select_trains_from"}], "app_bar_titles": [], "form_labels": [{"text": "Select Train Numbers", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 117, "category": "form_labels", "suggested_key": "form_select_train_numbers"}], "button_labels": [{"text": "Select Trains from the list", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 134, "category": "button_labels", "suggested_key": "btn_select_trains_from"}], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:dropdown_search/dropdown_search.dart", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagedropdownsearchdropdownsearchdart"}, {"text": "package:multi_dropdown/multi_dropdown.dart", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packagemultidropdownmultidropdowndart"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 60, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 65, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "All", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 94, "category": "general_strings", "suggested_key": "str_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 94, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select Train Numbers", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 117, "category": "general_strings", "suggested_key": "str_select_train_numbers"}, {"text": "Select Trains from the list", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 135, "category": "general_strings", "suggested_key": "str_select_trains_from"}, {"text": "Please select at least one Train", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 149, "category": "general_strings", "suggested_key": "str_please_select_at"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 158, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 167, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 188, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 197, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Train(name: $name, id: $id)", "file": "lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 213, "category": "general_strings", "suggested_key": "str_trainname_name_id"}]}, "lib/screens/user_screen/widgets/signup_page/first_name_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "First Name *", "file": "lib/screens/user_screen/widgets/signup_page/first_name_field.dart", "line_number": 42, "category": "form_labels", "suggested_key": "form_first_name"}], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/signup_page/first_name_field.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "First Name *", "file": "lib/screens/user_screen/widgets/signup_page/first_name_field.dart", "line_number": 42, "category": "general_strings", "suggested_key": "str_first_name"}, {"text": "Please enter your first name", "file": "lib/screens/user_screen/widgets/signup_page/first_name_field.dart", "line_number": 62, "category": "general_strings", "suggested_key": "str_please_enter_your"}]}, "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart": {"text_widgets": [{"text": "Select Zone", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 136, "category": "text_widgets", "suggested_key": "text_select_zone"}], "app_bar_titles": [], "form_labels": [{"text": "Zone", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 116, "category": "form_labels", "suggested_key": "form_zone"}, {"text": "Zone *", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 117, "category": "form_labels", "suggested_key": "form_zone"}], "button_labels": [{"text": "Select Zone", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 136, "category": "button_labels", "suggested_key": "btn_select_zone"}], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:multi_dropdown/multi_dropdown.dart", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagemultidropdownmultidropdowndart"}, {"text": "package:railops/services/train_services/train_service_signup.dart", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packagerailopsservicestrainservicestrainservicesignupdart"}, {"text": "package:railops/types/train_types/zone_division_type.dart", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 4, "category": "general_strings", "suggested_key": "str_packagerailopstypestraintypeszonedivisiontypedart"}, {"text": "All", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 68, "category": "general_strings", "suggested_key": "str_all"}, {"text": "0', name: ", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 69, "category": "general_strings", "suggested_key": "str_0_name"}, {"text": "Zone", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 116, "category": "general_strings", "suggested_key": "str_zone"}, {"text": "Zone *", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 117, "category": "general_strings", "suggested_key": "str_zone"}, {"text": "Select Zone", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 137, "category": "general_strings", "suggested_key": "str_select_zone"}, {"text": "Please select at least one zone", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 151, "category": "general_strings", "suggested_key": "str_please_select_at"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 160, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 165, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Error fetching zones: $e", "file": "lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 78, "category": "general_strings", "suggested_key": "str_error_fetching_zones"}]}, "lib/screens/user_screen/widgets/signup_page/emp_number_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Employee Id *", "file": "lib/screens/user_screen/widgets/signup_page/emp_number_field.dart", "line_number": 53, "category": "form_labels", "suggested_key": "form_employee_id"}], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/signup_page/emp_number_field.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "Employee Id *", "file": "lib/screens/user_screen/widgets/signup_page/emp_number_field.dart", "line_number": 53, "category": "general_strings", "suggested_key": "str_employee_id"}, {"text": "Employee id is already taken by another user in selected depot", "file": "lib/screens/user_screen/widgets/signup_page/emp_number_field.dart", "line_number": 88, "category": "general_strings", "suggested_key": "str_employee_id_is"}]}, "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart": {"text_widgets": [{"text": "Select depot from the list", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 129, "category": "text_widgets", "suggested_key": "text_select_depot_from"}], "app_bar_titles": [], "form_labels": [{"text": "Depot *", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 111, "category": "form_labels", "suggested_key": "form_depot"}], "button_labels": [{"text": "Select depot from the list", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 129, "category": "button_labels", "suggested_key": "btn_select_depot_from"}], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:multi_dropdown/multi_dropdown.dart", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagemultidropdownmultidropdowndart"}, {"text": "package:railops/services/train_services/index.dart", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packagerailopsservicestrainservicesindexdart"}, {"text": "package:railops/widgets/index.dart", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 4, "category": "general_strings", "suggested_key": "str_packagerailopswidgetsindexdart"}, {"text": "All", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 87, "category": "general_strings", "suggested_key": "str_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 87, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Depot *", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 111, "category": "general_strings", "suggested_key": "str_depot"}, {"text": "Select depot from the list", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 130, "category": "general_strings", "suggested_key": "str_select_depot_from"}, {"text": "Please select at least one depot", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 144, "category": "general_strings", "suggested_key": "str_please_select_at"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 156, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 164, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 184, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 192, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Depot(name: $name, id: $id)", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 208, "category": "general_strings", "suggested_key": "str_depotname_name_id"}, {"text": "Error fetching depots: $e", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 58, "category": "general_strings", "suggested_key": "str_error_fetching_depots"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 155, "category": "general_strings", "suggested_key": "str_select_all"}]}, "lib/screens/user_screen/widgets/signup_page/last_name_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Last Name *", "file": "lib/screens/user_screen/widgets/signup_page/last_name_field.dart", "line_number": 42, "category": "form_labels", "suggested_key": "form_last_name"}], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/signup_page/last_name_field.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "Last Name *", "file": "lib/screens/user_screen/widgets/signup_page/last_name_field.dart", "line_number": 42, "category": "general_strings", "suggested_key": "str_last_name"}, {"text": "Please enter your last name", "file": "lib/screens/user_screen/widgets/signup_page/last_name_field.dart", "line_number": 58, "category": "general_strings", "suggested_key": "str_please_enter_your"}]}, "lib/screens/user_screen/widgets/signup_page/re_password.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Re-enter Password *", "file": "lib/screens/user_screen/widgets/signup_page/re_password.dart", "line_number": 25, "category": "form_labels", "suggested_key": "form_reenter_password"}], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/signup_page/re_password.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "Re-enter Password *", "file": "lib/screens/user_screen/widgets/signup_page/re_password.dart", "line_number": 25, "category": "general_strings", "suggested_key": "str_reenter_password"}, {"text": "Please re-enter your password", "file": "lib/screens/user_screen/widgets/signup_page/re_password.dart", "line_number": 59, "category": "general_strings", "suggested_key": "str_please_reenter_your"}]}, "lib/screens/user_screen/widgets/signup_page/email_field.dart": {"text_widgets": [{"text": "Send OTP", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 149, "category": "text_widgets", "suggested_key": "text_send_otp"}, {"text": "Resend OTP in $_start s", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 153, "category": "text_widgets", "suggested_key": "text_resend_otp_in"}, {"text": "Resend OTP", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 170, "category": "text_widgets", "suggested_key": "text_resend_otp"}, {"text": "Verified", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 182, "category": "text_widgets", "suggested_key": "text_verified"}], "app_bar_titles": [], "form_labels": [{"text": "Email", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 114, "category": "form_labels", "suggested_key": "form_email"}], "button_labels": [], "general_strings": [{"text": "dart:async", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_dartasync"}, {"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:railops/screens/user_screen/widgets/index.dart", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packagerailopsscreensuserscreenwidgetsindexdart"}, {"text": "package:railops/services/otp_services/sign_up_otp.dart", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 4, "category": "general_strings", "suggested_key": "str_packagerailopsservicesotpservicessignupotpdart"}, {"text": "package:railops/widgets/index.dart", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 5, "category": "general_strings", "suggested_key": "str_packagerailopswidgetsindexdart"}, {"text": "mounted", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 47, "category": "general_strings", "suggested_key": "str_mounted"}, {"text": "Widget disposed before operation completes", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 48, "category": "general_strings", "suggested_key": "str_widget_disposed_before"}, {"text": "Send Otp Failed: $e", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 50, "category": "general_strings", "suggested_key": "str_send_otp_failed"}, {"text": "mounted", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 76, "category": "general_strings", "suggested_key": "str_mounted"}, {"text": "Widget disposed before operation completes", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 77, "category": "general_strings", "suggested_key": "str_widget_disposed_before"}, {"text": "Verification Failed: $e", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 79, "category": "general_strings", "suggested_key": "str_verification_failed_e"}, {"text": "Email", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 114, "category": "general_strings", "suggested_key": "str_email"}, {"text": "email", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 142, "category": "general_strings", "suggested_key": "str_email"}, {"text": "Send OTP", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 149, "category": "general_strings", "suggested_key": "str_send_otp"}, {"text": "Resend OTP in $_start s", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 154, "category": "general_strings", "suggested_key": "str_resend_otp_in"}, {"text": "email", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 163, "category": "general_strings", "suggested_key": "str_email"}, {"text": "Resend OTP", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 170, "category": "general_strings", "suggested_key": "str_resend_otp"}, {"text": "Verified", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 183, "category": "general_strings", "suggested_key": "str_verified"}, {"text": "Please enter your email", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 199, "category": "general_strings", "suggested_key": "str_please_enter_your"}, {"text": "email", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 216, "category": "general_strings", "suggested_key": "str_email"}, {"text": "email", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 225, "category": "general_strings", "suggested_key": "str_email"}, {"text": "Sending OTP, Please Wait...", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 27, "category": "general_strings", "suggested_key": "str_sending_otp_please"}, {"text": "Success", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 41, "category": "general_strings", "suggested_key": "str_success"}, {"text": "Error", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 46, "category": "general_strings", "suggested_key": "str_error"}, {"text": "Verifying <PERSON><PERSON>, Please Wait...", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 56, "category": "general_strings", "suggested_key": "str_verifying_otp_please"}, {"text": "Success", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 68, "category": "general_strings", "suggested_key": "str_success"}, {"text": "$e\", ", "file": "lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 75, "category": "general_strings", "suggested_key": "str_e"}]}, "lib/screens/user_screen/widgets/signup_page/virified_tag.dart": {"text_widgets": [{"text": "Verified", "file": "lib/screens/user_screen/widgets/signup_page/virified_tag.dart", "line_number": 27, "category": "text_widgets", "suggested_key": "text_verified"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/signup_page/virified_tag.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "Verified", "file": "lib/screens/user_screen/widgets/signup_page/virified_tag.dart", "line_number": 28, "category": "general_strings", "suggested_key": "str_verified"}]}, "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart": {"text_widgets": [{"text": "Select Divisions from the list", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 130, "category": "text_widgets", "suggested_key": "text_select_divisions_from"}], "app_bar_titles": [], "form_labels": [{"text": "Divisions", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 110, "category": "form_labels", "suggested_key": "form_divisions"}, {"text": "Divisions *", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 111, "category": "form_labels", "suggested_key": "form_divisions"}], "button_labels": [{"text": "Select Divisions from the list", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 130, "category": "button_labels", "suggested_key": "btn_select_divisions_from"}], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:multi_dropdown/multi_dropdown.dart", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagemultidropdownmultidropdowndart"}, {"text": "All", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 85, "category": "general_strings", "suggested_key": "str_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 85, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Divisions", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 110, "category": "general_strings", "suggested_key": "str_divisions"}, {"text": "Divisions *", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 111, "category": "general_strings", "suggested_key": "str_divisions"}, {"text": "Select Divisions from the list", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 131, "category": "general_strings", "suggested_key": "str_select_divisions_from"}, {"text": "Please select at least one division", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 145, "category": "general_strings", "suggested_key": "str_please_select_at"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 154, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 162, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 183, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 193, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Division(name: $name, id: $id)", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 209, "category": "general_strings", "suggested_key": "str_divisionname_name_id"}, {"text": "Error fetching divisions: $e", "file": "lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 56, "category": "general_strings", "suggested_key": "str_error_fetching_divisions"}]}, "lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart": {"text_widgets": [{"text": "Select Coaches from the list", "file": "lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart", "line_number": 99, "category": "text_widgets", "suggested_key": "text_select_coaches_from"}], "app_bar_titles": [], "form_labels": [{"text": "Select Coaches", "file": "lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart", "line_number": 82, "category": "form_labels", "suggested_key": "form_select_coaches"}], "button_labels": [{"text": "Select Coaches from the list", "file": "lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart", "line_number": 99, "category": "button_labels", "suggested_key": "btn_select_coaches_from"}], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:multi_dropdown/multi_dropdown.dart", "file": "lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagemultidropdownmultidropdowndart"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart", "line_number": 58, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart", "line_number": 58, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select Coaches", "file": "lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart", "line_number": 82, "category": "general_strings", "suggested_key": "str_select_coaches"}, {"text": "Select Coaches from the list", "file": "lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart", "line_number": 100, "category": "general_strings", "suggested_key": "str_select_coaches_from"}, {"text": "Please select at least one coach", "file": "lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart", "line_number": 114, "category": "general_strings", "suggested_key": "str_please_select_at"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart", "line_number": 123, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart", "line_number": 132, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart", "line_number": 153, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Select All", "file": "lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart", "line_number": 162, "category": "general_strings", "suggested_key": "str_select_all"}, {"text": "Coach(name: $name, id: $id)", "file": "lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart", "line_number": 178, "category": "general_strings", "suggested_key": "str_coachname_name_id"}]}, "lib/screens/user_screen/widgets/signup_page/roles_dropdown.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/signup_page/roles_dropdown.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "coach attendent", "file": "lib/screens/user_screen/widgets/signup_page/roles_dropdown.dart", "line_number": 16, "category": "general_strings", "suggested_key": "str_coach_attendent"}, {"text": "contractor", "file": "lib/screens/user_screen/widgets/signup_page/roles_dropdown.dart", "line_number": 17, "category": "general_strings", "suggested_key": "str_contractor"}, {"text": "contractor admin", "file": "lib/screens/user_screen/widgets/signup_page/roles_dropdown.dart", "line_number": 18, "category": "general_strings", "suggested_key": "str_contractor_admin"}, {"text": "railway admin", "file": "lib/screens/user_screen/widgets/signup_page/roles_dropdown.dart", "line_number": 21, "category": "general_strings", "suggested_key": "str_railway_admin"}, {"text": "railway officer", "file": "lib/screens/user_screen/widgets/signup_page/roles_dropdown.dart", "line_number": 22, "category": "general_strings", "suggested_key": "str_railway_officer"}, {"text": "s2 admin", "file": "lib/screens/user_screen/widgets/signup_page/roles_dropdown.dart", "line_number": 23, "category": "general_strings", "suggested_key": "str_s2_admin"}, {"text": "war room user", "file": "lib/screens/user_screen/widgets/signup_page/roles_dropdown.dart", "line_number": 24, "category": "general_strings", "suggested_key": "str_war_room_user"}, {"text": "write read", "file": "lib/screens/user_screen/widgets/signup_page/roles_dropdown.dart", "line_number": 25, "category": "general_strings", "suggested_key": "str_write_read"}, {"text": "passenger", "file": "lib/screens/user_screen/widgets/signup_page/roles_dropdown.dart", "line_number": 26, "category": "general_strings", "suggested_key": "str_passenger"}, {"text": "Select Role", "file": "lib/screens/user_screen/widgets/signup_page/roles_dropdown.dart", "line_number": 66, "category": "general_strings", "suggested_key": "str_select_role"}]}, "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart": {"text_widgets": [{"text": "Send OTP", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 165, "category": "text_widgets", "suggested_key": "text_send_otp"}, {"text": "Resend OTP in $_start s", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 170, "category": "text_widgets", "suggested_key": "text_resend_otp_in"}, {"text": "Resend OTP", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 189, "category": "text_widgets", "suggested_key": "text_resend_otp"}, {"text": "Verified", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 202, "category": "text_widgets", "suggested_key": "text_verified"}], "app_bar_titles": [], "form_labels": [{"text": "Enter your 10-digit mobile number only", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 124, "category": "form_labels", "suggested_key": "form_enter_your_10digit"}], "button_labels": [], "general_strings": [{"text": "dart:async", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_dartasync"}, {"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:railops/screens/user_screen/widgets/index.dart", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packagerailopsscreensuserscreenwidgetsindexdart"}, {"text": "package:railops/services/otp_services/sign_up_otp.dart", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 4, "category": "general_strings", "suggested_key": "str_packagerailopsservicesotpservicessignupotpdart"}, {"text": "package:railops/widgets/index.dart", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 5, "category": "general_strings", "suggested_key": "str_packagerailopswidgetsindexdart"}, {"text": "package:flutter/services.dart", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 6, "category": "general_strings", "suggested_key": "str_packageflutterservicesdart"}, {"text": "mounted", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 69, "category": "general_strings", "suggested_key": "str_mounted"}, {"text": "Widget disposed before operation completes", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 70, "category": "general_strings", "suggested_key": "str_widget_disposed_before"}, {"text": "Send Otp Failed: $e", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 72, "category": "general_strings", "suggested_key": "str_send_otp_failed"}, {"text": "mounted", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 97, "category": "general_strings", "suggested_key": "str_mounted"}, {"text": "Widget disposed before operation completes", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 98, "category": "general_strings", "suggested_key": "str_widget_disposed_before"}, {"text": "Verification Failed: $e", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 100, "category": "general_strings", "suggested_key": "str_verification_failed_e"}, {"text": "Mobile Number *", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 123, "category": "general_strings", "suggested_key": "str_mobile_number"}, {"text": "Enter your 10-digit mobile number only", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 124, "category": "general_strings", "suggested_key": "str_enter_your_10digit"}, {"text": "phone", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 155, "category": "general_strings", "suggested_key": "str_phone"}, {"text": "Send OTP", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 165, "category": "general_strings", "suggested_key": "str_send_otp"}, {"text": "Resend OTP in $_start s", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 171, "category": "general_strings", "suggested_key": "str_resend_otp_in"}, {"text": "phone", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 180, "category": "general_strings", "suggested_key": "str_phone"}, {"text": "Resend OTP", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 189, "category": "general_strings", "suggested_key": "str_resend_otp"}, {"text": "Verified", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 203, "category": "general_strings", "suggested_key": "str_verified"}, {"text": "Please enter your mobile number", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 219, "category": "general_strings", "suggested_key": "str_please_enter_your"}, {"text": "phone", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 228, "category": "general_strings", "suggested_key": "str_phone"}, {"text": "phone", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 239, "category": "general_strings", "suggested_key": "str_phone"}, {"text": "Sending OTP, Please Wait...", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 49, "category": "general_strings", "suggested_key": "str_sending_otp_please"}, {"text": "Success", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 63, "category": "general_strings", "suggested_key": "str_success"}, {"text": "Error", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 68, "category": "general_strings", "suggested_key": "str_error"}, {"text": "Verifying <PERSON><PERSON>, Please Wait...", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 78, "category": "general_strings", "suggested_key": "str_verifying_otp_please"}, {"text": "Success", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 89, "category": "general_strings", "suggested_key": "str_success"}, {"text": "$e\", ", "file": "lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 96, "category": "general_strings", "suggested_key": "str_e"}]}, "lib/screens/user_screen/widgets/signup_page/signup_error.dart": {"text_widgets": [{"text": "Request Error", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 57, "category": "text_widgets", "suggested_key": "text_request_error"}, {"text": "Please update the information marked with a red cross and try again.", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 108, "category": "text_widgets", "suggested_key": "text_please_update_the"}], "app_bar_titles": [], "form_labels": [], "button_labels": [{"text": "Please update the information marked with a red cross and try again.", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 108, "category": "button_labels", "suggested_key": "btn_please_update_the"}], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "Request Error", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 58, "category": "general_strings", "suggested_key": "str_request_error"}, {"text": "phone", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 70, "category": "general_strings", "suggested_key": "str_phone"}, {"text": "Phone Number", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 72, "category": "general_strings", "suggested_key": "str_phone_number"}, {"text": "phone", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 73, "category": "general_strings", "suggested_key": "str_phone"}, {"text": "status", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 73, "category": "general_strings", "suggested_key": "str_status"}, {"text": "phone", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 74, "category": "general_strings", "suggested_key": "str_phone"}, {"text": "message", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 74, "category": "general_strings", "suggested_key": "str_message"}, {"text": "phone", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 76, "category": "general_strings", "suggested_key": "str_phone"}, {"text": "whatsapp", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 79, "category": "general_strings", "suggested_key": "str_whatsapp"}, {"text": "WhatsApp Number", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 81, "category": "general_strings", "suggested_key": "str_whatsapp_number"}, {"text": "whatsapp", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 82, "category": "general_strings", "suggested_key": "str_whatsapp"}, {"text": "status", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 82, "category": "general_strings", "suggested_key": "str_status"}, {"text": "whatsapp", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 83, "category": "general_strings", "suggested_key": "str_whatsapp"}, {"text": "message", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 83, "category": "general_strings", "suggested_key": "str_message"}, {"text": "whatsapp", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 85, "category": "general_strings", "suggested_key": "str_whatsapp"}, {"text": "email", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 88, "category": "general_strings", "suggested_key": "str_email"}, {"text": "Email ID", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 90, "category": "general_strings", "suggested_key": "str_email_id"}, {"text": "email", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 91, "category": "general_strings", "suggested_key": "str_email"}, {"text": "status", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 91, "category": "general_strings", "suggested_key": "str_status"}, {"text": "email", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 92, "category": "general_strings", "suggested_key": "str_email"}, {"text": "message", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 92, "category": "general_strings", "suggested_key": "str_message"}, {"text": "email", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 94, "category": "general_strings", "suggested_key": "str_email"}, {"text": "emp_number", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 97, "category": "general_strings", "suggested_key": "str_empnumber"}, {"text": "Emp Number", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 99, "category": "general_strings", "suggested_key": "str_emp_number"}, {"text": "emp_number", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 100, "category": "general_strings", "suggested_key": "str_empnumber"}, {"text": "status", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 100, "category": "general_strings", "suggested_key": "str_status"}, {"text": "emp_number", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 101, "category": "general_strings", "suggested_key": "str_empnumber"}, {"text": "message", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 101, "category": "general_strings", "suggested_key": "str_message"}, {"text": "emp_number", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 103, "category": "general_strings", "suggested_key": "str_empnumber"}, {"text": "Please update the information marked with a red cross and try again.", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 109, "category": "general_strings", "suggested_key": "str_please_update_the"}, {"text": "new", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 128, "category": "general_strings", "suggested_key": "str_new"}, {"text": "new", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 212, "category": "general_strings", "suggested_key": "str_new"}, {"text": "New", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 213, "category": "general_strings", "suggested_key": "str_new"}, {"text": "taken", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 214, "category": "general_strings", "suggested_key": "str_taken"}, {"text": "Already Taken", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 215, "category": "general_strings", "suggested_key": "str_already_taken"}, {"text": "requested", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 216, "category": "general_strings", "suggested_key": "str_requested"}, {"text": "Already Requested", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 217, "category": "general_strings", "suggested_key": "str_already_requested"}, {"text": "Unknown", "file": "lib/screens/user_screen/widgets/signup_page/signup_error.dart", "line_number": 219, "category": "general_strings", "suggested_key": "str_unknown"}]}, "lib/screens/user_screen/widgets/signup_page/middle_name_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Middle Name", "file": "lib/screens/user_screen/widgets/signup_page/middle_name_field.dart", "line_number": 42, "category": "form_labels", "suggested_key": "form_middle_name"}], "button_labels": [], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/widgets/signup_page/middle_name_field.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "Middle Name", "file": "lib/screens/user_screen/widgets/signup_page/middle_name_field.dart", "line_number": 42, "category": "general_strings", "suggested_key": "str_middle_name"}]}, "lib/screens/user_screen/mobile_otp_screen.dart": {"text_widgets": [{"text": "Mobile OTP Login", "file": "lib/screens/user_screen/mobile_otp_screen.dart", "line_number": 42, "category": "text_widgets", "suggested_key": "text_mobile_otp_login"}, {"text": "v$appVersion", "file": "lib/screens/user_screen/mobile_otp_screen.dart", "line_number": 66, "category": "text_widgets", "suggested_key": "text_vappversion"}, {"text": "Please enter the OTP sent to your mobile number: $mobileNumber", "file": "lib/screens/user_screen/mobile_otp_screen.dart", "line_number": 96, "category": "text_widgets", "suggested_key": "text_please_enter_the"}, {"text": "Enter OTP", "file": "lib/screens/user_screen/mobile_otp_screen.dart", "line_number": 85, "category": "text_widgets", "suggested_key": "text_enter_otp"}], "app_bar_titles": [], "form_labels": [], "button_labels": [{"text": "Mobile OTP Login", "file": "lib/screens/user_screen/mobile_otp_screen.dart", "line_number": 42, "category": "button_labels", "suggested_key": "btn_mobile_otp_login"}, {"text": "Enter OTP", "file": "lib/screens/user_screen/mobile_otp_screen.dart", "line_number": 85, "category": "button_labels", "suggested_key": "btn_enter_otp"}], "general_strings": [{"text": "package:flutter/material.dart", "file": "lib/screens/user_screen/mobile_otp_screen.dart", "line_number": 1, "category": "general_strings", "suggested_key": "str_packagefluttermaterialdart"}, {"text": "package:package_info_plus/package_info_plus.dart", "file": "lib/screens/user_screen/mobile_otp_screen.dart", "line_number": 2, "category": "general_strings", "suggested_key": "str_packagepackageinfopluspackageinfoplusdart"}, {"text": "package:railops/screens/user_screen/form/otp_form.dart", "file": "lib/screens/user_screen/mobile_otp_screen.dart", "line_number": 3, "category": "general_strings", "suggested_key": "str_packagerailopsscreensuserscreenformotpformdart"}, {"text": "Mobile OTP Login", "file": "lib/screens/user_screen/mobile_otp_screen.dart", "line_number": 43, "category": "general_strings", "suggested_key": "str_mobile_otp_login"}, {"text": "/mobile-login", "file": "lib/screens/user_screen/mobile_otp_screen.dart", "line_number": 56, "category": "general_strings", "suggested_key": "str_mobilelogin"}, {"text": "v$appVersion", "file": "lib/screens/user_screen/mobile_otp_screen.dart", "line_number": 67, "category": "general_strings", "suggested_key": "str_vappversion"}, {"text": "Please enter the OTP sent to your mobile number: $mobileNumber", "file": "lib/screens/user_screen/mobile_otp_screen.dart", "line_number": 97, "category": "general_strings", "suggested_key": "str_please_enter_the"}, {"text": "Enter OTP", "file": "lib/screens/user_screen/mobile_otp_screen.dart", "line_number": 86, "category": "general_strings", "suggested_key": "str_enter_otp"}]}}}