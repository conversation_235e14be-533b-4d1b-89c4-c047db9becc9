import 'dart:io';
import 'package:csv/csv.dart';

void main() async {
  final file = File('test_translations.csv');
  final content = await file.readAsString();
  print('Raw content:');
  print(repr(content));
  print('\n---\n');
  
  final rows = const CsvToListConverter().convert(content);
  print('Parsed rows: ${rows.length}');
  for (int i = 0; i < rows.length; i++) {
    print('Row $i: ${rows[i]}');
  }
  
  // Test the logic from our script
  if (rows.isEmpty) {
    print('Error: No rows');
    return;
  }
  
  final headers = rows[0].map((cell) => cell.toString().toLowerCase().trim()).toList();
  print('\nHeaders: $headers');
  
  final result = <Map<String, String>>[];
  
  for (int i = 1; i < rows.length; i++) {
    final row = rows[i];
    final rowData = <String, String>{};
    
    for (int j = 0; j < headers.length && j < row.length; j++) {
      final value = row[j]?.toString().trim() ?? '';
      rowData[headers[j]] = value;
    }
    
    final key = rowData['key'] ?? '';
    print('Processing row $i: key="$key"');
    
    if (key.isEmpty || key == '@@locale') {
      print('  Skipping: empty key or @@locale');
      continue;
    }
    
    result.add(rowData);
    print('  Added to result');
  }
  
  print('\nFinal result count: ${result.length}');
  for (final item in result) {
    print('  ${item['key']}: ${item['hi']}');
  }
}

String repr(String s) {
  return s.replaceAll('\n', '\\n').replaceAll('\r', '\\r').replaceAll('\t', '\\t');
}
