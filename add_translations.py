import json
import os

# Define the translations for each language
translations = {
    'pa': {
        'text_train': 'ਰੇਲ',
        'text_coaches': 'ਕੋਚ',
        'text_origin_date': 'ਮੂਲ ਤਾਰੀਖ',
        'text_na': 'ਉਪਲਬਧ ਨਹੀਂ'
    },
    'mr': {
        'text_train': 'रेल्वे',
        'text_coaches': 'कोच',
        'text_origin_date': 'मूळ तारीख',
        'text_na': 'उपलब्ध नाही'
    },
    'kn': {
        'text_train': 'ರೈಲು',
        'text_coaches': 'ಕೋಚ್',
        'text_origin_date': 'ಮೂಲ ದಿನಾಂಕ',
        'text_na': 'ಲಭ್ಯವಿಲ್ಲ'
    },
    'ta': {
        'text_train': 'ரயில்',
        'text_coaches': 'கோச்',
        'text_origin_date': 'மூல தேதி',
        'text_na': 'கிடைக்கவில்லை'
    },
    'te': {
        'text_train': 'రైలు',
        'text_coaches': 'కోచ్',
        'text_origin_date': 'మూల తేదీ',
        'text_na': 'అందుబాటులో లేదు'
    },
    'ml': {
        'text_train': 'റെയിൽ',
        'text_coaches': 'കോച്ച്',
        'text_origin_date': 'മൂല തീയതി',
        'text_na': 'ലഭ്യമല്ല'
    }
}

# Process each language file
for lang_code, trans in translations.items():
    file_path = f'lib/l10n/app_{lang_code}.arb'
    
    if os.path.exists(file_path):
        print(f"Processing {file_path}")
        
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the last entry and add new translations
        if content.strip().endswith('}'):
            # Remove the last closing brace
            content = content.rstrip().rstrip('}').rstrip()
            
            # Add comma if needed
            if not content.endswith(','):
                content += ','
            
            # Add new translations
            for key, value in trans.items():
                content += f'\n  "{key}": "{value}",\n  "@{key}": {{\n    "description": "Table column header for {key.split("_")[-1]}",\n    "context": "add_train_screen"\n  }},'
            
            # Remove the last comma and add closing brace
            content = content.rstrip(',') + '\n}'
            
            # Write back to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"Updated {file_path}")
        else:
            print(f"Skipping {file_path} - unexpected format")
    else:
        print(f"File not found: {file_path}")

print("Done!")
