{"functions": {"source": "functions", "runtime": "nodejs18", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"public": "public", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]}, "emulators": {"functions": {"port": 5001}, "firestore": {"port": 8080}, "hosting": {"port": 5000}, "ui": {"enabled": true}}, "flutter": {"platforms": {"android": {"default": {"projectId": "railwaysapp-prod", "appId": "1:513557807469:android:0f06856533db70111e5a64", "fileOutput": "android/app/google-services.json"}}, "dart": {"lib/firebase_options.dart": {"projectId": "railwaysapp-prod", "configurations": {"android": "1:513557807469:android:0f06856533db70111e5a64", "ios": "1:513557807469:ios:7efc23d0a5be0a591e5a64", "web": "1:513557807469:web:4f88f89be348433a1e5a64"}}}}}}