{"name": "railops-cloud-functions", "version": "1.0.0", "description": "Firebase Cloud Functions for RailOps train location-based notifications", "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "engines": {"node": "18"}, "dependencies": {"axios": "^1.6.0", "firebase-admin": "^12.0.0", "firebase-functions": "^4.0.0"}, "devDependencies": {"@types/jest": "^29.0.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.57.1", "eslint-plugin-import": "^2.25.0", "jest": "^29.0.0", "ts-jest": "^29.0.0", "typescript": "^5.0.0"}, "private": true, "jest": {"preset": "ts-jest", "testEnvironment": "node", "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts"]}}