﻿key,english,context,description,hi,bn,as,pa,mr,kn,ta,te,ml
@@locale,en,metadata,Locale metadata - reference only,,,,,,,,,
text_reenter_otp,Re-enter OTP,text_widgets,Text from text_widgets: Re-enter OTP,,,,,,,,,
text_deny,Deny,text_widgets,Text from text_widgets: Deny,,,,,,,,,
text_enable,Enable,text_widgets,Text from text_widgets: Enable,,,,,,,,,
text_location_access_required,Location Access Required,text_widgets,Text from text_widgets: Location Access Required,,,,,,,,,
text_decline,Decline,text_widgets,Text from text_widgets: Decline,,,,,,,,,
text_accept,Accept,text_widgets,Text from text_widgets: Accept,,,,,,,,,
text_confirm_delete,Confirm Delete,text_widgets,Text from text_widgets: Confirm Delete,,,,,,,,,
text_cancel,Cancel,text_widgets,Text from text_widgets: Cancel,,,,,,,,,
text_delete,Delete,text_widgets,Text from text_widgets: Delete,,,,,,,,,
text_storage_permission_is,Storage permission is required to download files,text_widgets,Text from text_widgets: Storage permission is required to download files,,,,,,,,,
text_please_select_a,Please select a train first,text_widgets,Text from text_widgets: Please select a train first,,,,,,,,,
text_update_required,Update Required,text_widgets,Text from text_widgets: Update Required,,,,,,,,,
text_update_now,Update Now,text_widgets,Text from text_widgets: Update Now,,,,,,,,,
text_please_select_a_1,Please select a train number and date.,text_widgets,Text from text_widgets: Please select a train number and date.,,,,,,,,,
text_all_details_updated,All details updated successfully.,text_widgets,Text from text_widgets: All details updated successfully.,,,,,,,,,
text_failed_to_update,Failed to update details: $e,text_widgets,Text from text_widgets: Failed to update details: $e,,,,,,,,,
text_data_refreshed_successfully,Data refreshed successfully,text_widgets,Text from text_widgets: Data refreshed successfully,,,,,,,,,
text_train_location_saved,Train Location Saved Successfully,text_widgets,Text from text_widgets: Train Location Saved Successfully,,,,,,,,,
text_self,Self,text_widgets,Text from text_widgets: Self,,,,,,,,,
text_other_ca,Other CA,text_widgets,Text from text_widgets: Other CA,,,,,,,,,
text_other_ehkobhs,Other EHK/OBHS,text_widgets,Text from text_widgets: Other EHK/OBHS,,,,,,,,,
text_chart_has_not,Chart has not been prepared for this station,text_widgets,Text from text_widgets: Chart has not been prepared for this station,,,,,,,,,
text_camera,Camera,text_widgets,Text from text_widgets: Camera,,,,,,,,,
text_gallery,Gallery,text_widgets,Text from text_widgets: Gallery,,,,,,,,,
text_getting_location,Getting location...,text_widgets,Text from text_widgets: Getting location...,,,,,,,,,
text_attendance_marked_successfully,Attendance marked successfully!,text_widgets,Text from text_widgets: Attendance marked successfully!,,,,,,,,,
text_error_message,Error: $message,text_widgets,Text from text_widgets: Error: $message,,,,,,,,,
text_failed_to_get,Failed to get location: $e,text_widgets,Text from text_widgets: Failed to get location: $e,,,,,,,,,
text_error_fetching_images,Error fetching images: $e,text_widgets,Text from text_widgets: Error fetching images: $e,,,,,,,,,
text_attendance_already_submitted,Attendance Already Submitted,text_widgets,Text from text_widgets: Attendance Already Submitted,,,,,,,,,
text_back_to_all,Back to all users,text_widgets,Text from text_widgets: Back to all users,,,,,,,,,
text_submit,Submit,text_widgets,Text from text_widgets: Submit,,,,,,,,,
text_upload_status,Upload Status,text_widgets,Text from text_widgets: Upload Status,,,,,,,,,
text_compressing_image,Compressing image,text_widgets,Text from text_widgets: Compressing image,,,,,,,,,
text_upload_entrykeysubstring0_6,"Upload ${entry.key.substring(0, 6)}...",text_widgets,"Text from text_widgets: Upload ${entry.key.substring(0, 6)}...",,,,,,,,,
text_close,Close,text_widgets,Text from text_widgets: Close,,,,,,,,,
text_image_uploading,Image Uploading,text_widgets,Text from text_widgets: Image Uploading,,,,,,,,,
text_no_attendance_found,No attendance found.,text_widgets,Text from text_widgets: No attendance found.,,,,,,,,,
text_error_snapshoterror,Error: ${snapshot.error},text_widgets,Text from text_widgets: Error: ${snapshot.error},,,,,,,,,
text_latitude_entrylatitude,latitude: ${entry.latitude},text_widgets,Text from text_widgets: latitude: ${entry.latitude},,,,,,,,,
text_longitude_entrylongitude,longitude: ${entry.longitude},text_widgets,Text from text_widgets: longitude: ${entry.longitude},,,,,,,,,
text_distance_entrydistance_km,Distance: ${entry.distance} km,text_widgets,Text from text_widgets: Distance: ${entry.distance} km,,,,,,,,,
text_updated_by_entryupdatedby,Updated By: ${entry.updatedBy},text_widgets,Text from text_widgets: Updated By: ${entry.updatedBy},,,,,,,,,
text_no_data_available,No data available.,text_widgets,Text from text_widgets: No data available.,,,,,,,,,
text_show_more,Show More,text_widgets,Text from text_widgets: Show More,,,,,,,,,
text_show_less,Show Less,text_widgets,Text from text_widgets: Show Less,,,,,,,,,
text_could_not_open,Could not open file: ${result.message},text_widgets,Text from text_widgets: Could not open file: ${result.message},,,,,,,,,
text_download_started,Download Started!,text_widgets,Text from text_widgets: Download Started!,,,,,,,,,
text_pdf_downloaded_successfully,PDF downloaded successfully to ${path},text_widgets,Text from text_widgets: PDF downloaded successfully to ${path},,,,,,,,,
text_download,Download,text_widgets,Text from text_widgets: Download,,,,,,,,,
text_get_in_email,Get in Email,text_widgets,Text from text_widgets: Get in Email,,,,,,,,,
text_could_not_launch,Could not launch the link,text_widgets,Text from text_widgets: Could not launch the link,,,,,,,,,
text_permission_denied,Permission Denied,text_widgets,Text from text_widgets: Permission Denied,,,,,,,,,
text_requested_users,Requested Users,text_widgets,Text from text_widgets: Requested Users,,,,,,,,,
text_end_date_cannot,End date cannot be before start date,text_widgets,Text from text_widgets: End date cannot be before start date,,,,,,,,,
text_please_select_both,Please select both suspension dates,text_widgets,Text from text_widgets: Please select both suspension dates,,,,,,,,,
text_update_user_details,Update User Details,text_widgets,Text from text_widgets: Update User Details,,,,,,,,,
text_request_for_update,Request For Update User Details,text_widgets,Text from text_widgets: Request For Update User Details,,,,,,,,,
text_i_dont_have,I don't have an email,text_widgets,Text from text_widgets: I don't have an email,,,,,,,,,
text_information,Information,text_widgets,Text from text_widgets: Information,,,,,,,,,
text_request_for_sign,Request For Sign Up,text_widgets,Text from text_widgets: Request For Sign Up,,,,,,,,,
text_forgot_password,Forgot Password,text_widgets,Text from text_widgets: Forgot Password,,,,,,,,,
text_error,Error,text_widgets,Text from text_widgets: Error,,,,,,,,,
text_send_otp,Send OTP,text_widgets,Text from text_widgets: Send OTP,,,,,,,,,
text_verify_otp,Verify OTP,text_widgets,Text from text_widgets: Verify OTP,,,,,,,,,
text_sign_in_with,Sign in with Google,text_widgets,Text from text_widgets: Sign in with Google,,,,,,,,,
text_new_user_sign,New User? Sign Up Here,text_widgets,Text from text_widgets: New User? Sign Up Here,,,,,,,,,
text_resend_otp,Resend OTP,text_widgets,Text from text_widgets: Resend OTP,,,,,,,,,
text_please_enter_a,Please enter a valid 10-digit PNR number,text_widgets,Text from text_widgets: Please enter a valid 10-digit PNR number,,,,,,,,,
text_failed_to_fetch,Failed to fetch PNR data. Please try again.,text_widgets,Text from text_widgets: Failed to fetch PNR data. Please try again.,,,,,,,,,
text_check_pnr_status,Check PNR Status,text_widgets,Text from text_widgets: Check PNR Status,,,,,,,,,
text_pnr_number,PNR Number,text_widgets,Text from text_widgets: PNR Number,,,,,,,,,
text_no_pnr_data,No PNR Data Found,text_widgets,Text from text_widgets: No PNR Data Found,,,,,,,,,
text_please_turn_on,Please turn on location services,text_widgets,Text from text_widgets: Please turn on location services,,,,,,,,,
text_an_error_occurred,An error occurred: $e,text_widgets,Text from text_widgets: An error occurred: $e,,,,,,,,,
text_your_current_location,Your current location,text_widgets,Text from text_widgets: Your current location,,,,,,,,,
text_location_services_disabled,Location Services Disabled,text_widgets,Text from text_widgets: Location Services Disabled,,,,,,,,,
text_please_enable_location,Please enable location services to proceed.,text_widgets,Text from text_widgets: Please enable location services to proceed.,,,,,,,,,
text_location_permission_denied,Location Permission Denied,text_widgets,Text from text_widgets: Location Permission Denied,,,,,,,,,
text_open_settings,Open Settings,text_widgets,Text from text_widgets: Open Settings,,,,,,,,,
text_location_permission_denied_1,Location Permission Denied Forever,text_widgets,Text from text_widgets: Location Permission Denied Forever,,,,,,,,,
text_refresh_failed_e,Refresh failed: ${e},text_widgets,Text from text_widgets: Refresh failed: ${e},,,,,,,,,
text_no_location_data,No location data available.,text_widgets,Text from text_widgets: No location data available.,,,,,,,,,
text_no_data_available_1,No data available,text_widgets,Text from text_widgets: No data available,,,,,,,,,
text_no_train_details,No train details available.,text_widgets,Text from text_widgets: No train details available.,,,,,,,,,
text_none,None,text_widgets,Text from text_widgets: None,,,,,,,,,
text_download_pdf_for,Download PDF for all stations,text_widgets,Text from text_widgets: Download PDF for all stations,,,,,,,,,
text_mail_pdf_for,Mail PDF for all stations,text_widgets,Text from text_widgets: Mail PDF for all stations,,,,,,,,,
text_add_configuration,Add Configuration,text_widgets,Text from text_widgets: Add Configuration,,,,,,,,,
text_select_charting_day,Select Charting Day,text_widgets,Text from text_widgets: Select Charting Day,,,,,,,,,
text_submitting,Submitting...,text_widgets,Text from text_widgets: Submitting...,,,,,,,,,
text_return_gap_updated,Return gap updated successfully,text_widgets,Text from text_widgets: Return gap updated successfully,,,,,,,,,
text_data_not_refreshed,Data not refreshed: $e,text_widgets,Text from text_widgets: Data not refreshed: $e,,,,,,,,,
text_edit_configuration,Edit Configuration,text_widgets,Text from text_widgets: Edit Configuration,,,,,,,,,
text_no_coaches_available,No coaches available,text_widgets,Text from text_widgets: No coaches available,,,,,,,,,
text_invalid_response_format,Invalid response format from server,text_widgets,Text from text_widgets: Invalid response format from server,,,,,,,,,
text_please_select_a_2,Please select a train and date first,text_widgets,Text from text_widgets: Please select a train and date first,,,,,,,,,
text_coach_handover_report,Coach Handover Report,text_widgets,Text from text_widgets: Coach Handover Report,,,,,,,,,
text_save_selection,Save Selection,text_widgets,Text from text_widgets: Save Selection,,,,,,,,,
text_select_media_type,Select Media Type,text_widgets,Text from text_widgets: Select Media Type,,,,,,,,,
text_image,Image,text_widgets,Text from text_widgets: Image,,,,,,,,,
text_video,Video,text_widgets,Text from text_widgets: Video,,,,,,,,,
text_please_select_images,Please select images to upload,text_widgets,Text from text_widgets: Please select images to upload,,,,,,,,,
text_please_select_at,Please select at least one issue,text_widgets,Text from text_widgets: Please select at least one issue,,,,,,,,,
text_failed_to_upload,Failed to upload images,text_widgets,Text from text_widgets: Failed to upload images,,,,,,,,,
text_error_updating_issue,Error updating issue: $e,text_widgets,Text from text_widgets: Error updating issue: $e,,,,,,,,,
text_statustype_by,$statusType By,text_widgets,Text from text_widgets: $statusType By,,,,,,,,,
text_no_images_available,No images available,text_widgets,Text from text_widgets: No images available,,,,,,,,,
text_issuesubissue,Issue/Subissue,text_widgets,Text from text_widgets: Issue/Subissue,,,,,,,,,
text_status,Status,text_widgets,Text from text_widgets: Status,,,,,,,,,
text_submitupdate,Submit/Update,text_widgets,Text from text_widgets: Submit/Update,,,,,,,,,
text_reportedby,Reported_by,text_widgets,Text from text_widgets: Reported_by,,,,,,,,,
text_fixedby,Fixed_by,text_widgets,Text from text_widgets: Fixed_by,,,,,,,,,
text_resolvedby,Resolved_by,text_widgets,Text from text_widgets: Resolved_by,,,,,,,,,
text_pick_images,Pick Images,text_widgets,Text from text_widgets: Pick Images,,,,,,,,,
text_uploading,Uploading...,text_widgets,Text from text_widgets: Uploading...,,,,,,,,,
text_submit_upload,Submit & Upload,text_widgets,Text from text_widgets: Submit & Upload,,,,,,,,,
text_retry,Retry,text_widgets,Text from text_widgets: Retry,,,,,,,,,
text_approve,Approve,text_widgets,Text from text_widgets: Approve,,,,,,,,,
text_no_requests_selected,No requests selected,text_widgets,Text from text_widgets: No requests selected,,,,,,,,,
text_are_you_sure,Are you sure you want to approve these users:,text_widgets,Text from text_widgets: Are you sure you want to approve these users:,,,,,,,,,
text_requeststoprocesslength_users_approved,${requestsToProcess.length} users approved successfully,text_widgets,Text from text_widgets: ${requestsToProcess.length} users approved success...,,,,,,,,,
text_confirm_denial,Confirm Denial,text_widgets,Text from text_widgets: Confirm Denial,,,,,,,,,
text_deny_request,Deny Request,text_widgets,Text from text_widgets: Deny Request,,,,,,,,,
text_approve_selected,Approve Selected,text_widgets,Text from text_widgets: Approve Selected,,,,,,,,,
text_approve_all,Approve All,text_widgets,Text from text_widgets: Approve All,,,,,,,,,
text_processing_requests,Processing requests...,text_widgets,Text from text_widgets: Processing requests...,,,,,,,,,
text_clear_search,Clear search,text_widgets,Text from text_widgets: Clear search,,,,,,,,,
text_failed_to_fetch_1,Failed to fetch complaints,text_widgets,Text from text_widgets: Failed to fetch complaints,,,,,,,,,
text_error_fetching_trains,Error fetching trains: $e,text_widgets,Text from text_widgets: Error fetching trains: $e,,,,,,,,,
text_traintrainno_traintrainname,${train.trainNo} - ${train.trainName},text_widgets,Text from text_widgets: ${train.trainNo} - ${train.trainName},,,,,,,,,
text_other,Other,text_widgets,Text from text_widgets: Other,,,,,,,,,
text_existing_images,Existing Images:,text_widgets,Text from text_widgets: Existing Images:,,,,,,,,,
text_delete_image,Delete Image,text_widgets,Text from text_widgets: Delete Image,,,,,,,,,
text_image_deleted,Image deleted,text_widgets,Text from text_widgets: Image deleted,,,,,,,,,
text_newly_selected_images,Newly Selected Images:,text_widgets,Text from text_widgets: Newly Selected Images:,,,,,,,,,
text_add_image,Add Image,text_widgets,Text from text_widgets: Add Image,,,,,,,,,
text_complaint_updated,Complaint updated,text_widgets,Text from text_widgets: Complaint updated,,,,,,,,,
text_update_failed,Update failed,text_widgets,Text from text_widgets: Update failed,,,,,,,,,
text_save_changes,Save Changes,text_widgets,Text from text_widgets: Save Changes,,,,,,,,,
text_delete_complaint,Delete Complaint,text_widgets,Text from text_widgets: Delete Complaint,,,,,,,,,
text_are_you_sure_1,Are you sure you want to delete this complaint?,text_widgets,Text from text_widgets: Are you sure you want to delete this complaint?,,,,,,,,,
text_complaint_deleted_successfully,Complaint deleted successfully,text_widgets,Text from text_widgets: Complaint deleted successfully,,,,,,,,,
text_failed_to_delete,Failed to delete complaint,text_widgets,Text from text_widgets: Failed to delete complaint,,,,,,,,,
text_select_date,Select Date,text_widgets,Text from text_widgets: Select Date,,,,,,,,,
text_no_complaints_found,No complaints found,text_widgets,Text from text_widgets: No complaints found,,,,,,,,,
text_train_no_complainttrainnumber,Train No: ${complaint.trainNumber},text_widgets,Text from text_widgets: Train No: ${complaint.trainNumber},,,,,,,,,
text_date_complaintcomplaindate,Date: ${complaint.complainDate},text_widgets,Text from text_widgets: Date: ${complaint.complainDate},,,,,,,,,
text_pnr_complaintpnrnumber,PNR: ${complaint.pnrNumber},text_widgets,Text from text_widgets: PNR: ${complaint.pnrNumber},,,,,,,,,
text_edit,Edit,text_widgets,Text from text_widgets: Edit,,,,,,,,,
text_success,Success,text_widgets,Text from text_widgets: Success,,,,,,,,,
text_complaint_submitted_successfully,Complaint submitted successfully!,text_widgets,Text from text_widgets: Complaint submitted successfully!,,,,,,,,,
text_failed_to_submit,Failed to submit complaint,text_widgets,Text from text_widgets: Failed to submit complaint,,,,,,,,,
text_error_e,Error: $e,text_widgets,Text from text_widgets: Error: $e,,,,,,,,,
text_ehk_ehkdisplay,EHK: $ehkDisplay,text_widgets,Text from text_widgets: EHK: $ehkDisplay,,,,,,,,,
text_pending,Pending,text_widgets,Text from text_widgets: Pending,,,,,,,,,
text_completed,Completed,text_widgets,Text from text_widgets: Completed,,,,,,,,,
text_upload_imagevideo,Upload Image/Video,text_widgets,Text from text_widgets: Upload Image/Video,,,,,,,,,
text_submit_issue,Submit Issue,text_widgets,Text from text_widgets: Submit Issue,,,,,,,,,
text_validate,Validate,text_widgets,Text from text_widgets: Validate,,,,,,,,,
text_next,Next,text_widgets,Text from text_widgets: Next,,,,,,,,,
text_error_loading_authentication,Error loading authentication state,text_widgets,Text from text_widgets: Error loading authentication state,,,,,,,,,
text_storage_permission_required,Storage Permission Required,text_widgets,Text from text_widgets: Storage Permission Required,,,,,,,,,
text_please_select_a_3,Please select a JSON file to submit.,text_widgets,Text from text_widgets: Please select a JSON file to submit.,,,,,,,,,
text_error_json_file,Error: Json file is not in the correct format,text_widgets,Text from text_widgets: Error: Json file is not in the correct format,,,,,,,,,
text_selection_cleared,Selection cleared.,text_widgets,Text from text_widgets: Selection cleared.,,,,,,,,,
text_upload_json_data,Upload Json Data,text_widgets,Text from text_widgets: Upload Json Data,,,,,,,,,
text_update,Update,text_widgets,Text from text_widgets: Update,,,,,,,,,
text_add_issue,Add Issue,text_widgets,Text from text_widgets: Add Issue,,,,,,,,,
text_no_subissues,No subissues,text_widgets,Text from text_widgets: No subissues,,,,,,,,,
text_select_issue,Select Issue,text_widgets,Text from text_widgets: Select Issue,,,,,,,,,
text_add_subissue,Add Subissue,text_widgets,Text from text_widgets: Add Subissue,,,,,,,,,
text_add_new_item,Add New Item,text_widgets,Text from text_widgets: Add New Item,,,,,,,,,
text_images_or_videos,Images or Videos upload initiated successfully,text_widgets,Text from text_widgets: Images or Videos upload initiated successfully,,,,,,,,,
text_please_select_an,Please select an image and issue to upload,text_widgets,Text from text_widgets: Please select an image and issue to upload,,,,,,,,,
text_issues_saved_upload,Issues saved upload Images/Videos,text_widgets,Text from text_widgets: Issues saved upload Images/Videos,,,,,,,,,
text_select_issues_for,Select Issues for Coach $coach,text_widgets,Text from text_widgets: Select Issues for Coach $coach,,,,,,,,,
text_pick_imagesvideos,Pick Images/Videos,text_widgets,Text from text_widgets: Pick Images/Videos,,,,,,,,,
text_please_select_imagevideo,Please select image/video to upload,text_widgets,Text from text_widgets: Please select image/video to upload,,,,,,,,,
text_confirm_deletion,Confirm Deletion,text_widgets,Text from text_widgets: Confirm Deletion,,,,,,,,,
text_delete_report,Delete Report,text_widgets,Text from text_widgets: Delete Report,,,,,,,,,
text_coach_issue_status,Coach Issue Status,text_widgets,Text from text_widgets: Coach Issue Status,,,,,,,,,
text_both_person_and,Both person and date/time are required.,text_widgets,Text from text_widgets: Both person and date/time are required.,,,,,,,,,
text_select_widgetstatustype_by,Select ${widget.statusType} By & Date,text_widgets,Text from text_widgets: Select ${widget.statusType} By & Date,,,,,,,,,
text_subissue_widgetname,SubIssue : ${widget.name} ,text_widgets,Text from text_widgets: SubIssue : ${widget.name} ,,,,,,,,,
text_issue_widgetname,Issue : ${widget.name} ,text_widgets,Text from text_widgets: Issue : ${widget.name} ,,,,,,,,,
text_confirm,Confirm,text_widgets,Text from text_widgets: Confirm,,,,,,,,,
text_manage_issues,Manage Issues,text_widgets,Text from text_widgets: Manage Issues,,,,,,,,,
text_rake_deficiency_report,Rake Deficiency Report Issues,text_widgets,Text from text_widgets: Rake Deficiency Report Issues,,,,,,,,,
text_upload_pnr_image,Upload PNR Image,text_widgets,Text from text_widgets: Upload PNR Image,,,,,,,,,
text_pick_imagesvideos_for,Pick Images/Videos for Feedback,text_widgets,Text from text_widgets: Pick Images/Videos for Feedback,,,,,,,,,
text_please_wait_until,Please wait until the upload is complete,text_widgets,Text from text_widgets: Please wait until the upload is complete,,,,,,,,,
text_submit_feedback,Submit Feedback,text_widgets,Text from text_widgets: Submit Feedback,,,,,,,,,
text_verify_email,Verify Email,text_widgets,Text from text_widgets: Verify Email,,,,,,,,,
text_check_your_inbox,• Check your inbox first,text_widgets,Text from text_widgets: • Check your inbox first,,,,,,,,,
text_if_not_found,"• If not found, check spam/junk folder",text_widgets,"Text from text_widgets: • If not found, check spam/junk folder",,,,,,,,,
text_add_our_domain,• Add our domain to your safe sender list,text_widgets,Text from text_widgets: • Add our domain to your safe sender list,,,,,,,,,
text_i_understand,I Understand,text_widgets,Text from text_widgets: I Understand,,,,,,,,,
text_nonac,NONAC,text_widgets,Text from text_widgets: NONAC,,,,,,,,,
text_select,Select,text_widgets,Text from text_widgets: Select,,,,,,,,,
text_failed_to_load,Failed to load image,text_widgets,Text from text_widgets: Failed to load image,,,,,,,,,
text_review_feedback,Review Feedback,text_widgets,Text from text_widgets: Review Feedback,,,,,,,,,
text_deleting_feedback,Deleting feedback...,text_widgets,Text from text_widgets: Deleting feedback...,,,,,,,,,
text_feedback_deleted_successfully,Feedback deleted successfully,text_widgets,Text from text_widgets: Feedback deleted successfully,,,,,,,,,
text_error_deleting_feedback,Error deleting feedback: $e,text_widgets,Text from text_widgets: Error deleting feedback: $e,,,,,,,,,
text_no_feedback_available,No feedback available for this train.,text_widgets,Text from text_widgets: No feedback available for this train.,,,,,,,,,
text_train_no_trainnumber,Train No: $trainNumber,text_widgets,Text from text_widgets: Train No: $trainNumber,,,,,,,,,
text_non_ac,Non AC,text_widgets,Text from text_widgets: Non AC,,,,,,,,,
text_message,Message,text_widgets,Text from text_widgets: Message,,,,,,,,,
text_job_chart_status,Job Chart Status Added,text_widgets,Text from text_widgets: Job Chart Status Added,,,,,,,,,
text_please_select_all,Please select all fields before submitting.,text_widgets,Text from text_widgets: Please select all fields before submitting.,,,,,,,,,
text_update_amount_for,Update Amount for $userId,text_widgets,Text from text_widgets: Update Amount for $userId,,,,,,,,,
text_assigned,Assigned,text_widgets,Text from text_widgets: Assigned,,,,,,,,,
text_amount,Amount,text_widgets,Text from text_widgets: Amount,,,,,,,,,
text_no_image_url,No image URL provided,text_widgets,Text from text_widgets: No image URL provided,,,,,,,,,
text_image_downloaded_successfully,Image downloaded successfully!,text_widgets,Text from text_widgets: Image downloaded successfully!,,,,,,,,,
text_failed_to_download,Failed to download image,text_widgets,Text from text_widgets: Failed to download image,,,,,,,,,
text_failed_to_download_1,Failed to download image: $error,text_widgets,Text from text_widgets: Failed to download image: $error,,,,,,,,,
text_image_detail,Image Detail,text_widgets,Text from text_widgets: Image Detail,,,,,,,,,
text_download_image,Download Image,text_widgets,Text from text_widgets: Download Image,,,,,,,,,
text_train_trainnumber_details,Train $trainNumber details deleted successfully,text_widgets,Text from text_widgets: Train $trainNumber details deleted successfully,,,,,,,,,
text_confirm_deactivation,Confirm Deactivation,text_widgets,Text from text_widgets: Confirm Deactivation,,,,,,,,,
text_proceed,Proceed,text_widgets,Text from text_widgets: Proceed,,,,,,,,,
text_add_email,Add Email,text_widgets,Text from text_widgets: Add Email,,,,,,,,,
text_back,Back,text_widgets,Text from text_widgets: Back,,,,,,,,,
text_email_verification,Email Verification,text_widgets,Text from text_widgets: Email Verification,,,,,,,,,
text_phone_verification,Phone Verification,text_widgets,Text from text_widgets: Phone Verification,,,,,,,,,
text_logout_confirmation,Logout Confirmation,text_widgets,Text from text_widgets: Logout Confirmation,,,,,,,,,
text_do_you_want,Do you want to logout now?,text_widgets,Text from text_widgets: Do you want to logout now?,,,,,,,,,
text_addupdate,Add/Update,text_widgets,Text from text_widgets: Add/Update,,,,,,,,,
text_alert,Alert,text_widgets,Text from text_widgets: Alert,,,,,,,,,
text_generate_otp,Generate OTP,text_widgets,Text from text_widgets: Generate OTP,,,,,,,,,
text_otp_sent_successfully,OTP sent successfully!,text_widgets,Text from text_widgets: OTP sent successfully!,,,,,,,,,
text_failed_to_send,Failed to send OTP: $e,text_widgets,Text from text_widgets: Failed to send OTP: $e,,,,,,,,,
text_please_enter_the,Please enter the OTP,text_widgets,Text from text_widgets: Please enter the OTP,,,,,,,,,
text_email_saved_successfully,Email saved successfully!,text_widgets,Text from text_widgets: Email saved successfully!,,,,,,,,,
text_failed_to_verify,Failed to verify OTP: $e,text_widgets,Text from text_widgets: Failed to verify OTP: $e,,,,,,,,,
text_send_mobile_otp,Send Mobile OTP,text_widgets,Text from text_widgets: Send Mobile OTP,,,,,,,,,
text_send_email_otp,Send Email OTP,text_widgets,Text from text_widgets: Send Email OTP,,,,,,,,,
text_uploaded_at_formatteddate,Uploaded at: $formattedDate,text_widgets,Text from text_widgets: Uploaded at: $formattedDate,,,,,,,,,
text_uploaded_by_widgetimageresponsecreatedby,Uploaded by: ${widget.imageResponse.createdBy},text_widgets,Text from text_widgets: Uploaded by: ${widget.imageResponse.createdBy},,,,,,,,,
text_id_widgetimageresponseid,id: ${widget.imageResponse.id},text_widgets,Text from text_widgets: id: ${widget.imageResponse.id},,,,,,,,,
text_coach_widgetimageresponsecoach,Coach: ${widget.imageResponse.coach},text_widgets,Text from text_widgets: Coach: ${widget.imageResponse.coach},,,,,,,,,
text_issue_widgetimageresponseissue,Issue: ${widget.imageResponse.issue},text_widgets,Text from text_widgets: Issue: ${widget.imageResponse.issue},,,,,,,,,
text_delete_confirmation,Delete Confirmation,text_widgets,Text from text_widgets: Delete Confirmation,,,,,,,,,
text_are_you_sure_2,Are you sure you want to delete this image?,text_widgets,Text from text_widgets: Are you sure you want to delete this image?,,,,,,,,,
text_save,Save,text_widgets,Text from text_widgets: Save,,,,,,,,,
text_ehkca,EHK/CA,text_widgets,Text from text_widgets: EHK/CA,,,,,,,,,
text_image_upload_initiated,Image upload initiated successfully,text_widgets,Text from text_widgets: Image upload initiated successfully,,,,,,,,,
text_failed_to_upload_1,Failed to upload image,text_widgets,Text from text_widgets: Failed to upload image,,,,,,,,,
text_please_select_an_1,Please select an image to upload,text_widgets,Text from text_widgets: Please select an image to upload,,,,,,,,,
text_jobchart_deleted_successfully,JobChart deleted successfully,text_widgets,Text from text_widgets: JobChart deleted successfully,,,,,,,,,
text_failed_to_delete_1,Failed to delete JobChart,text_widgets,Text from text_widgets: Failed to delete JobChart,,,,,,,,,
text_pick_image,Pick Image,text_widgets,Text from text_widgets: Pick Image,,,,,,,,,
text_upload_image,Upload Image,text_widgets,Text from text_widgets: Upload Image,,,,,,,,,
text_failed_to_load_1,Failed to load train numbers: $e,text_widgets,Text from text_widgets: Failed to load train numbers: $e,,,,,,,,,
text_language,Language,text_widgets,Text from text_widgets: Language,,,,,,,,,
text_select_language,Select Language,text_widgets,Text from text_widgets: Select Language,,,,,,,,,
text_train_tracker,Train Tracker,text_widgets,Text from text_widgets: Train Tracker,,,,,,,,,
text_assign_ca,Assign CA,text_widgets,Text from text_widgets: Assign CA,,,,,,,,,
text_assign_cs,Assign CS,text_widgets,Text from text_widgets: Assign CS,,,,,,,,,
text_pnr_details,PNR Details,text_widgets,Text from text_widgets: PNR Details,,,,,,,,,
text_rail_sathi,Rail Sathi,text_widgets,Text from text_widgets: Rail Sathi,,,,,,,,,
text_passenger_chart,Passenger Chart,text_widgets,Text from text_widgets: Passenger Chart,,,,,,,,,
text_map_screen,Map Screen,text_widgets,Text from text_widgets: Map Screen,,,,,,,,,
text_configuration,Configuration,text_widgets,Text from text_widgets: Configuration,,,,,,,,,
text_reports,Reports,text_widgets,Text from text_widgets: Reports,,,,,,,,,
text_passenger_feedback,Passenger Feedback,text_widgets,Text from text_widgets: Passenger Feedback,,,,,,,,,
text_rake_deficiency_report_1,Rake Deficiency Report,text_widgets,Text from text_widgets: Rake Deficiency Report,,,,,,,,,
text_obhs_to_mcc,OBHS to MCC Handover,text_widgets,Text from text_widgets: OBHS to MCC Handover,,,,,,,,,
text_mcc_to_obhs,MCC to OBHS Handover,text_widgets,Text from text_widgets: MCC to OBHS Handover,,,,,,,,,
text_upload_data,Upload data,text_widgets,Text from text_widgets: Upload data,,,,,,,,,
text_user_management,User Management,text_widgets,Text from text_widgets: User Management,,,,,,,,,
text_issue_management,Issue Management,text_widgets,Text from text_widgets: Issue Management,,,,,,,,,
text_rail_sathi_qr,Rail Sathi Qr,text_widgets,Text from text_widgets: Rail Sathi Qr,,,,,,,,,
text_customer_care,Customer Care,text_widgets,Text from text_widgets: Customer Care,,,,,,,,,
title_location_access_required,Location Access Required,app_bar_titles,Text from app_bar_titles: Location Access Required,,,,,,,,,
title_upload_status,Upload Status,app_bar_titles,Text from app_bar_titles: Upload Status,,,,,,,,,
title_compressing_image,Compressing image,app_bar_titles,Text from app_bar_titles: Compressing image,,,,,,,,,
title_upload_entrykeysubstring0_6,"Upload ${entry.key.substring(0, 6)}...",app_bar_titles,"Text from app_bar_titles: Upload ${entry.key.substring(0, 6)}...",,,,,,,,,
title_permission_denied,Permission Denied,app_bar_titles,Text from app_bar_titles: Permission Denied,,,,,,,,,
title_confirm_delete,Confirm Delete,app_bar_titles,Text from app_bar_titles: Confirm Delete,,,,,,,,,
title_add_new_item,Add New Item,app_bar_titles,Text from app_bar_titles: Add New Item,,,,,,,,,
title_add_issue,Add Issue,app_bar_titles,Text from app_bar_titles: Add Issue,,,,,,,,,
title_add_subissue,Add Subissue,app_bar_titles,Text from app_bar_titles: Add Subissue,,,,,,,,,
title_select_widgetstatustype_by,Select ${widget.statusType} By & Date,app_bar_titles,Text from app_bar_titles: Select ${widget.statusType} By & Date,,,,,,,,,
title_update_amount_for,Update Amount for $userId,app_bar_titles,Text from app_bar_titles: Update Amount for $userId,,,,,,,,,
form_train_number,Train Number,form_labels,Text from form_labels: Train Number,,,,,,,,,
form_date,Date,form_labels,Text from form_labels: Date,,,,,,,,,
form_select_date_ddmmmyyyy,Select Date (DD-MMM-YYYY),form_labels,Text from form_labels: Select Date (DD-MMM-YYYY),,,,,,,,,
form_first_name,First Name *,form_labels,Text from form_labels: First Name *,,,,,,,,,
form_enter_first_name,Enter first name,form_labels,Text from form_labels: Enter first name,,,,,,,,,
form_middle_name_optional,Middle Name (Optional),form_labels,Text from form_labels: Middle Name (Optional),,,,,,,,,
form_enter_middle_name,Enter middle name,form_labels,Text from form_labels: Enter middle name,,,,,,,,,
form_last_name,Last Name *,form_labels,Text from form_labels: Last Name *,,,,,,,,,
form_enter_last_name,Enter last name,form_labels,Text from form_labels: Enter last name,,,,,,,,,
form_phone_number,Phone Number,form_labels,Text from form_labels: Phone Number,,,,,,,,,
form_secondary_phone_number,Secondary Phone Number (Optional),form_labels,Text from form_labels: Secondary Phone Number (Optional),,,,,,,,,
form_whatsapp_number,WhatsApp Number,form_labels,Text from form_labels: WhatsApp Number,,,,,,,,,
form_enter_10digit_whatsapp,Enter 10-digit WhatsApp number,form_labels,Text from form_labels: Enter 10-digit WhatsApp number,,,,,,,,,
form_email,Email *,form_labels,Text from form_labels: Email *,,,,,,,,,
form_enter_your_email,Enter your email address,form_labels,Text from form_labels: Enter your email address,,,,,,,,,
form_enter_your_first,Enter your first name,form_labels,Text from form_labels: Enter your first name,,,,,,,,,
form_enter_your_middle,Enter your middle name,form_labels,Text from form_labels: Enter your middle name,,,,,,,,,
form_enter_your_last,Enter your last name,form_labels,Text from form_labels: Enter your last name,,,,,,,,,
form_enter_10digit_secondary,Enter 10-digit secondary phone number,form_labels,Text from form_labels: Enter 10-digit secondary phone number,,,,,,,,,
form_email_1,Email,form_labels,Text from form_labels: Email,,,,,,,,,
form_mobile_number,Mobile Number,form_labels,Text from form_labels: Mobile Number,,,,,,,,,
form_enter_your_10digit,Enter your 10-digit mobile number only,form_labels,Text from form_labels: Enter your 10-digit mobile number only,,,,,,,,,
form_password,Password *,form_labels,Text from form_labels: Password *,,,,,,,,,
form_select_train_numbers,Select Train Numbers,form_labels,Text from form_labels: Select Train Numbers,,,,,,,,,
form_zone,Zone,form_labels,Text from form_labels: Zone,,,,,,,,,
form_zone_1,Zone *,form_labels,Text from form_labels: Zone *,,,,,,,,,
form_employee_id,Employee Id *,form_labels,Text from form_labels: Employee Id *,,,,,,,,,
form_depot,Depot *,form_labels,Text from form_labels: Depot *,,,,,,,,,
form_reenter_password,Re-enter Password *,form_labels,Text from form_labels: Re-enter Password *,,,,,,,,,
form_divisions,Divisions,form_labels,Text from form_labels: Divisions,,,,,,,,,
form_divisions_1,Divisions *,form_labels,Text from form_labels: Divisions *,,,,,,,,,
form_select_coaches,Select Coaches,form_labels,Text from form_labels: Select Coaches,,,,,,,,,
form_middle_name,Middle Name,form_labels,Text from form_labels: Middle Name,,,,,,,,,
form_select_train_number,Select Train Number,form_labels,Text from form_labels: Select Train Number,,,,,,,,,
form_train_name,Train Name,form_labels,Text from form_labels: Train Name,,,,,,,,,
form_select_stations,Select Stations,form_labels,Text from form_labels: Select Stations,,,,,,,,,
form_select_date,Select Date,form_labels,Text from form_labels: Select Date,,,,,,,,,
form_whatsapp_number_1,WhatsApp Number *,form_labels,Text from form_labels: WhatsApp Number *,,,,,,,,,
form_enter_secondary_phone,Enter secondary phone number (Optional),form_labels,Text from form_labels: Enter secondary phone number (Optional),,,,,,,,,
form_mobile_number_1,Mobile Number *,form_labels,Text from form_labels: Mobile Number *,,,,,,,,,
form_enter_mobile_number,Enter mobile number to fetch details,form_labels,Text from form_labels: Enter mobile number to fetch details,,,,,,,,,
form_enter_mobile_number_1,Enter Mobile Number,form_labels,Text from form_labels: Enter Mobile Number,,,,,,,,,
form_related_train,Related Train,form_labels,Text from form_labels: Related Train,,,,,,,,,
form_division,Division,form_labels,Text from form_labels: Division,,,,,,,,,
form_depot_1,Depot,form_labels,Text from form_labels: Depot,,,,,,,,,
form_charting_day,Charting Day,form_labels,Text from form_labels: Charting Day,,,,,,,,,
form_from_station,From Station,form_labels,Text from form_labels: From Station,,,,,,,,,
form_to_station,To Station,form_labels,Text from form_labels: To Station,,,,,,,,,
form_direction_updown,Direction (Up/Down),form_labels,Text from form_labels: Direction (Up/Down),,,,,,,,,
form_start_time,Start Time,form_labels,Text from form_labels: Start Time,,,,,,,,,
form_eg_0900_am,e.g. 09:00 AM,form_labels,Text from form_labels: e.g. 09:00 AM,,,,,,,,,
form_end_time,End Time,form_labels,Text from form_labels: End Time,,,,,,,,,
form_eg_0500_pm,e.g. 05:00 PM,form_labels,Text from form_labels: e.g. 05:00 PM,,,,,,,,,
form_charting_time,Charting Time,form_labels,Text from form_labels: Charting Time,,,,,,,,,
form_return_gap_days,Return Gap (Days),form_labels,Text from form_labels: Return Gap (Days),,,,,,,,,
form_inout,In/Out,form_labels,Text from form_labels: In/Out,,,,,,,,,
form_related_train_number,Related Train Number,form_labels,Text from form_labels: Related Train Number,,,,,,,,,
form_updown,Up/Down,form_labels,Text from form_labels: Up/Down,,,,,,,,,
form_train_type,Train Type,form_labels,Text from form_labels: Train Type,,,,,,,,,
form_search_train_number,Search Train Number,form_labels,Text from form_labels: Search Train Number,,,,,,,,,
form_coaches_comma_separated,Coaches (comma separated),form_labels,Text from form_labels: Coaches (comma separated),,,,,,,,,
form_eg_h_gsl,"E.g., H, GSL, A, B, M",form_labels,"Text from form_labels: E.g., H, GSL, A, B, M",,,,,,,,,
form_enter_coach_names,Enter coach names separated by commas,form_labels,Text from form_labels: Enter coach names separated by commas,,,,,,,,,
form_select_days,Select Days,form_labels,Text from form_labels: Select Days,,,,,,,,,
form_add_stoppage,Add Stoppage,form_labels,Text from form_labels: Add Stoppage,,,,,,,,,
form_type_a_station,Type a station name and press Enter or Space,form_labels,Text from form_labels: Type a station name and press Enter or Space,,,,,,,,,
form_search,Search,form_labels,Text from form_labels: Search,,,,,,,,,
form_stoppages_in_sequence,Stoppages in Sequence,form_labels,Text from form_labels: Stoppages in Sequence,,,,,,,,,
form_type_a_station_1,Type a Station and hit space or Enter,form_labels,Text from form_labels: Type a Station and hit space or Enter,,,,,,,,,
form_frequency,Frequency,form_labels,Text from form_labels: Frequency,,,,,,,,,
form_enter_new_coach,Enter new coach,form_labels,Text from form_labels: Enter new coach,,,,,,,,,
form_use_comma_to,Use comma to add multiple coaches,form_labels,Text from form_labels: Use comma to add multiple coaches,,,,,,,,,
form_add_your_comments,Add your comments here (max 250 characters)...,form_labels,Text from form_labels: Add your comments here (max 250 characters)...,,,,,,,,,
form_search_by_name,"Search by name, email, phone or depot",form_labels,"Text from form_labels: Search by name, email, phone or depot",,,,,,,,,
form_train,Train,form_labels,Text from form_labels: Train,,,,,,,,,
form_complaint_type,Complaint Type,form_labels,Text from form_labels: Complaint Type,,,,,,,,,
form_status,Status,form_labels,Text from form_labels: Status,,,,,,,,,
form_write_your_issue,Write your issue,form_labels,Text from form_labels: Write your issue,,,,,,,,,
form_issue_status,Issue Status,form_labels,Text from form_labels: Issue Status,,,,,,,,,
form_name,Name,form_labels,Text from form_labels: Name,,,,,,,,,
form_search_by_train,Search by train number or name,form_labels,Text from form_labels: Search by train number or name,,,,,,,,,
form_train_selection,Train Selection,form_labels,Text from form_labels: Train Selection,,,,,,,,,
form_journey_start_date,Journey Start Date,form_labels,Text from form_labels: Journey Start Date,,,,,,,,,
form_ddmmyyyy,DD/MM/YYYY,form_labels,Text from form_labels: DD/MM/YYYY,,,,,,,,,
form_coach,Coach,form_labels,Text from form_labels: Coach,,,,,,,,,
form_berth,Berth,form_labels,Text from form_labels: Berth,,,,,,,,,
form_issue_name,Issue Name,form_labels,Text from form_labels: Issue Name,,,,,,,,,
form_subissue_name,Subissue Name,form_labels,Text from form_labels: Subissue Name,,,,,,,,,
form_search_1,Search...,form_labels,Text from form_labels: Search...,,,,,,,,,
form_widgetstatustype_by,${widget.statusType} by,form_labels,Text from form_labels: ${widget.statusType} by,,,,,,,,,
form_select_date_time,Select Date & Time,form_labels,Text from form_labels: Select Date & Time,,,,,,,,,
form_add_your_feedback,Add your feedback here...,form_labels,Text from form_labels: Add your feedback here...,,,,,,,,,
form_task_status,Task Status *,form_labels,Text from form_labels: Task Status *,,,,,,,,,
form_search_train_number_1,Search train number,form_labels,Text from form_labels: Search train number,,,,,,,,,
form_train_number_1,Train Number *,form_labels,Text from form_labels: Train Number *,,,,,,,,,
form_pnr_number,PNR Number *,form_labels,Text from form_labels: PNR Number *,,,,,,,,,
form_passenger_name,Passenger Name *,form_labels,Text from form_labels: Passenger Name *,,,,,,,,,
form_coach_no,Coach No *,form_labels,Text from form_labels: Coach No *,,,,,,,,,
form_berth_no,Berth No *,form_labels,Text from form_labels: Berth No *,,,,,,,,,
form_email_id,Email ID,form_labels,Text from form_labels: Email ID,,,,,,,,,
form_enter_otp,Enter OTP,form_labels,Text from form_labels: Enter OTP,,,,,,,,,
form_issue_type,Issue Type,form_labels,Text from form_labels: Issue Type,,,,,,,,,
form_sub_issue_type,Sub Issue Type,form_labels,Text from form_labels: Sub Issue Type,,,,,,,,,
form_resolved_yesno,Resolved (Yes/No) *,form_labels,Text from form_labels: Resolved (Yes/No) *,,,,,,,,,
form_crn_number,CRN Number*,form_labels,Text from form_labels: CRN Number*,,,,,,,,,
form_train_no,Train No *,form_labels,Text from form_labels: Train No *,,,,,,,,,
form_train_name_1,Train Name *,form_labels,Text from form_labels: Train Name *,,,,,,,,,
form_marks_1_to,Marks (1 to 10) *,form_labels,Text from form_labels: Marks (1 to 10) *,,,,,,,,,
form_remarks_by_passenger,Remarks by Passenger,form_labels,Text from form_labels: Remarks by Passenger,,,,,,,,,
form_passenger_name_1,Passenger Name,form_labels,Text from form_labels: Passenger Name,,,,,,,,,
form_pnr_number_1,PNR Number,form_labels,Text from form_labels: PNR Number,,,,,,,,,
form_crn_number_1,CRN Number,form_labels,Text from form_labels: CRN Number,,,,,,,,,
form_coach_no_1,Coach No,form_labels,Text from form_labels: Coach No,,,,,,,,,
form_berth_no_1,Berth No,form_labels,Text from form_labels: Berth No,,,,,,,,,
form_remarks,Remarks,form_labels,Text from form_labels: Remarks,,,,,,,,,
form_task_status_1,Task Status,form_labels,Text from form_labels: Task Status,,,,,,,,,
form_feedback,Feedback,form_labels,Text from form_labels: Feedback,,,,,,,,,
form_amount_in_hand,Amount in Hand (₹),form_labels,Text from form_labels: Amount in Hand (₹),,,,,,,,,
form_select_user,Select User,form_labels,Text from form_labels: Select User,,,,,,,,,
form_enter_email_otp,Enter Email OTP,form_labels,Text from form_labels: Enter Email OTP,,,,,,,,,
form_enter_phone_otp,Enter Phone OTP,form_labels,Text from form_labels: Enter Phone OTP,,,,,,,,,
form_select_coaches_optional,Select Coaches (optional),form_labels,Text from form_labels: Select Coaches (optional),,,,,,,,,
btn_deny,Deny,button_labels,Text from button_labels: Deny,,,,,,,,,
btn_enable,Enable,button_labels,Text from button_labels: Enable,,,,,,,,,
btn_decline,Decline,button_labels,Text from button_labels: Decline,,,,,,,,,
btn_accept,Accept,button_labels,Text from button_labels: Accept,,,,,,,,,
btn_self,Self,button_labels,Text from button_labels: Self,,,,,,,,,
btn_other_ca,Other CA,button_labels,Text from button_labels: Other CA,,,,,,,,,
btn_other_ehkobhs,Other EHK/OBHS,button_labels,Text from button_labels: Other EHK/OBHS,,,,,,,,,
btn_close,Close,button_labels,Text from button_labels: Close,,,,,,,,,
btn_no_attendance_found,No attendance found.,button_labels,Text from button_labels: No attendance found.,,,,,,,,,
btn_error_snapshoterror,Error: ${snapshot.error},button_labels,Text from button_labels: Error: ${snapshot.error},,,,,,,,,
btn_no_data_available,No data available.,button_labels,Text from button_labels: No data available.,,,,,,,,,
btn_your_current_location,Your current location,button_labels,Text from button_labels: Your current location,,,,,,,,,
btn_no_location_data,No location data available.,button_labels,Text from button_labels: No location data available.,,,,,,,,,
btn_no_data_available_1,No data available,button_labels,Text from button_labels: No data available,,,,,,,,,
btn_add_configuration,Add Configuration,button_labels,Text from button_labels: Add Configuration,,,,,,,,,
btn_select_charting_day,Select Charting Day,button_labels,Text from button_labels: Select Charting Day,,,,,,,,,
btn_edit_configuration,Edit Configuration,button_labels,Text from button_labels: Edit Configuration,,,,,,,,,
btn_no_coaches_available,No coaches available,button_labels,Text from button_labels: No coaches available,,,,,,,,,
btn_coach_handover_report,Coach Handover Report,button_labels,Text from button_labels: Coach Handover Report,,,,,,,,,
btn_statustype_by,$statusType By,button_labels,Text from button_labels: $statusType By,,,,,,,,,
btn_traintrainno_traintrainname,${train.trainNo} - ${train.trainName},button_labels,Text from button_labels: ${train.trainNo} - ${train.trainName},,,,,,,,,
btn_other,Other,button_labels,Text from button_labels: Other,,,,,,,,,
btn_no_complaints_found,No complaints found,button_labels,Text from button_labels: No complaints found,,,,,,,,,
btn_pending,Pending,button_labels,Text from button_labels: Pending,,,,,,,,,
btn_completed,Completed,button_labels,Text from button_labels: Completed,,,,,,,,,
btn_upload_json_data,Upload Json Data,button_labels,Text from button_labels: Upload Json Data,,,,,,,,,
btn_cancel,Cancel,button_labels,Text from button_labels: Cancel,,,,,,,,,
btn_delete,Delete,button_labels,Text from button_labels: Delete,,,,,,,,,
btn_save_selection,Save Selection,button_labels,Text from button_labels: Save Selection,,,,,,,,,
btn_coach_issue_status,Coach Issue Status,button_labels,Text from button_labels: Coach Issue Status,,,,,,,,,
btn_rake_deficiency_report,Rake Deficiency Report Issues,button_labels,Text from button_labels: Rake Deficiency Report Issues,,,,,,,,,
btn_no_feedback_available,No feedback available for this train.,button_labels,Text from button_labels: No feedback available for this train.,,,,,,,,,
btn_save,Save,button_labels,Text from button_labels: Save,,,,,,,,,
snackbar_please_select_a,Please select a train first,snackbar_messages,Text from snackbar_messages: Please select a train first,,,,,,,,,
snackbar_data_refreshed_successfully,Data refreshed successfully,snackbar_messages,Text from snackbar_messages: Data refreshed successfully,,,,,,,,,
snackbar_train_location_saved,Train Location Saved Successfully,snackbar_messages,Text from snackbar_messages: Train Location Saved Successfully,,,,,,,,,
snackbar_error_fetching_images,Error fetching images: $e,snackbar_messages,Text from snackbar_messages: Error fetching images: $e,,,,,,,,,
snackbar_download_started,Download Started!,snackbar_messages,Text from snackbar_messages: Download Started!,,,,,,,,,
snackbar_pdf_downloaded_successfully,PDF downloaded successfully to ${path},snackbar_messages,Text from snackbar_messages: PDF downloaded successfully to ${path},,,,,,,,,
snackbar_could_not_launch,Could not launch the link,snackbar_messages,Text from snackbar_messages: Could not launch the link,,,,,,,,,
snackbar_refresh_failed_e,Refresh failed: ${e},snackbar_messages,Text from snackbar_messages: Refresh failed: ${e},,,,,,,,,
snackbar_return_gap_updated,Return gap updated successfully,snackbar_messages,Text from snackbar_messages: Return gap updated successfully,,,,,,,,,
snackbar_data_not_refreshed,Data not refreshed: $e,snackbar_messages,Text from snackbar_messages: Data not refreshed: $e,,,,,,,,,
snackbar_invalid_response_format,Invalid response format from server,snackbar_messages,Text from snackbar_messages: Invalid response format from server,,,,,,,,,
snackbar_please_select_a_1,Please select a train and date first,snackbar_messages,Text from snackbar_messages: Please select a train and date first,,,,,,,,,
snackbar_please_select_images,Please select images to upload,snackbar_messages,Text from snackbar_messages: Please select images to upload,,,,,,,,,
snackbar_please_select_at,Please select at least one issue,snackbar_messages,Text from snackbar_messages: Please select at least one issue,,,,,,,,,
snackbar_failed_to_upload,Failed to upload images,snackbar_messages,Text from snackbar_messages: Failed to upload images,,,,,,,,,
snackbar_error_updating_issue,Error updating issue: $e,snackbar_messages,Text from snackbar_messages: Error updating issue: $e,,,,,,,,,
snackbar_no_images_available,No images available,snackbar_messages,Text from snackbar_messages: No images available,,,,,,,,,
snackbar_failed_to_fetch,Failed to fetch complaints,snackbar_messages,Text from snackbar_messages: Failed to fetch complaints,,,,,,,,,
snackbar_error_fetching_trains,Error fetching trains: $e,snackbar_messages,Text from snackbar_messages: Error fetching trains: $e,,,,,,,,,
snackbar_complaint_updated,Complaint updated,snackbar_messages,Text from snackbar_messages: Complaint updated,,,,,,,,,
snackbar_update_failed,Update failed,snackbar_messages,Text from snackbar_messages: Update failed,,,,,,,,,
snackbar_complaint_deleted_successfully,Complaint deleted successfully,snackbar_messages,Text from snackbar_messages: Complaint deleted successfully,,,,,,,,,
snackbar_failed_to_delete,Failed to delete complaint,snackbar_messages,Text from snackbar_messages: Failed to delete complaint,,,,,,,,,
snackbar_failed_to_submit,Failed to submit complaint,snackbar_messages,Text from snackbar_messages: Failed to submit complaint,,,,,,,,,
snackbar_error_e,Error: $e,snackbar_messages,Text from snackbar_messages: Error: $e,,,,,,,,,
snackbar_issues_saved_upload,Issues saved upload Images/Videos,snackbar_messages,Text from snackbar_messages: Issues saved upload Images/Videos,,,,,,,,,
snackbar_feedback_deleted_successfully,Feedback deleted successfully,snackbar_messages,Text from snackbar_messages: Feedback deleted successfully,,,,,,,,,
snackbar_error_deleting_feedback,Error deleting feedback: $e,snackbar_messages,Text from snackbar_messages: Error deleting feedback: $e,,,,,,,,,
snackbar_job_chart_status,Job Chart Status Added,snackbar_messages,Text from snackbar_messages: Job Chart Status Added,,,,,,,,,
snackbar_no_image_url,No image URL provided,snackbar_messages,Text from snackbar_messages: No image URL provided,,,,,,,,,
snackbar_image_downloaded_successfully,Image downloaded successfully!,snackbar_messages,Text from snackbar_messages: Image downloaded successfully!,,,,,,,,,
snackbar_failed_to_download,Failed to download image,snackbar_messages,Text from snackbar_messages: Failed to download image,,,,,,,,,
snackbar_failed_to_download_1,Failed to download image: $error,snackbar_messages,Text from snackbar_messages: Failed to download image: $error,,,,,,,,,
snackbar_otp_sent_successfully,OTP sent successfully!,snackbar_messages,Text from snackbar_messages: OTP sent successfully!,,,,,,,,,
snackbar_failed_to_send,Failed to send OTP: $e,snackbar_messages,Text from snackbar_messages: Failed to send OTP: $e,,,,,,,,,
snackbar_please_enter_the,Please enter the OTP,snackbar_messages,Text from snackbar_messages: Please enter the OTP,,,,,,,,,
snackbar_email_saved_successfully,Email saved successfully!,snackbar_messages,Text from snackbar_messages: Email saved successfully!,,,,,,,,,
snackbar_failed_to_verify,Failed to verify OTP: $e,snackbar_messages,Text from snackbar_messages: Failed to verify OTP: $e,,,,,,,,,
snackbar_jobchart_deleted_successfully,JobChart deleted successfully,snackbar_messages,Text from snackbar_messages: JobChart deleted successfully,,,,,,,,,
snackbar_failed_to_delete_1,Failed to delete JobChart,snackbar_messages,Text from snackbar_messages: Failed to delete JobChart,,,,,,,,,
snackbar_failed_to_load,Failed to load train numbers: $e,snackbar_messages,Text from snackbar_messages: Failed to load train numbers: $e,,,,,,,,,