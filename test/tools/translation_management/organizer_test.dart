import 'package:flutter_test/flutter_test.dart';
import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as p;

// Load the organizer tool as a library
Future<Map<String, dynamic>> processExtractedARB(String filePath) async {
  // Mimicking what the organizer tool does internally
  final file = File(filePath);
  if (!file.existsSync()) {
    throw Exception('Extracted ARB file not found');
  }

  final content = await file.readAsString();
  final extractedARB = jsonDecode(content) as Map<String, dynamic>;

  // Prioritize and filter strings
  final prioritizedStrings = <String, dynamic>{'@@locale': 'en'};

  for (final key in extractedARB.keys) {
    if (key.startsWith('@@') || key.startsWith('@')) continue; // Skip metadata
    
    final text = extractedARB[key] as String;
    final metadata = extractedARB['@$key'] as Map<String, dynamic>;
    final context = metadata['context'] ?? 'general_strings';

    // Prioritize based on context
    if (['app_bar_titles', 'button_labels', 'text_widgets'].contains(context)) {
      prioritizedStrings[key] = text;
      prioritizedStrings['@$key'] = metadata;
    }
  }

  return prioritizedStrings;
}

void main() {
  group('ARB Organizer Tests', () {
    late Directory testDataDir;
    late String extractedFilePath;

    setUp(() async {
      // Initialize test directory and file path
      final currentDir = Directory.current.path;
      testDataDir = Directory(p.join(currentDir, 'test', 'tools', 'translation_management', 'test_data'));
      extractedFilePath = p.join(testDataDir.path, 'extracted_strings.arb');
    });

    test('Prioritizes and filters UI strings', () async {
      final prioritized = await processExtractedARB(extractedFilePath);
      
      expect(prioritized.containsKey('loginButton'), isTrue);
      expect(prioritized.containsKey('homeTitle'), isTrue);
      expect(prioritized.containsKey('helpText'), isTrue);
      expect(prioritized.containsKey('trainStationName'), isTrue);
      
      // Non-UI strings should be excluded
      expect(prioritized.containsKey('apiEndpoint'), isFalse);
      expect(prioritized.containsKey('technicalDebugMessage'), isFalse);
      expect(prioritized.containsKey('veryLongTechnicalString'), isFalse);
    });

    test('Generates a summary report', () async {
      final prioritized = await processExtractedARB(extractedFilePath);
      
      // Mock merging with existing ARB to generate a report
      final report = StringBuffer()
        ..writeln('Summary Report')
        ..writeln('Original Strings: ${prioritized.length}')
        ..write('Prioritized: ${prioritized.keys.length}');

      expect(report.toString().contains('Prioritized'), isTrue);
    });
  });
}

