{"@@locale": "en", "loginButton": "<PERSON><PERSON>", "@loginButton": {"context": "button_labels", "description": "Login button text"}, "homeTitle": "Home", "@homeTitle": {"context": "app_bar_titles", "description": "Home screen title"}, "userNameField": "User Name", "@userNameField": {"context": "form_labels", "description": "User name input field label"}, "apiEndpoint": "/api/v1/users", "@apiEndpoint": {"context": "general_strings", "description": "API endpoint URL"}, "technicalDebugMessage": "Error in UserAuthenticationServiceImpl.validateCredentials()", "@technicalDebugMessage": {"context": "general_strings", "description": "Technical debug message"}, "helpText": "Need help with your account?", "@helpText": {"context": "text_widgets", "description": "Help link text"}, "veryLongTechnicalString": "This is a very long technical string that contains implementation details and should probably not be localized because it's meant for developers and contains technical jargon that users shouldn't see", "@veryLongTechnicalString": {"context": "general_strings", "description": "Long technical description"}, "trainStationName": "New Delhi Railway Station", "@trainStationName": {"context": "text_widgets", "description": "Railway station name"}, "routePath": "/home/<USER>", "@routePath": {"context": "general_strings", "description": "Navigation route"}, "saveChanges": "Save Changes", "@saveChanges": {"context": "button_labels", "description": "Save button text"}}