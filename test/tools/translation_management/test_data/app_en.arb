{"@@locale": "en", "appTitle": "RailOps", "@appTitle": {"description": "The title of the application"}, "loginButton": "<PERSON><PERSON>", "@loginButton": {"description": "Button text for login action"}, "welcomeMessage": "Welcome back, {name}!", "@welcomeMessage": {"description": "Welcome message with user name placeholder", "placeholders": {"name": {"type": "String", "example": "<PERSON>"}}}, "trainSchedule": "Train Schedule", "@trainSchedule": {"description": "Title for train schedule page"}, "passengerCount": "Passengers: {count}", "@passengerCount": {"description": "Display number of passengers", "placeholders": {"count": {"type": "int", "example": "42"}}}, "multiPlaceholder": "Journey from {from} to {to} on {date}", "@multiPlaceholder": {"description": "Journey details with multiple placeholders", "placeholders": {"from": {"type": "String", "example": "Delhi"}, "to": {"type": "String", "example": "Mumbai"}, "date": {"type": "String", "example": "2024-01-15"}}}}