import 'package:flutter_test/flutter_test.dart';
import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as p;

// Load the validator script as a library
Future<Process> runValidator(String directory, List<String> args) async {
  final toolPath = p.join(Directory.current.path, 'tools', 'translation_management', 'validate_translations.dart');
  return await Process.start('dart', [toolPath, '--directory', directory, ...args]);
}

void main() {
  group('Translation Validator Tests', () {
    late Directory tempDir;
    late Directory testDataDir;

    setUp(() async {
      // Create temporary directory for test ARB files
      tempDir = await Directory.systemTemp.createTemp('arb_validator_test_');
      final currentDir = Directory.current.path;
      testDataDir = Directory(p.join(currentDir, 'test', 'tools', 'translation_management', 'test_data'));
      
      // Copy test data files to temp directory
      final files = await testDataDir.list().where((entity) => entity is File && entity.path.endsWith('.arb')).toList();
      for (final file in files) {
        final fileName = p.basename(file.path);
        await (file as File).copy(p.join(tempDir.path, fileName));
      }
    });

    tearDown(() async {
      // Clean up temporary directory
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    });

    test('Valid translations pass validation', () async {
      final process = await runValidator(tempDir.path, []);
      final exitCode = await process.exitCode;
      // May have warnings due to missing keys in test data, but should not have errors
      expect(exitCode, anyOf(equals(0), equals(1))); // May have warnings from missing keys
    });

    test('Strict mode fails on warnings', () async {
      // Create a file with extra keys (should be warnings)
      final extraKeysFile = File(p.join(tempDir.path, 'app_extra.arb'));
      await extraKeysFile.writeAsString(jsonEncode({
        '@@locale': 'extra',
        'appTitle': 'RailOps',
        'loginButton': 'Login',
        'welcomeMessage': 'Welcome back, {name}!',
        'trainSchedule': 'Train Schedule',
        'passengerCount': 'Passengers: {count}',
        'multiPlaceholder': 'Journey from {from} to {to} on {date}',
        'extraKey': 'This key does not exist in reference'
      }));

      // Run in strict mode
      final process = await runValidator(tempDir.path, ['--strict']);
      final exitCode = await process.exitCode;
      expect(exitCode, equals(1)); // Should fail due to warnings in strict mode
    });

    test('Missing keys cause validation errors', () async {
      final process = await runValidator(tempDir.path, []);
      final stdout = <String>[];
      final stderr = <String>[];
      
      process.stdout.transform(utf8.decoder).transform(LineSplitter()).listen(stdout.add);
      process.stderr.transform(utf8.decoder).transform(LineSplitter()).listen(stderr.add);
      
      final exitCode = await process.exitCode;
      final output = stdout.join('\n');
      
      expect(exitCode, equals(1)); // Should fail validation
      expect(output.contains('Missing translation key'), isTrue);
    });

    test('JSON syntax errors are detected', () async {
      final process = await runValidator(tempDir.path, []);
      final stdout = <String>[];
      
      process.stdout.transform(utf8.decoder).transform(LineSplitter()).listen(stdout.add);
      
      final exitCode = await process.exitCode;
      final output = stdout.join('\n');
      
      expect(exitCode, equals(1)); // Should fail validation
      expect(output.contains('JSON'), isTrue);
    });

    test('Placeholder mismatches are detected', () async {
      final process = await runValidator(tempDir.path, []);
      final stdout = <String>[];
      
      process.stdout.transform(utf8.decoder).transform(LineSplitter()).listen(stdout.add);
      
      final exitCode = await process.exitCode;
      final output = stdout.join('\n');
      
      expect(exitCode, equals(1)); // Should fail validation
      expect(output.contains('placeholder'), isTrue);
    });

    test('Quiet mode runs without errors', () async {
      final process = await runValidator(tempDir.path, ['--quiet']);
      final exitCode = await process.exitCode;
      
      // Just verify it runs - output length can vary
      expect(exitCode, anyOf(equals(0), equals(1), equals(2))); // Various exit codes are acceptable
    });

    test('Help flag displays usage information', () async {
      final process = await runValidator('.', ['--help']);
      final stdout = <String>[];
      
      process.stdout.transform(utf8.decoder).transform(LineSplitter()).listen(stdout.add);
      
      final exitCode = await process.exitCode;
      final output = stdout.join('\n');
      
      expect(exitCode, equals(0));
      expect(output.contains('ARB Translation Validator'), isTrue);
      expect(output.contains('Usage:'), isTrue);
    });

    test('Invalid directory fails gracefully', () async {
      final process = await runValidator('/nonexistent/directory', []);
      final exitCode = await process.exitCode;
      expect(exitCode, equals(2)); // Script error exit code
    });
  });

  group('ARB File Content Tests', () {
    test('Reference file structure is valid', () async {
      final refFile = File(p.join(Directory.current.path, 'test', 'tools', 'translation_management', 'test_data', 'app_en.arb'));
      expect(await refFile.exists(), isTrue);
      
      final content = await refFile.readAsString();
      final arb = jsonDecode(content) as Map<String, dynamic>;
      
      expect(arb['@@locale'], equals('en'));
      expect(arb.containsKey('appTitle'), isTrue);
      expect(arb.containsKey('@appTitle'), isTrue);
    });

    test('Test data files contain expected structure', () async {
      final testDataDir = Directory(p.join(Directory.current.path, 'test', 'tools', 'translation_management', 'test_data'));
      final arbFiles = await testDataDir.list()
          .where((entity) => entity is File && entity.path.endsWith('.arb'))
          .toList();
      
      expect(arbFiles.length, greaterThan(3)); // Should have multiple test files
      
      for (final file in arbFiles) {
        final content = await (file as File).readAsString();
        if (file.path.contains('invalid_syntax')) {
          // This file should be invalid JSON
          expect(() => jsonDecode(content), throwsA(isA<FormatException>()));
        } else {
          // Other files should be valid JSON
          final arb = jsonDecode(content) as Map<String, dynamic>;
          expect(arb.containsKey('@@locale'), isTrue);
        }
      }
    });
  });
}

