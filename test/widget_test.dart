import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

void main() {
  group('Localization Tests', () {
    testWidgets('English translations work correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          locale: Locale('en'),
          localizationsDelegates: [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: [
            Locale('en'),
            Locale('hi'),
            Locale('bn'),
            Locale('as'),
            Locale('pa'),
            Locale('mr'),
            Locale('kn'),
            Locale('ta'),
            Locale('te'),
            Locale('ml'),
          ],
          home: TestWidget(),
        ),
      );

      await tester.pumpAndSettle();

      // Test English translations
      expect(find.text('Menu'), findsOneWidget);
      expect(find.text('Train Tracker'), findsOneWidget);
      expect(find.text('Assign CA'), findsOneWidget);
      expect(find.text('Customer Care'), findsOneWidget);
    });

    testWidgets('Hindi translations work correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          locale: Locale('hi'),
          localizationsDelegates: [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: [
            Locale('en'),
            Locale('hi'),
            Locale('bn'),
            Locale('as'),
            Locale('pa'),
            Locale('mr'),
            Locale('kn'),
            Locale('ta'),
            Locale('te'),
            Locale('ml'),
          ],
          home: TestWidget(),
        ),
      );

      await tester.pumpAndSettle();

      // Test Hindi translations
      expect(find.text('मेनू'), findsOneWidget);
      expect(find.text('ट्रेन ट्रैकर'), findsOneWidget);
      expect(find.text('CA असाइन करें'), findsOneWidget);
      expect(find.text('ग्राहक सेवा'), findsOneWidget);
    });

    testWidgets('Bengali translations work correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          locale: Locale('bn'),
          localizationsDelegates: [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: [
            Locale('en'),
            Locale('hi'),
            Locale('bn'),
            Locale('as'),
            Locale('pa'),
            Locale('mr'),
            Locale('kn'),
            Locale('ta'),
            Locale('te'),
            Locale('ml'),
          ],
          home: TestWidget(),
        ),
      );

      await tester.pumpAndSettle();

      // Test Bengali translations
      expect(find.text('মেনু'), findsOneWidget);
      expect(find.text('ট্রেন ট্র্যাকার'), findsOneWidget);
      expect(find.text('CA নিয়োগ করুন'), findsOneWidget);
      expect(find.text('গ্রাহক সেবা'), findsOneWidget);
    });
  });
}

class TestWidget extends StatelessWidget {
  const TestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.text_menu),
      ),
      body: Column(
        children: [
          Text(localizations.text_train_tracker),
          Text(localizations.text_assign_ca),
          Text(localizations.text_customer_care),
        ],
      ),
    );
  }
}
