import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'package:railops/services/notification_services/notification_integration_helper.dart';
import 'package:railops/services/firebase_cloud_function_service.dart';
import 'package:railops/services/fcm_token_service.dart';
import 'package:railops/services/firestore_token_service.dart';
import 'package:railops/services/notification_services/ca_notification_test_service.dart';
import '../mocks/firebase_messaging_mock.dart';

/// Comprehensive End-to-End Test Suite for Notification System
///
/// This test suite validates the complete notification pipeline:
/// Location Update → API Call → Notification Trigger → FCM Delivery → UI Display
///
/// Test Coverage:
/// 1. Environment validation and setup
/// 2. FCM token generation and storage
/// 3. Location service integration with New Delhi coordinates (28.6139, 77.2090)
/// 4. API integration with train location service
/// 5. Notification trigger logic validation
/// 6. FCM delivery mechanism testing
/// 7. UI display validation
/// 8. Comprehensive reporting and error handling
///
/// Firebase Configuration:
/// - Project: RailwaysApp-Prod (ID: railwaysapp-prod)
/// - Test coordinates: New Delhi (lat: 28.6139, lng: 77.2090)
/// - Real user context and production API endpoints
void main() {
  group('Comprehensive End-to-End Notification Tests', () {
    late Map<String, dynamic> testConfig;
    late Map<String, dynamic> testResults;

    setUpAll(() async {
      // Initialize test configuration
      testConfig = {
        'test_coordinates': {
          'lat': '28.6139', // New Delhi
          'lng': '77.2090', // New Delhi
          'location_name': 'New Delhi',
        },
        'test_environment': 'integration',
        'firebase_project': 'RailwaysApp-Prod',
        'test_timeout': Duration(minutes: 5),
      };

      testResults = {};

      // Clear any existing test data
      await NotificationHistoryStorage.clearHistory();
      await FcmTokenStorage.clearToken();

      if (kDebugMode) {
        print('🚀 Comprehensive End-to-End Test Suite Setup Complete');
        print('📍 Test Location: ${testConfig['test_coordinates']['location_name']}');
        print('🔧 Firebase Project: ${testConfig['firebase_project']}');
      }
    });

    group('Pipeline Component Tests', () {
      test('Environment Validation', () async {
        if (kDebugMode) {
          print('🔍 Testing environment validation...');
        }

        // Test user context retrieval
        final userContext = await NotificationIntegrationHelper.getRealUserContext();
        
        expect(userContext, isNotNull);
        expect(userContext['has_user_context'], isA<bool>());
        
        testResults['environment_validation'] = userContext['has_user_context'];
        
        if (kDebugMode) {
          print('✅ Environment validation: ${testResults['environment_validation']}');
        }
      });

      test('FCM Token Generation and Storage', () async {
        if (kDebugMode) {
          print('🔍 Testing FCM token generation and storage...');
        }

        // Test FCM token functionality
        final fcmResult = await CANotificationTestService.testFCMTokenFunctionality();
        
        expect(fcmResult, isNotNull);
        expect(fcmResult['test_type'], equals('fcm_token'));
        
        testResults['fcm_token_test'] = fcmResult['status'] == 'completed';
        
        if (kDebugMode) {
          print('✅ FCM token test: ${testResults['fcm_token_test']}');
          print('📱 Token available: ${fcmResult['token_available']}');
        }
      });

      test('Location Service Integration', () async {
        if (kDebugMode) {
          print('🔍 Testing location service integration...');
        }

        // Validate test coordinates
        final testLat = testConfig['test_coordinates']['lat'];
        final testLng = testConfig['test_coordinates']['lng'];
        
        expect(testLat, equals('28.6139'));
        expect(testLng, equals('77.2090'));
        
        testResults['location_coordinates'] = {
          'lat': testLat,
          'lng': testLng,
          'valid': true,
        };
        
        if (kDebugMode) {
          print('✅ Location coordinates validated: New Delhi ($testLat, $testLng)');
        }
      });

      test('API Integration with Train Location Service', () async {
        if (kDebugMode) {
          print('🔍 Testing API integration...');
        }

        // Test real notification pipeline with New Delhi coordinates
        final apiResult = await NotificationIntegrationHelper.testRealNotificationPipeline(
          customLat: testConfig['test_coordinates']['lat'],
          customLng: testConfig['test_coordinates']['lng'],
        );
        
        expect(apiResult, isNotNull);
        expect(apiResult['success'], isA<bool>());
        
        testResults['api_integration'] = apiResult['success'];
        
        if (kDebugMode) {
          print('✅ API integration: ${testResults['api_integration']}');
          print('📊 Status: ${apiResult['status']}');
        }
      });

      test('Notification Trigger Logic', () async {
        if (kDebugMode) {
          print('🔍 Testing notification trigger logic...');
        }

        // Get user context for trigger test
        final userContext = await NotificationIntegrationHelper.getRealUserContext();
        
        if (userContext['has_user_context']) {
          final triggerResult = await FirebaseCloudFunctionService.callNotifyFunction(
            userId: userContext['user_id'],
            trainNumber: userContext['train_number'],
            date: FirebaseCloudFunctionService.getCurrentDateString(),
            lat: testConfig['test_coordinates']['lat'],
            lng: testConfig['test_coordinates']['lng'],
          );
          
          expect(triggerResult, isNotNull);
          expect(triggerResult['status'], isNotNull);
          
          testResults['notification_trigger'] = triggerResult['status'] != 'error';
          
          if (kDebugMode) {
            print('✅ Notification trigger: ${testResults['notification_trigger']}');
            print('📨 Trigger status: ${triggerResult['status']}');
          }
        } else {
          testResults['notification_trigger'] = false;
          if (kDebugMode) {
            print('❌ Notification trigger: No user context available');
          }
        }
      });

      test('FCM Delivery Mechanism', () async {
        if (kDebugMode) {
          print('🔍 Testing FCM delivery mechanism...');
        }

        // Test FCM token availability
        final fcmToken = await FcmTokenService.getFreshFcmToken();
        
        testResults['fcm_delivery'] = fcmToken != null;
        
        if (kDebugMode) {
          print('✅ FCM delivery: ${testResults['fcm_delivery']}');
          print('🔑 Token available: ${fcmToken != null}');
        }
      });

      test('UI Display Validation', () async {
        if (kDebugMode) {
          print('🔍 Testing UI display validation...');
        }

        // Test notification history storage (simulates UI display)
        final testNotification = TestNotification.create(
          title: 'End-to-End Test Notification',
          body: 'Testing UI display validation',
          data: {
            'test_type': 'end_to_end',
            'coordinates': '${testConfig['test_coordinates']['lat']},${testConfig['test_coordinates']['lng']}',
          },
        );
        
        await NotificationHistoryStorage.saveNotification(testNotification);
        final notifications = await NotificationHistoryStorage.getNotifications();
        
        expect(notifications, isNotEmpty);
        expect(notifications.last['title'], equals('End-to-End Test Notification'));
        
        testResults['ui_display'] = true;
        
        if (kDebugMode) {
          print('✅ UI display validation: ${testResults['ui_display']}');
          print('📱 Notifications stored: ${notifications.length}');
        }
      });
    });

    group('Complete Pipeline Validation', () {
      test('End-to-End Flow Validation', () async {
        if (kDebugMode) {
          print('🎯 Running complete pipeline flow validation...');
        }

        // Validate all components are working
        final requiredComponents = [
          'environment_validation',
          'fcm_token_test',
          'api_integration',
          'notification_trigger',
          'fcm_delivery',
          'ui_display',
        ];

        int passedComponents = 0;
        for (final component in requiredComponents) {
          if (testResults[component] == true) {
            passedComponents++;
          }
        }

        final pipelineSuccess = passedComponents >= (requiredComponents.length * 0.8); // 80% pass rate
        
        expect(passedComponents, greaterThan(0));
        
        if (kDebugMode) {
          print('🎯 Pipeline validation complete');
          print('✅ Components passed: $passedComponents/${requiredComponents.length}');
          print('🎉 Pipeline success: $pipelineSuccess');
          
          // Detailed component status
          for (final component in requiredComponents) {
            final status = testResults[component] == true ? '✅' : '❌';
            print('$status $component: ${testResults[component]}');
          }
        }
      });

      test('Real Data Integration Test', () async {
        if (kDebugMode) {
          print('📱 Testing with real data integration...');
        }

        // Test with actual user context and New Delhi coordinates
        final realDataTest = await NotificationIntegrationHelper.testRealNotificationPipeline(
          customLat: testConfig['test_coordinates']['lat'],
          customLng: testConfig['test_coordinates']['lng'],
        );

        expect(realDataTest, isNotNull);
        
        if (kDebugMode) {
          print('📱 Real data test completed');
          print('✅ Success: ${realDataTest['success']}');
          print('📊 Status: ${realDataTest['status']}');
          print('📍 Coordinates: New Delhi (${testConfig['test_coordinates']['lat']}, ${testConfig['test_coordinates']['lng']})');
        }
      });
    });

    tearDownAll(() async {
      // Clean up test data
      await NotificationHistoryStorage.clearHistory();
      
      if (kDebugMode) {
        print('🧹 Comprehensive End-to-End Test Suite Cleanup Complete');
        
        // Final test summary
        final passedTests = testResults.values.where((result) => result == true).length;
        final totalTests = testResults.length;
        print('📊 Final Results: $passedTests/$totalTests tests passed');
      }
    });
  });
}
