{"@@locale": "mr", "appTitle": "रेलओप्स", "@appTitle": {"description": "The title of the application"}, "cancel": "", "confirm": "", "delete": "", "edit": "", "error": "", "home": "", "loading": "", "login": "लॉगिन", "@login": {"description": "Login button text"}, "logout": "", "next": "", "no": "", "ok": "", "password": "", "previous": "", "refresh": "", "retry": "", "save": "", "search": "", "settings": "", "submit": "", "username": "", "welcome": "रेलओप्समध्ये आपले स्वागत", "@welcome": {"description": "Welcome message displayed on home screen"}, "yes": "", "test_welcome": "स्वागत", "@test_welcome": {"description": "Welcome message for testing", "context": "test_context"}, "test_login": "लॉगिन", "@test_login": {"description": "Login button text", "context": "button_labels"}, "text_menu": "मेनू", "@text_menu": {"description": "Text for drawer menu header", "context": "custom_drawer"}, "text_train_tracker": "ट्रेन ट्रॅकर", "@text_train_tracker": {"description": "Text from text_widgets: Train Tracker", "context": "text_widgets"}, "text_assign_ca": "CA नियुक्त करा", "@text_assign_ca": {"description": "Text from text_widgets: Assign CA", "context": "text_widgets"}, "text_assign_cs": "CS नियुक्त करा", "@text_assign_cs": {"description": "Text from text_widgets: Assign CS", "context": "text_widgets"}, "text_pnr_details": "PNR तपशील", "@text_pnr_details": {"description": "Text from text_widgets: PNR Details", "context": "text_widgets"}, "text_passenger_chart": "प्रवासी चार्ट", "@text_passenger_chart": {"description": "Text from text_widgets: Passenger Chart", "context": "text_widgets"}, "text_map_screen": "नकाशा स्क्रीन", "@text_map_screen": {"description": "Text from text_widgets: Map Screen", "context": "text_widgets"}, "text_configuration": "कॉन्फिगरेशन", "@text_configuration": {"description": "Text from text_widgets: Configuration", "context": "text_widgets"}, "text_reports": "अहवाल", "@text_reports": {"description": "Text from text_widgets: Reports", "context": "text_widgets"}, "text_passenger_feedback": "प्रवासी फीडबॅक", "@text_passenger_feedback": {"description": "Text from text_widgets: Passenger Feedback", "context": "text_widgets"}, "text_rake_deficiency_report_1": "रेक कमतरता अहवाल", "@text_rake_deficiency_report_1": {"description": "Text from text_widgets: Rake Deficiency Report", "context": "text_widgets"}, "text_obhs_to_mcc": "OBHS ते MCC हस्तांतरण", "@text_obhs_to_mcc": {"description": "Text from text_widgets: OBHS to MCC Handover", "context": "text_widgets"}, "text_mcc_to_obhs": "MCC ते OBHS हस्तांतरण", "@text_mcc_to_obhs": {"description": "Text from text_widgets: MCC to OBHS Handover", "context": "text_widgets"}, "text_upload_data": "डेटा अपलोड करा", "@text_upload_data": {"description": "Text from text_widgets: Upload data", "context": "text_widgets"}, "text_user_management": "वापरकर्ता व्यवस्थापन", "@text_user_management": {"description": "Text from text_widgets: User Management", "context": "text_widgets"}, "text_issue_management": "समस्या व्यवस्थापन", "@text_issue_management": {"description": "Text from text_widgets: Issue Management", "context": "text_widgets"}, "text_rail_sathi_qr": "रेल साथी QR", "@text_rail_sathi_qr": {"description": "Text from text_widgets: Rail Sathi Qr", "context": "text_widgets"}, "text_customer_care": "ग्राहक सेवा", "@text_customer_care": {"description": "Text from text_widgets: Customer Care", "context": "text_widgets"}, "text_change_mobile": "मोबाइल बदला", "@text_change_mobile": {"description": "Profile screen translation for: text_change_mobile", "context": "profile_screen"}, "text_change_whatsapp": "व्हाट्सअप नंबर बदला", "@text_change_whatsapp": {"description": "Profile screen translation for: text_change_whatsapp", "context": "profile_screen"}, "text_alert": "सावधान", "@text_alert": {"description": "Profile screen translation for: text_alert", "context": "profile_screen"}, "text_close": "ब<PERSON><PERSON> करा", "@text_close": {"description": "Profile screen translation for: text_close", "context": "profile_screen"}, "text_change_your_email": "तुमचा ईमेल बदला", "@text_change_your_email": {"description": "Profile screen translation for: text_change_your_email", "context": "profile_screen"}, "text_current_email": "सध्याचा ईमेल", "@text_current_email": {"description": "Profile screen translation for: text_current_email", "context": "profile_screen"}, "text_new_email": "नवीन ईमेल", "@text_new_email": {"description": "Profile screen translation for: text_new_email", "context": "profile_screen"}, "text_please_enter_new_email": "कृपया नवीन ईमेल टाका", "@text_please_enter_new_email": {"description": "Profile screen translation for: text_please_enter_new_email", "context": "profile_screen"}, "text_otp": "ओटीपी", "@text_otp": {"description": "Profile screen translation for: text_otp", "context": "profile_screen"}, "text_resend_otp": "ओटीपी पुन्हा पाठवा", "@text_resend_otp": {"description": "Profile screen translation for: text_resend_otp", "context": "profile_screen"}, "text_resend_in_seconds": "{seconds} सेकंदात पुन्हा पाठवा", "@text_resend_in_seconds": {"description": "Profile screen translation for: text_resend_in_seconds", "context": "profile_screen"}, "text_verify_otp": "ओटीपी तपासा", "@text_verify_otp": {"description": "Profile screen translation for: text_verify_otp", "context": "profile_screen"}, "text_generate_otp": "ओटीपी तयार करा", "@text_generate_otp": {"description": "Profile screen translation for: text_generate_otp", "context": "profile_screen"}, "text_change_your_password": "तुमचा पासवर्ड बदला", "@text_change_your_password": {"description": "Profile screen translation for: text_change_your_password", "context": "profile_screen"}, "text_old_password": "जुना पासवर्ड", "@text_old_password": {"description": "Profile screen translation for: text_old_password", "context": "profile_screen"}, "text_new_password": "नवीन पासवर्ड", "@text_new_password": {"description": "Profile screen translation for: text_new_password", "context": "profile_screen"}, "text_confirm_new_password": "नवीन पासवर्डची पुष्टी करा", "@text_confirm_new_password": {"description": "Profile screen translation for: text_confirm_new_password", "context": "profile_screen"}, "text_please_enter_otp": "कृपया ओटीपी टाका", "@text_please_enter_otp": {"description": "Profile screen translation for: text_please_enter_otp", "context": "profile_screen"}, "text_send_mobile_otp": "मोबाइल ओटीपी पाठवा", "@text_send_mobile_otp": {"description": "Profile screen translation for: text_send_mobile_otp", "context": "profile_screen"}, "text_send_email_otp": "ईमेल ओटीपी पाठवा", "@text_send_email_otp": {"description": "Profile screen translation for: text_send_email_otp", "context": "profile_screen"}, "text_please_enter_value": "कृपया एक मूल्य टाका", "@text_please_enter_value": {"description": "Profile screen translation for: text_please_enter_value", "context": "profile_screen"}, "text_please_enter_valid_mobile": "कृपया वैध मोबाइल नंबर टाका", "@text_please_enter_valid_mobile": {"description": "Profile screen translation for: text_please_enter_valid_mobile", "context": "profile_screen"}, "text_success": "यश", "@text_success": {"description": "Profile screen translation for: text_success", "context": "profile_screen"}, "text_ok": "ठीक आहे", "@text_ok": {"description": "Profile screen translation for: text_ok", "context": "profile_screen"}, "text_change_your_mobile_number": "तुमचा मोबाइल नंबर बदला", "@text_change_your_mobile_number": {"description": "Profile screen translation for: text_change_your_mobile_number", "context": "profile_screen"}, "text_current_mobile_number": "सध्याचा मोबाइल नंबर", "@text_current_mobile_number": {"description": "Profile screen translation for: text_current_mobile_number", "context": "profile_screen"}, "text_new_mobile_number": "नवीन मोबाइल नंबर", "@text_new_mobile_number": {"description": "Profile screen translation for: text_new_mobile_number", "context": "profile_screen"}, "text_please_enter_new_mobile": "कृपया नवीन मोबाइल नंबर टाका", "@text_please_enter_new_mobile": {"description": "Profile screen translation for: text_please_enter_new_mobile", "context": "profile_screen"}, "text_change_your_whatsapp_number": "तुमचा व्हाट्सअप नंबर बदला", "@text_change_your_whatsapp_number": {"description": "Profile screen translation for: text_change_your_whatsapp_number", "context": "profile_screen"}, "text_current_whatsapp_number": "सध्याचा व्हाट्सअप नंबर", "@text_current_whatsapp_number": {"description": "Profile screen translation for: text_current_whatsapp_number", "context": "profile_screen"}, "text_new_whatsapp_number": "नवीन व्हाट्सअप नंबर", "@text_new_whatsapp_number": {"description": "Profile screen translation for: text_new_whatsapp_number", "context": "profile_screen"}, "text_please_enter_new_whatsapp": "कृपया नवीन व्हाट्सअप मोबाइल नंबर टाका", "@text_please_enter_new_whatsapp": {"description": "Profile screen translation for: text_please_enter_new_whatsapp", "context": "profile_screen"}, "text_train": "रेल्वे", "@text_train": {"description": "Table column header for train", "context": "add_train_screen"}, "text_coaches": "कोच", "@text_coaches": {"description": "Table column header for coaches", "context": "add_train_screen"}, "text_origin_date": "मूळ तारीख", "@text_origin_date": {"description": "Table column header for date", "context": "add_train_screen"}, "text_na": "उपलब्ध नाही", "@text_na": {"description": "Table column header for na", "context": "add_train_screen"}, "text_send_otp": "OTP पाठवा", "@text_send_otp": {"description": "Send OTP button text", "context": "change_email_modal"}, "text_failed_to_send_otp": "OTP पाठवण्यात अयशस्वी: {error}", "@text_failed_to_send_otp": {"description": "Error message when OTP sending fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_email_saved_successfully": "ईमेल यशस्वीरित्या सेव्ह झाला!", "@text_email_saved_successfully": {"description": "Success message when email is saved", "context": "change_email_modal"}, "text_failed_to_verify_otp": "OTP सत्यापन करण्यात अयशस्वी: {error}", "@text_failed_to_verify_otp": {"description": "Error message when OTP verification fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_cancel": "र<PERSON><PERSON><PERSON> करा", "@text_cancel": {"description": "Cancel button text", "context": "general"}, "rm_feedback_app_bar_title": "रेलमदद प्रवासी फीडबॅक", "rm_feedback_main_title": "रेलमदद फीडबॅक", "form_pnr_number": "PNR नंबर *", "form_crn_number": "CRN नंबर*", "form_train_no": "रेल्वे नंबर *", "form_train_name": "रेल्वेचे नाव *", "form_passenger_name": "प्रवाशाचे नाव *", "form_coach_no": "कोच नंबर *", "form_berth_no": "बर्थ नंबर *", "form_mobile_number": "मोबाइल नंबर *", "form_email_id": "ईमेल आयडी", "form_issue_type": "समस्येचा प्रकार", "form_sub_issue_type": "उप समस्येचा प्रकार", "form_resolved_status": "निराकरण झाले (होय/नाही) *", "form_marks": "गुण (1 ते 10) *", "form_task_status": "कामाची स्थिती *", "form_remarks": "प्रवाशाची टिप्पणी", "btn_validate": "सत्यापन करा", "btn_verified": "सत्यापित", "btn_verify_email": "ईमेल सत्यापन करा", "btn_verify_otp": "OTP सत्यापन करा", "btn_submit_feedback": "फीडबॅक सबमिट करा", "btn_upload_pnr_image": "PNR प्रतिमा अपलोड करा", "btn_pick_media": "फीडबॅकसाठी प्रतिमा/व्हिडिओ निवडा", "btn_camera": "कॅमेरा", "btn_gallery": "गॅलरी", "btn_image": "प्रतिमा", "btn_video": "व्हिडिओ", "btn_i_understand": "मला समजले", "btn_ok": "ठीक आहे", "status_verified": "सत्यापित", "status_pending": "प्रलंबित", "status_completed": "पूर्ण", "status_yes": "होय", "status_no": "नाही", "status_select": "निवडा", "section_email_verification": "ईमेल सत्यापन (पर्यायी)", "section_selected_images": "निवडलेल्या प्रतिमा:", "section_selected_videos": "निवडलेले व्हिडिओ:", "dialog_email_verification_info": "ईमेल सत्यापन माहिती", "dialog_select_media_type": "मीडिया प्रकार निवडा", "validation_fill_all_fields": "कृपया सर्व फील्ड वैध माहितीसह भरा.", "validation_pnr_digits": "PNR नंबर 8 किंवा 10 अंकांचा असावा", "validation_berth_number": "बर्थ नंबर वैध संख्या असावी", "validation_feedback_length": "फीडबॅक 100 अक्षरांपेक्षा जास्त असू शकत नाही", "validation_email_required": "कृपया वैध ईमेल आयडी टाका.", "validation_otp_required": "कृपया OTP टाका.", "validation_train_no_required": "रेल्वे नंबर आवश्यक आहे", "validation_train_name_required": "रेल्वेचे नाव आवश्यक आहे", "validation_passenger_name_required": "प्रवाशाचे नाव आवश्यक आहे", "validation_mobile_required": "मोबाइल नंबर आवश्यक आहे", "validation_mobile_digits": "मोबाइल नंबर 10 अंकांचा असावा", "validation_issue_type_required": "कृपया समस्येचा प्रकार निवडा", "validation_sub_issue_required": "कृपया उप-समस्येचा प्रकार निवडा", "validation_resolved_required": "कृपया निराकरणाची स्थिती निवडा", "validation_marks_required": "कृपया गुण निवडा", "msg_pnr_images_limit": "तुम्ही फक्त 3 PNR प्रतिमा निवडू शकता", "msg_feedback_images_limit": "जास्तीत जास्त 3 फीडबॅक प्रतिमांना परवानगी आहे", "msg_images_added_limit": "फक्त {count} प्रतिमा जोडल्या. जास्तीत जास्त 3 ची मर्यादा गाठली.", "msg_error_picking_media": "मीडिया निवडण्यात त्रुटी: {error}", "msg_failed_fetch_train_name": "रेल्वेचे नाव आणण्यात अयशस्वी", "msg_invalid_pnr": "अवैध PNR नंबर.", "msg_pnr_success": "PNR तपशील यशस्वीरित्या आणले.", "msg_pnr_validation_failed": "PNR तपशील सत्यापन करण्यात अयशस्वी. अवैध PNR नंबर.", "msg_email_verification_sent": "ईमेल सत्यापन सुरू केले. कृपया तुमचे इनबॉक्स आणि स्पॅम फोल्डर दोन्ही तपासा..", "msg_otp_verified": "OTP यशस्वीरित्या सत्यापित केले.", "msg_feedback_submitted": "फीडबॅक यशस्वीरित्या सबमिट केले!", "msg_feedback_failed": "फीडबॅक सबमिट करण्यात अयशस्वी", "msg_unexpected_error": "अनपेक्षित त्रुटी आली. कृपया पुन्हा प्रयत्न करा.", "info_spam_folder_note": "कृपया लक्षात ठेवा की सत्यापन ईमेल कधीकधी तुमच्या स्पॅम/जंक फोल्डरमध्ये पोहोचू शकतात.", "info_after_requesting_otp": "OTP विनंती केल्यानंतर:", "info_check_inbox": "प्रथम तुमचे इनबॉक्स तपासा", "info_check_spam": "सापडले नाही तर, स्पॅम/जंक फोल्डर तपासा", "info_add_safe_sender": "आमचे डोमेन तुमच्या सुरक्षित पाठवणारे यादीत जोडा", "text_no_feedback_images": "कोणत्याही फीडबॅक प्रतिमा निवडल्या नाहीत", "text_no_pnr_images": "कोणत्याही PNR प्रतिमा निवडल्या नाहीत", "text_character_count": "{count}/100 अक्षरे", "loading_sending_otp": "OTP पाठवत आहे", "loading_verifying_otp": "OTP सत्यापन करत आहे", "loading_submitting_feedback": "फीडबॅक सबमिट करत आहे", "attendance_api_summary": "API सारांश तपशील", "@attendance_api_summary": {"description": "Attendance: API Summary Details", "context": "attendance"}, "attendance_assigned_coaches": "🚆 नियुक्त Coaches:", "@attendance_assigned_coaches": {"description": "Attendance: 🚆 Assigned Coaches:", "context": "attendance"}, "attendance_available_label": "उपलब्ध: {count}", "@attendance_available_label": {"description": "Attendance: Available: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_available_section": "उपलब्ध", "@attendance_available_section": {"description": "Attendance: Available", "context": "attendance"}, "attendance_berths_count": "{count} berths", "@attendance_berths_count": {"description": "Attendance: {count} berths", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_buffer_time_restriction": "The वेळ between the current वेळ and the ट्रेन's arrival वेळ is not within the 2-hour buffer.", "@attendance_buffer_time_restriction": {"description": "Attendance: The time between the current time and the train's arrival time is not within the 2-hour buffer.", "context": "attendance"}, "attendance_cancel": "र<PERSON><PERSON><PERSON> करा", "@attendance_cancel": {"description": "Attendance: Cancel", "context": "attendance"}, "attendance_cancelled": "Cancelled", "@attendance_cancelled": {"description": "Attendance: Cancelled", "context": "attendance"}, "attendance_chart_not_prepared": "चार्ट has not been prepared for this स्टेशन", "@attendance_chart_not_prepared": {"description": "Attendance: Chart has not been prepared for this station", "context": "attendance"}, "attendance_charting_refreshed": "चार्टिंग refreshed at: {time}", "@attendance_charting_refreshed": {"description": "Attendance: Charting refreshed at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_started": "चार्टिंग started at: {time}", "@attendance_charting_started": {"description": "Attendance: Charting started at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_time": "चार्टिंग वेळ: {time}", "@attendance_charting_time": {"description": "Attendance: Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_click_more": "click for more...", "@attendance_click_more": {"description": "Attendance: click for more...", "context": "attendance"}, "attendance_coach_label": "🚃 कोच: {coach}", "@attendance_coach_label": {"description": "Attendance: 🚃 Coach: {coach}", "context": "attendance", "placeholders": {"coach": {"type": "String"}}}, "attendance_coach_occupancy": "कोच Occupancy तपशील", "@attendance_coach_occupancy": {"description": "Attendance: Coach Occupancy <PERSON>", "context": "attendance"}, "attendance_coach_type": "कोच Type:", "@attendance_coach_type": {"description": "Attendance: Coach Type:", "context": "attendance"}, "attendance_coaches": "Coaches: {coaches}", "@attendance_coaches": {"description": "Attendance: Coaches: {coaches}", "context": "attendance", "placeholders": {"coaches": {"type": "String"}}}, "attendance_concise_view": "Concise पहा", "@attendance_concise_view": {"description": "Attendance: Concise View", "context": "attendance"}, "attendance_count_label": "A: {count}", "@attendance_count_label": {"description": "Attendance: A: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_daily": "Daily", "@attendance_daily": {"description": "Attendance: Daily", "context": "attendance"}, "attendance_data_refreshed": "डेटा refreshed यशस्वीरित्या", "@attendance_data_refreshed": {"description": "Attendance: Data refreshed successfully", "context": "attendance"}, "attendance_deboarding": "🔴 उतरणे:", "@attendance_deboarding": {"description": "Attendance: 🔴 Deboarding:", "context": "attendance"}, "attendance_deboarding_none": "🔴 उतरणे: None", "@attendance_deboarding_none": {"description": "Attendance: 🔴 Deboarding: None", "context": "attendance"}, "attendance_detailed_view": "Detailed पहा", "@attendance_detailed_view": {"description": "Attendance: Detailed View", "context": "attendance"}, "attendance_ehk_assigned": "EHK नियुक्त for ट्रेन:{ehkName}", "@attendance_ehk_assigned": {"description": "Attendance: EHK Assigned for train:{ehkName}", "context": "attendance", "placeholders": {"ehkName": {"type": "String"}}}, "attendance_expected_charting": "Expected चार्टिंग वेळ: {time}", "@attendance_expected_charting": {"description": "Attendance: Expected Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_failed_load_data": "अयशस्वी to load detailed डेटा: {error}", "@attendance_failed_load_data": {"description": "Attendance: Failed to load detailed data: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_failed_update_status": "अयशस्वी to अपडेट करा ट्रेन स्थिती", "@attendance_failed_update_status": {"description": "Attendance: Failed to update train status", "context": "attendance"}, "attendance_go": "जा", "@attendance_go": {"description": "Attendance: Go", "context": "attendance"}, "attendance_in_route": "In-route", "@attendance_in_route": {"description": "Attendance: In-route", "context": "attendance"}, "attendance_inside": "आत", "@attendance_inside": {"description": "Attendance: Inside", "context": "attendance"}, "attendance_inside_train": "You are now चिन्हांकित as आत the ट्रेन", "@attendance_inside_train": {"description": "Attendance: You are now marked as inside the train", "context": "attendance"}, "attendance_journey_status_updated": "प्रवास स्थिती updated to {status}", "@attendance_journey_status_updated": {"description": "Attendance: Journey status updated to {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_last_fetched": "Last fetched: {time}", "@attendance_last_fetched": {"description": "Attendance: Last fetched: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_loading": "लोड होत आहे...", "@attendance_loading": {"description": "Attendance: Loading...", "context": "attendance"}, "attendance_location_not_fetched": "ट्रेन ठिकाण is not fetched yet, कृपया try again later", "@attendance_location_not_fetched": {"description": "Attendance: Train Location is not fetched yet, please try again later", "context": "attendance"}, "attendance_na": "N/A", "@attendance_na": {"description": "Attendance: N/A", "context": "attendance"}, "attendance_near_stations": "You're near the following स्टेशन(s):", "@attendance_near_stations": {"description": "Attendance: You're near the following station(s):", "context": "attendance"}, "attendance_nearby_station_alert": "🛤️ जवळचे स्टेशन सावधान", "@attendance_nearby_station_alert": {"description": "Attendance: 🛤️ Nearby Station Alert", "context": "attendance"}, "attendance_non_sleeper": "Non-Sleeper", "@attendance_non_sleeper": {"description": "Attendance: Non-Sleeper", "context": "attendance"}, "attendance_not_attendance_station": "उपस्थिति cannot be चिन्हांकित for स्टेशन {stationCode} as it is not an उपस्थिति स्टेशन.", "@attendance_not_attendance_station": {"description": "Attendance: Attendance cannot be marked for station {stationCode} as it is not an attendance station.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_not_inside_train": "You are not आत the ट्रेन. कृपया जा आत the ट्रेन first.", "@attendance_not_inside_train": {"description": "Attendance: You are not inside the train. Please go inside the train first.", "context": "attendance"}, "attendance_off_label": "Off: {count}", "@attendance_off_label": {"description": "Attendance: Off: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_offboarding_section": "Offboarding", "@attendance_offboarding_section": {"description": "Attendance: Offboarding", "context": "attendance"}, "attendance_ok": "ठीक", "@attendance_ok": {"description": "Attendance: OK", "context": "attendance"}, "attendance_on_label": "On: {count}", "@attendance_on_label": {"description": "Attendance: On: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_onboarding": "🟢 चढणे:", "@attendance_onboarding": {"description": "Attendance: 🟢 Onboarding:", "context": "attendance"}, "attendance_onboarding_none": "🟢 चढणे: None", "@attendance_onboarding_none": {"description": "Attendance: 🟢 Onboarding: None", "context": "attendance"}, "attendance_onboarding_section": "चढणे", "@attendance_onboarding_section": {"description": "Attendance: Onboarding", "context": "attendance"}, "attendance_other_ca": "Other CA", "@attendance_other_ca": {"description": "Attendance: Other CA", "context": "attendance"}, "attendance_other_ehk": "Other EHK/OBHS", "@attendance_other_ehk": {"description": "Attendance: Other EHK/OBHS", "context": "attendance"}, "attendance_outside_train": "You are now चिन्हां<PERSON>ित as बाहेर the ट्रेन", "@attendance_outside_train": {"description": "Attendance: You are now marked as outside the train", "context": "attendance"}, "attendance_passenger_chart": "प्रवासी चार्ट   Atten..", "@attendance_passenger_chart": {"description": "Attendance: Passenger Chart   Atten..", "context": "attendance"}, "attendance_refresh_failed": "रिफ्रेश अयशस्वी: {error}", "@attendance_refresh_failed": {"description": "Attendance: Refresh failed: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_screen_title": "उपस्थिति", "@attendance_screen_title": {"description": "Attendance: Attendance", "context": "attendance"}, "attendance_select_date": "निवडा तारीख", "@attendance_select_date": {"description": "Attendance: Select date", "context": "attendance"}, "attendance_select_train_date": "कृपया निवडा a ट्रेन क्रमांक and तारीख.", "@attendance_select_train_date": {"description": "Attendance: Please select a train number and date.", "context": "attendance"}, "attendance_select_train_first": "कृपया निवडा a ट्रेन first", "@attendance_select_train_first": {"description": "Attendance: Please select a train first", "context": "attendance"}, "attendance_self": "Self", "@attendance_self": {"description": "Attendance: Self", "context": "attendance"}, "attendance_sleeper": "Sleeper", "@attendance_sleeper": {"description": "Attendance: Sleeper", "context": "attendance"}, "attendance_station_details": "स्टेशन तपशील - {stationCode}", "@attendance_station_details": {"description": "Attendance: Station Details - {stationCode}", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_stoppages": "Stoppages", "@attendance_stoppages": {"description": "Attendance: Stoppages", "context": "attendance"}, "attendance_timings": "Tim<PERSON>", "@attendance_timings": {"description": "Attendance: Timings", "context": "attendance"}, "attendance_today": "Today", "@attendance_today": {"description": "Attendance: Today", "context": "attendance"}, "attendance_too_far_from_station": "You're over 50 KM away from the selected स्टेशन {stationCode}. उपस्थिति can only be चिन्हांकित when you're within the allowed range.", "@attendance_too_far_from_station": {"description": "Attendance: You're over 50 KM away from the selected station {stationCode}. Attendance can only be marked when you're within the allowed range.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_train": "ट्रेन", "@attendance_train": {"description": "Attendance: Train", "context": "attendance"}, "attendance_train_date": "ट्रेन {trainNo} - {date}", "@attendance_train_date": {"description": "Attendance: Train {trainNo} - {date}", "context": "attendance", "placeholders": {"trainNo": {"type": "String"}, "date": {"type": "String"}}}, "attendance_train_depot": "ट्रेन डिपो:{depot}", "@attendance_train_depot": {"description": "Attendance: Train Depot:{depot}", "context": "attendance", "placeholders": {"depot": {"type": "String"}}}, "attendance_train_not_running": "ट्रेन Not Running", "@attendance_train_not_running": {"description": "Attendance: Train Not Running", "context": "attendance"}, "attendance_train_not_running_message": "ट्रेन {trainNumber} is NOT running on {dayOfWeek}", "@attendance_train_not_running_message": {"description": "Attendance: Train {trainNumber} is NOT running on {dayOfWeek}", "context": "attendance", "placeholders": {"trainNumber": {"type": "String"}, "dayOfWeek": {"type": "String"}}}, "attendance_update": "अपडेट करा", "@attendance_update": {"description": "Attendance: Update", "context": "attendance"}, "attendance_update_journey_status": "अपडेट करा प्रवास स्थिती", "@attendance_update_journey_status": {"description": "Attendance: Update Journey Status", "context": "attendance"}, "attendance_update_message": "A new आवृत्ती of the app is उपलब्ध. You must अपडेट करा to continue using the app.", "@attendance_update_message": {"description": "Attendance: A new version of the app is available. You must update to continue using the app.", "context": "attendance"}, "attendance_update_now": "अपडेट करा Now", "@attendance_update_now": {"description": "Attendance: Update Now", "context": "attendance"}, "attendance_update_required": "अपडेट करा आवश्यक", "@attendance_update_required": {"description": "Attendance: Update Required", "context": "attendance"}, "attendance_user_location": "वापरकर्ता ठिकाण:{locationStatus}", "@attendance_user_location": {"description": "Attendance: User Location:{locationStatus}", "context": "attendance", "placeholders": {"locationStatus": {"type": "String"}}}, "attendance_details_distance": "अंतर: {distance} km", "@attendance_details_distance": {"description": "Attendance: Distance: {distance} km", "context": "attendance", "placeholders": {"distance": {"type": "String"}}}, "attendance_details_error": "त्रुटी: {error}", "@attendance_details_error": {"description": "Attendance: Error: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_details_journey_date": "प्रवास तारीख:", "@attendance_details_journey_date": {"description": "Attendance: Journey Date:", "context": "attendance"}, "attendance_details_latitude": "latitude: {latitude}", "@attendance_details_latitude": {"description": "Attendance: latitude: {latitude}", "context": "attendance", "placeholders": {"latitude": {"type": "String"}}}, "attendance_details_longitude": "longitude: {longitude}", "@attendance_details_longitude": {"description": "Attendance: longitude: {longitude}", "context": "attendance", "placeholders": {"longitude": {"type": "String"}}}, "attendance_details_match_percentage": "Match टक्केवारी: {percentage}", "@attendance_details_match_percentage": {"description": "Attendance: Match Percentage: {percentage}", "context": "attendance", "placeholders": {"percentage": {"type": "String"}}}, "attendance_details_nearest_station": "nearest स्टेशन: {station}", "@attendance_details_nearest_station": {"description": "Attendance: nearest Station: {station}", "context": "attendance", "placeholders": {"station": {"type": "String"}}}, "attendance_details_no_attendance": "No उपस्थिति found.", "@attendance_details_no_attendance": {"description": "Attendance: No attendance found.", "context": "attendance"}, "attendance_details_no_data": "No डेटा उपलब्ध.", "@attendance_details_no_data": {"description": "Attendance: No data available.", "context": "attendance"}, "attendance_details_no_human_detected": "No human detected", "@attendance_details_no_human_detected": {"description": "Attendance: No human detected", "context": "attendance"}, "attendance_details_station_code": "स्टेशन Code:", "@attendance_details_station_code": {"description": "Attendance: Station Code:", "context": "attendance"}, "attendance_details_status": "स्थिती: {status}", "@attendance_details_status": {"description": "Attendance: Status: {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_details_status_marked": "चिन्हा<PERSON><PERSON>ित", "@attendance_details_status_marked": {"description": "Attendance: Marked", "context": "attendance"}, "attendance_details_status_pending": "Pending", "@attendance_details_status_pending": {"description": "Attendance: Pending", "context": "attendance"}, "attendance_details_title": "उपस्थिति तपशील", "@attendance_details_title": {"description": "Attendance: Attendance Details", "context": "attendance"}, "attendance_details_train_number": "ट्रेन क्रमांक:", "@attendance_details_train_number": {"description": "Attendance: Train Number:", "context": "attendance"}, "attendance_details_updated": "All तपशील updated यशस्वीरित्या.", "@attendance_details_updated": {"description": "Attendance: All details updated successfully.", "context": "attendance"}, "attendance_details_updated_at": "Updated At: {updatedAt}", "@attendance_details_updated_at": {"description": "Attendance: Updated At: {updatedAt}", "context": "attendance", "placeholders": {"updatedAt": {"type": "String"}}}, "attendance_details_updated_by": "Updated By: {updatedBy}", "@attendance_details_updated_by": {"description": "Attendance: Updated By: {updatedBy}", "context": "attendance", "placeholders": {"updatedBy": {"type": "String"}}}, "attendance_details_username": "Username: {username}", "@attendance_details_username": {"description": "Attendance: Username: {username}", "context": "attendance", "placeholders": {"username": {"type": "String"}}}, "btn_no_attendance_found": "No उपस्थिति found.", "@btn_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}, "text_attendance_already_submitted": "उपस्थिति Already Submitted", "@text_attendance_already_submitted": {"description": "Attendance: Attendance Already Submitted", "context": "attendance"}, "text_attendance_marked_successfully": "उपस्थिति चिन्हांकित यशस्वीरित्या!", "@text_attendance_marked_successfully": {"description": "Attendance: Attendance marked successfully!", "context": "attendance"}, "text_no_attendance_found": "No उपस्थिति found.", "@text_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}}