{"@@locale": "ml", "appTitle": "റെയിൽഓപ്സ്", "@appTitle": {"description": "The title of the application"}, "cancel": "", "confirm": "", "delete": "", "edit": "", "error": "", "home": "", "loading": "", "login": "ലോഗിൻ", "@login": {"description": "Login button text"}, "logout": "", "next": "", "no": "", "ok": "", "password": "", "previous": "", "refresh": "", "retry": "", "save": "", "search": "", "settings": "", "submit": "", "username": "", "welcome": "റെയിൽഓപ്സിൽേക്ക് സ്വാഗതം", "@welcome": {"description": "Welcome message displayed on home screen"}, "yes": "", "test_welcome": "സ്വാഗതം", "@test_welcome": {"description": "Welcome message for testing", "context": "test_context"}, "test_login": "ലോഗിൻ", "@test_login": {"description": "Login button text", "context": "button_labels"}, "text_menu": "മെനു", "@text_menu": {"description": "Text for drawer menu header", "context": "custom_drawer"}, "text_train_tracker": "ട്രെയിൻ ട്രാക്കർ", "@text_train_tracker": {"description": "Text from text_widgets: Train Tracker", "context": "text_widgets"}, "text_assign_ca": "CA നിയോഗിക്കുക", "@text_assign_ca": {"description": "Text from text_widgets: Assign CA", "context": "text_widgets"}, "text_assign_cs": "CS നിയോഗിക്കുക", "@text_assign_cs": {"description": "Text from text_widgets: Assign CS", "context": "text_widgets"}, "text_pnr_details": "PNR വിശദാംശങ്ങൾ", "@text_pnr_details": {"description": "Text from text_widgets: PNR Details", "context": "text_widgets"}, "text_passenger_chart": "യാത്രക്കാരുടെ ചാർട്ട്", "@text_passenger_chart": {"description": "Text from text_widgets: Passenger Chart", "context": "text_widgets"}, "text_map_screen": "മാപ്പ് സ്ക്രീൻ", "@text_map_screen": {"description": "Text from text_widgets: Map Screen", "context": "text_widgets"}, "text_configuration": "കോൺഫിഗറേഷൻ", "@text_configuration": {"description": "Text from text_widgets: Configuration", "context": "text_widgets"}, "text_reports": "റിപ്പോർട്ടുകൾ", "@text_reports": {"description": "Text from text_widgets: Reports", "context": "text_widgets"}, "text_passenger_feedback": "യാത്രക്കാരുടെ ഫീഡ്ബാക്ക്", "@text_passenger_feedback": {"description": "Text from text_widgets: Passenger Feedback", "context": "text_widgets"}, "text_rake_deficiency_report_1": "റേക്ക് കുറവ് റിപ്പോർട്ട്", "@text_rake_deficiency_report_1": {"description": "Text from text_widgets: Rake Deficiency Report", "context": "text_widgets"}, "text_obhs_to_mcc": "OBHS ൽ നിന്ന് MCC കൈമാറ്റം", "@text_obhs_to_mcc": {"description": "Text from text_widgets: OBHS to MCC Handover", "context": "text_widgets"}, "text_mcc_to_obhs": "MCC ൽ നിന്ന് OBHS കൈമാറ്റം", "@text_mcc_to_obhs": {"description": "Text from text_widgets: MCC to OBHS Handover", "context": "text_widgets"}, "text_upload_data": "ഡാറ്റ അപ്‌ലോഡ് ചെയ്യുക", "@text_upload_data": {"description": "Text from text_widgets: Upload data", "context": "text_widgets"}, "text_user_management": "ഉപയോക്തൃ മാനേജ്മെന്റ്", "@text_user_management": {"description": "Text from text_widgets: User Management", "context": "text_widgets"}, "text_issue_management": "പ്രശ്ന മാനേജ്മെന്റ്", "@text_issue_management": {"description": "Text from text_widgets: Issue Management", "context": "text_widgets"}, "text_rail_sathi_qr": "റെയിൽ സാഥി QR", "@text_rail_sathi_qr": {"description": "Text from text_widgets: Rail Sathi Qr", "context": "text_widgets"}, "text_customer_care": "കസ്റ്റമർ കെയർ", "@text_customer_care": {"description": "Text from text_widgets: Customer Care", "context": "text_widgets"}}