{"@@locale": "hi", "appTitle": "रेलऑप्स", "@appTitle": {"description": "The title of the application"}, "cancel": "", "confirm": "", "delete": "", "edit": "", "error": "", "home": "", "loading": "", "login": "लॉगिन", "@login": {"description": "Login button text"}, "logout": "", "next": "", "no": "", "ok": "", "password": "", "previous": "", "refresh": "", "retry": "", "save": "", "search": "", "settings": "", "submit": "", "username": "", "welcome": "रेलऑप्स में आपका स्वागत है", "@welcome": {"description": "Welcome message displayed on home screen"}, "yes": "", "test_welcome": "नमस्ते ऐप में", "@test_welcome": {"description": "Welcome message for testing", "context": "test_context"}, "test_login": "लॉगिन", "@test_login": {"description": "Login button text", "context": "button_labels"}, "text_menu": "मेनू", "@text_menu": {"description": "Text for drawer menu header", "context": "custom_drawer"}, "text_train_tracker": "ट्रेन ट्रैकर", "@text_train_tracker": {"description": "Text from text_widgets: Train Tracker", "context": "text_widgets"}, "text_assign_ca": "CA असाइन करें", "@text_assign_ca": {"description": "Text from text_widgets: Assign CA", "context": "text_widgets"}, "text_assign_cs": "CS असाइन करें", "@text_assign_cs": {"description": "Text from text_widgets: Assign CS", "context": "text_widgets"}, "text_pnr_details": "PNR विवरण", "@text_pnr_details": {"description": "Text from text_widgets: PNR Details", "context": "text_widgets"}, "text_passenger_chart": "यात्री चार्ट", "@text_passenger_chart": {"description": "Text from text_widgets: Passenger Chart", "context": "text_widgets"}, "text_map_screen": "मैप स्क्रीन", "@text_map_screen": {"description": "Text from text_widgets: Map Screen", "context": "text_widgets"}, "text_configuration": "कॉन्फ़िगरेशन", "@text_configuration": {"description": "Text from text_widgets: Configuration", "context": "text_widgets"}, "text_reports": "रिपोर्ट", "@text_reports": {"description": "Text from text_widgets: Reports", "context": "text_widgets"}, "text_passenger_feedback": "यात्री फीडबैक", "@text_passenger_feedback": {"description": "Text from text_widgets: Passenger Feedback", "context": "text_widgets"}, "text_rake_deficiency_report_1": "रेक कमी रिपोर्ट", "@text_rake_deficiency_report_1": {"description": "Text from text_widgets: Rake Deficiency Report", "context": "text_widgets"}, "text_obhs_to_mcc": "OBHS से MCC हैंडओवर", "@text_obhs_to_mcc": {"description": "Text from text_widgets: OBHS to MCC Handover", "context": "text_widgets"}, "text_mcc_to_obhs": "MCC से OBHS हैंडओवर", "@text_mcc_to_obhs": {"description": "Text from text_widgets: MCC to OBHS Handover", "context": "text_widgets"}, "text_upload_data": "डेटा अपलोड करें", "@text_upload_data": {"description": "Text from text_widgets: Upload data", "context": "text_widgets"}, "text_user_management": "उपयोगकर्ता प्रबंधन", "@text_user_management": {"description": "Text from text_widgets: User Management", "context": "text_widgets"}, "text_issue_management": "समस्या प्रबंधन", "@text_issue_management": {"description": "Text from text_widgets: Issue Management", "context": "text_widgets"}, "text_rail_sathi_qr": "रेल साथी QR", "@text_rail_sathi_qr": {"description": "Text from text_widgets: Rail Sathi Qr", "context": "text_widgets"}, "text_customer_care": "ग्राहक सेवा", "@text_customer_care": {"description": "Text from text_widgets: Customer Care", "context": "text_widgets"}, "text_reenter_otp": "OTP पुनः दर्ज करें", "@text_reenter_otp": {"description": "Text from text_widgets: Re-enter OTP", "context": "text_widgets"}, "text_deny": "अस्वीकार करें", "@text_deny": {"description": "Text from text_widgets: <PERSON>y", "context": "text_widgets"}, "text_enable": "सक्षम करें", "@text_enable": {"description": "Text from text_widgets: Enable", "context": "text_widgets"}, "text_location_access_required": "स्थान पहुंच आवश्यक", "@text_location_access_required": {"description": "Text from text_widgets: Location Access Required", "context": "text_widgets"}, "text_decline": "अस्वीकार करें", "@text_decline": {"description": "Text from text_widgets: Decline", "context": "text_widgets"}, "text_accept": "स्वीकार करें", "@text_accept": {"description": "Text from text_widgets: Accept", "context": "text_widgets"}, "text_confirm_delete": "हटाने की पुष्टि करें", "@text_confirm_delete": {"description": "Text from text_widgets: Confirm Delete", "context": "text_widgets"}, "text_cancel": "रद्<PERSON> करें", "@text_cancel": {"description": "Text from text_widgets: Cancel", "context": "text_widgets"}, "text_delete": "हटाएं", "@text_delete": {"description": "Text from text_widgets: Delete", "context": "text_widgets"}, "text_storage_permission_is": "फाइलें डाउनलोड करने के लिए स्टोरेज अनुमति आवश्यक है", "@text_storage_permission_is": {"description": "Text from text_widgets: Storage permission is required to download files", "context": "text_widgets"}, "text_please_select_a": "कृपया पहले एक ट्रेन चुनें", "@text_please_select_a": {"description": "Text from text_widgets: Please select a train first", "context": "text_widgets"}, "text_update_required": "अपडेट आवश्यक", "@text_update_required": {"description": "Text from text_widgets: Update Required", "context": "text_widgets"}, "text_update_now": "अभी अपडेट करें", "@text_update_now": {"description": "Text from text_widgets: Update Now", "context": "text_widgets"}, "text_please_select_a_1": "कृपया ट्रेन नंबर और तारीख चुनें।", "@text_please_select_a_1": {"description": "Text from text_widgets: Please select a train number and date.", "context": "text_widgets"}, "text_all_details_updated": "सभी विवरण सफलतापूर्वक अपडेट किए गए।", "@text_all_details_updated": {"description": "Text from text_widgets: All details updated successfully.", "context": "text_widgets"}, "text_failed_to_update": "विवरण अपडेट करने में विफल: $e", "@text_failed_to_update": {"description": "Text from text_widgets: Failed to update details: $e", "context": "text_widgets"}, "text_data_refreshed_successfully": "डेटा सफलतापूर्वक रीफ्रेश किया गया", "@text_data_refreshed_successfully": {"description": "Text from text_widgets: Data refreshed successfully", "context": "text_widgets"}, "text_train_location_saved": "ट्रेन स्थान सफलतापूर्वक सहेजा गया", "@text_train_location_saved": {"description": "Text from text_widgets: Train Location Saved Successfully", "context": "text_widgets"}, "text_self": "स्वयं", "@text_self": {"description": "Text from text_widgets: Self", "context": "text_widgets"}, "text_other_ca": "अन्य CA", "@text_other_ca": {"description": "Text from text_widgets: Other CA", "context": "text_widgets"}, "btn_accept": "स्वीकार करें", "@btn_accept": {"description": "Button text for accept action", "context": "button_labels"}, "btn_add_configuration": "कॉन्फ़िगरेशन जोड़ें", "@btn_add_configuration": {"description": "Button text for add configuration", "context": "button_labels"}, "btn_cancel": "रद्<PERSON> करें", "@btn_cancel": {"description": "Button text for cancel action", "context": "button_labels"}, "btn_close": "ब<PERSON><PERSON> करें", "@btn_close": {"description": "<PERSON><PERSON> text for close action", "context": "button_labels"}, "btn_coach_handover_report": "कोच हैंडओवर रिपोर्ट", "@btn_coach_handover_report": {"description": "But<PERSON> text for coach handover report", "context": "button_labels"}, "btn_coach_issue_status": "कोच समस्या स्थिति", "@btn_coach_issue_status": {"description": "<PERSON><PERSON> text for coach issue status", "context": "button_labels"}, "btn_completed": "पूर्ण", "@btn_completed": {"description": "Button text for completed status", "context": "button_labels"}, "btn_decline": "अस्वीकार करें", "@btn_decline": {"description": "Button text for decline action", "context": "button_labels"}, "btn_delete": "हटाएं", "@btn_delete": {"description": "Button text for delete action", "context": "button_labels"}, "btn_deny": "अस्वीकार करें", "@btn_deny": {"description": "Button text for deny action", "context": "button_labels"}, "btn_edit_configuration": "कॉन्फ़िगरेशन संपादित करें", "@btn_edit_configuration": {"description": "Button text for edit configuration", "context": "button_labels"}, "btn_enable": "सक्षम करें", "@btn_enable": {"description": "Button text for enable action", "context": "button_labels"}, "btn_error_snapshoterror": "त्रुटि हुई", "@btn_error_snapshoterror": {"description": "Button text for error occurred", "context": "button_labels"}, "btn_no_attendance_found": "कोई उपस्थिति नहीं मिली", "@btn_no_attendance_found": {"description": "Button text for no attendance found", "context": "button_labels"}, "btn_no_coaches_available": "कोई कोच उपलब्ध नहीं", "@btn_no_coaches_available": {"description": "Button text for no coaches available", "context": "button_labels"}, "btn_no_complaints_found": "कोई शिकायत नहीं मिली", "@btn_no_complaints_found": {"description": "Button text for no complaints found", "context": "button_labels"}, "btn_no_data_available": "कोई डेटा उपलब्ध नहीं", "@btn_no_data_available": {"description": "Button text for no data available", "context": "button_labels"}, "btn_no_data_available_1": "कोई डेटा उपलब्ध नहीं", "@btn_no_data_available_1": {"description": "Button text for no data available (variant)", "context": "button_labels"}, "btn_no_feedback_available": "कोई फीडबैक उपलब्ध नहीं", "@btn_no_feedback_available": {"description": "Button text for no feedback available", "context": "button_labels"}, "btn_no_location_data": "कोई स्थान डेटा नहीं", "@btn_no_location_data": {"description": "Button text for no location data", "context": "button_labels"}, "btn_no_notifications_found": "कोई सूचना नहीं मिली", "@btn_no_notifications_found": {"description": "Button text for no notifications found", "context": "button_labels"}, "btn_no_passengers_found": "कोई यात्री नहीं मिला", "@btn_no_passengers_found": {"description": "Button text for no passengers found", "context": "button_labels"}, "btn_no_reports_found": "कोई रिपोर्ट नहीं मिली", "@btn_no_reports_found": {"description": "Button text for no reports found", "context": "button_labels"}, "btn_no_trains_found": "कोई ट्रेन नहीं मिली", "@btn_no_trains_found": {"description": "Button text for no trains found", "context": "button_labels"}, "btn_ok": "ठीक है", "@btn_ok": {"description": "Button text for OK action", "context": "button_labels"}, "btn_refresh": "रीफ्रेश करें", "@btn_refresh": {"description": "Button text for refresh action", "context": "button_labels"}, "btn_retry": "पुनः प्रयास करें", "@btn_retry": {"description": "Button text for retry action", "context": "button_labels"}, "btn_save": "सहेजें", "@btn_save": {"description": "Button text for save action", "context": "button_labels"}, "btn_submit": "<PERSON><PERSON><PERSON> करें", "@btn_submit": {"description": "Button text for submit action", "context": "button_labels"}, "btn_update": "अपडेट करें", "@btn_update": {"description": "Button text for update action", "context": "button_labels"}, "form_train_number": "ट्रेन संख्या", "@form_train_number": {"description": "Form label for train number", "context": "form_labels"}, "form_date": "तारीख", "@form_date": {"description": "Form label for date", "context": "form_labels"}, "form_time": "समय", "@form_time": {"description": "Form label for time", "context": "form_labels"}, "form_station": "स्टेशन", "@form_station": {"description": "Form label for station", "context": "form_labels"}, "form_coach": "कोच", "@form_coach": {"description": "Form label for coach", "context": "form_labels"}, "form_seat": "सीट", "@form_seat": {"description": "Form label for seat", "context": "form_labels"}, "form_passenger_name": "यात्री का नाम", "@form_passenger_name": {"description": "Form label for passenger name", "context": "form_labels"}, "form_mobile_number": "मोबाइल नंबर", "@form_mobile_number": {"description": "Form label for mobile number", "context": "form_labels"}, "form_email_address": "ईमेल पता", "@form_email_address": {"description": "Form label for email address", "context": "form_labels"}, "form_remarks": "टिप्पणी", "@form_remarks": {"description": "Form label for remarks", "context": "form_labels"}}