{"@@locale": "ta", "appTitle": "ரெயில்ஆப்ஸ்", "@appTitle": {"description": "The title of the application"}, "cancel": "", "confirm": "", "delete": "", "edit": "", "error": "", "home": "", "loading": "", "login": "உள்நুழைவு", "@login": {"description": "Login button text"}, "logout": "", "next": "", "no": "", "ok": "", "password": "", "previous": "", "refresh": "", "retry": "", "save": "", "search": "", "settings": "", "submit": "", "username": "", "welcome": "ரெயில்ஆப்ஸ்க்கு உங்களை வரவேற்கிறோம்", "@welcome": {"description": "Welcome message displayed on home screen"}, "yes": "", "test_welcome": "வரவேற்கிறோம்", "@test_welcome": {"description": "Welcome message for testing", "context": "test_context"}, "test_login": "உள்நுழைவு", "@test_login": {"description": "Login button text", "context": "button_labels"}, "text_menu": "மெனு", "@text_menu": {"description": "Text for drawer menu header", "context": "custom_drawer"}, "text_train_tracker": "ரயில் டிராக்கர்", "@text_train_tracker": {"description": "Text from text_widgets: Train Tracker", "context": "text_widgets"}, "text_assign_ca": "CA ஒதுக்கீடு", "@text_assign_ca": {"description": "Text from text_widgets: Assign CA", "context": "text_widgets"}, "text_assign_cs": "CS ஒதுக்கீடு", "@text_assign_cs": {"description": "Text from text_widgets: Assign CS", "context": "text_widgets"}, "text_pnr_details": "PNR விவரங்கள்", "@text_pnr_details": {"description": "Text from text_widgets: PNR Details", "context": "text_widgets"}, "text_passenger_chart": "பயணிகள் அட்டவணை", "@text_passenger_chart": {"description": "Text from text_widgets: Passenger Chart", "context": "text_widgets"}, "text_map_screen": "வரைபட திரை", "@text_map_screen": {"description": "Text from text_widgets: Map Screen", "context": "text_widgets"}, "text_configuration": "கட்டமைப்பு", "@text_configuration": {"description": "Text from text_widgets: Configuration", "context": "text_widgets"}, "text_reports": "அறிக்கைகள்", "@text_reports": {"description": "Text from text_widgets: Reports", "context": "text_widgets"}, "text_passenger_feedback": "பயணிகள் கருத்து", "@text_passenger_feedback": {"description": "Text from text_widgets: Passenger Feedback", "context": "text_widgets"}, "text_rake_deficiency_report_1": "ரேக் குறைபாடு அறிக்கை", "@text_rake_deficiency_report_1": {"description": "Text from text_widgets: Rake Deficiency Report", "context": "text_widgets"}, "text_obhs_to_mcc": "OBHS இலிருந்து MCC ஒப்படைப்பு", "@text_obhs_to_mcc": {"description": "Text from text_widgets: OBHS to MCC Handover", "context": "text_widgets"}, "text_mcc_to_obhs": "MCC இலிருந்து OBHS ஒப்படைப்பு", "@text_mcc_to_obhs": {"description": "Text from text_widgets: MCC to OBHS Handover", "context": "text_widgets"}, "text_upload_data": "தரவு பதிவேற்றம்", "@text_upload_data": {"description": "Text from text_widgets: Upload data", "context": "text_widgets"}, "text_user_management": "பயனர் மேலாண்மை", "@text_user_management": {"description": "Text from text_widgets: User Management", "context": "text_widgets"}, "text_issue_management": "சிக்கல் மேலாண்மை", "@text_issue_management": {"description": "Text from text_widgets: Issue Management", "context": "text_widgets"}, "text_rail_sathi_qr": "ரயில் சாதி QR", "@text_rail_sathi_qr": {"description": "Text from text_widgets: Rail Sathi Qr", "context": "text_widgets"}, "text_customer_care": "வாடிக்கையாளர் சேவை", "@text_customer_care": {"description": "Text from text_widgets: Customer Care", "context": "text_widgets"}, "text_change_mobile": "மொபைலை மாற்றவும்", "@text_change_mobile": {"description": "Profile screen translation for: text_change_mobile", "context": "profile_screen"}, "text_change_whatsapp": "வாட்ஸ்அப் எண்ணை மாற்றவும்", "@text_change_whatsapp": {"description": "Profile screen translation for: text_change_whatsapp", "context": "profile_screen"}, "text_alert": "எச்சரிக்கை", "@text_alert": {"description": "Profile screen translation for: text_alert", "context": "profile_screen"}, "text_close": "மூடவும்", "@text_close": {"description": "Profile screen translation for: text_close", "context": "profile_screen"}, "text_change_your_email": "உங்கள் மின்னஞ்சலை மாற்றவும்", "@text_change_your_email": {"description": "Profile screen translation for: text_change_your_email", "context": "profile_screen"}, "text_current_email": "தற்போதைய மின்னஞ்சல்", "@text_current_email": {"description": "Profile screen translation for: text_current_email", "context": "profile_screen"}, "text_new_email": "புதிய மின்னஞ்சல்", "@text_new_email": {"description": "Profile screen translation for: text_new_email", "context": "profile_screen"}, "text_please_enter_new_email": "தயவுசெய்து புதிய மின்னஞ்சலை உள்ளிடவும்", "@text_please_enter_new_email": {"description": "Profile screen translation for: text_please_enter_new_email", "context": "profile_screen"}, "text_otp": "ஓடிபி", "@text_otp": {"description": "Profile screen translation for: text_otp", "context": "profile_screen"}, "text_resend_otp": "ஓடிபியை மீண்டும் அனுப்பவும்", "@text_resend_otp": {"description": "Profile screen translation for: text_resend_otp", "context": "profile_screen"}, "text_resend_in_seconds": "{seconds} வினாடிகளில் மீண்டும் அனுப்பவும்", "@text_resend_in_seconds": {"description": "Profile screen translation for: text_resend_in_seconds", "context": "profile_screen"}, "text_verify_otp": "ஓடிபியை சரிபார்க்கவும்", "@text_verify_otp": {"description": "Profile screen translation for: text_verify_otp", "context": "profile_screen"}, "text_generate_otp": "ஓடிபியை உருவாக்கவும்", "@text_generate_otp": {"description": "Profile screen translation for: text_generate_otp", "context": "profile_screen"}, "text_change_your_password": "உங்கள் கடவுச்சொல்லை மாற்றவும்", "@text_change_your_password": {"description": "Profile screen translation for: text_change_your_password", "context": "profile_screen"}, "text_old_password": "பழைய கடவுச்சொல்", "@text_old_password": {"description": "Profile screen translation for: text_old_password", "context": "profile_screen"}, "text_new_password": "புதிய கடவுச்சொல்", "@text_new_password": {"description": "Profile screen translation for: text_new_password", "context": "profile_screen"}, "text_confirm_new_password": "புதிய கடவுச்சொல்லை உறுதிப்படுத்தவும்", "@text_confirm_new_password": {"description": "Profile screen translation for: text_confirm_new_password", "context": "profile_screen"}, "text_please_enter_otp": "தயவுசெய்து ஓடிபியை உள்ளிடவும்", "@text_please_enter_otp": {"description": "Profile screen translation for: text_please_enter_otp", "context": "profile_screen"}, "text_send_mobile_otp": "மொபைல் ஓடிபியை அனுப்பவும்", "@text_send_mobile_otp": {"description": "Profile screen translation for: text_send_mobile_otp", "context": "profile_screen"}, "text_send_email_otp": "மின்னஞ்சல் ஓடிபியை அனுப்பவும்", "@text_send_email_otp": {"description": "Profile screen translation for: text_send_email_otp", "context": "profile_screen"}, "text_please_enter_value": "தயவுசெய்து ஒரு மதிப்பை உள்ளிடவும்", "@text_please_enter_value": {"description": "Profile screen translation for: text_please_enter_value", "context": "profile_screen"}, "text_please_enter_valid_mobile": "தயவுசெய்து சரியான மொபைல் எண்ணை உள்ளிடவும்", "@text_please_enter_valid_mobile": {"description": "Profile screen translation for: text_please_enter_valid_mobile", "context": "profile_screen"}, "text_success": "வெற்றி", "@text_success": {"description": "Profile screen translation for: text_success", "context": "profile_screen"}, "text_ok": "சரி", "@text_ok": {"description": "Profile screen translation for: text_ok", "context": "profile_screen"}, "text_change_your_mobile_number": "உங்கள் மொபைல் எண்ணை மாற்றவும்", "@text_change_your_mobile_number": {"description": "Profile screen translation for: text_change_your_mobile_number", "context": "profile_screen"}, "text_current_mobile_number": "தற்போதைய மொபைல் எண்", "@text_current_mobile_number": {"description": "Profile screen translation for: text_current_mobile_number", "context": "profile_screen"}, "text_new_mobile_number": "புதிய மொபைல் எண்", "@text_new_mobile_number": {"description": "Profile screen translation for: text_new_mobile_number", "context": "profile_screen"}, "text_please_enter_new_mobile": "தயவுசெய்து புதிய மொபைல் எண்ணை உள்ளிடவும்", "@text_please_enter_new_mobile": {"description": "Profile screen translation for: text_please_enter_new_mobile", "context": "profile_screen"}, "text_change_your_whatsapp_number": "உங்கள் வாட்ஸ்அப் எண்ணை மாற்றவும்", "@text_change_your_whatsapp_number": {"description": "Profile screen translation for: text_change_your_whatsapp_number", "context": "profile_screen"}, "text_current_whatsapp_number": "தற்போதைய வாட்ஸ்அப் எண்", "@text_current_whatsapp_number": {"description": "Profile screen translation for: text_current_whatsapp_number", "context": "profile_screen"}, "text_new_whatsapp_number": "புதிய வாட்ஸ்அப் எண்", "@text_new_whatsapp_number": {"description": "Profile screen translation for: text_new_whatsapp_number", "context": "profile_screen"}, "text_please_enter_new_whatsapp": "தயவுசெய்து புதிய வாட்ஸ்அப் மொபைல் எண்ணை உள்ளிடவும்", "@text_please_enter_new_whatsapp": {"description": "Profile screen translation for: text_please_enter_new_whatsapp", "context": "profile_screen"}, "text_train": "ரயில்", "@text_train": {"description": "Table column header for train", "context": "add_train_screen"}, "text_coaches": "கோச்", "@text_coaches": {"description": "Table column header for coaches", "context": "add_train_screen"}, "text_origin_date": "மூல தேதி", "@text_origin_date": {"description": "Table column header for date", "context": "add_train_screen"}, "text_na": "கிடைக்கவில்லை", "@text_na": {"description": "Table column header for na", "context": "add_train_screen"}, "text_send_otp": "OTP அனுப்பவும்", "@text_send_otp": {"description": "Send OTP button text", "context": "change_email_modal"}, "text_failed_to_send_otp": "OTP அனுப்புவதில் தோல்வி: {error}", "@text_failed_to_send_otp": {"description": "Error message when OTP sending fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_email_saved_successfully": "மின்னஞ்சல் வெற்றிகரமாக சேமிக்கப்பட்டது!", "@text_email_saved_successfully": {"description": "Success message when email is saved", "context": "change_email_modal"}, "text_failed_to_verify_otp": "OTP சரிபார்ப்பதில் தோல்வி: {error}", "@text_failed_to_verify_otp": {"description": "Error message when OTP verification fails", "context": "change_email_modal", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "text_cancel": "ரத்து செய்", "@text_cancel": {"description": "Cancel button text", "context": "general"}, "rm_feedback_app_bar_title": "ரெயில்மதத் பயணிகள் கருத்து", "rm_feedback_main_title": "ரெயில்மதத் கருத்து", "form_pnr_number": "PNR எண் *", "form_crn_number": "CRN எண்*", "form_train_no": "ரயில் எண் *", "form_train_name": "ரயிலின் பெயர் *", "form_passenger_name": "பயணியின் பெயர் *", "form_coach_no": "கோச் எண் *", "form_berth_no": "பெர்த் எண் *", "form_mobile_number": "மொபைல் எண் *", "form_email_id": "மின்னஞ்சல் ஐடி", "form_issue_type": "பிரச்சினையின் வகை", "form_sub_issue_type": "துணை பிரச்சினையின் வகை", "form_resolved_status": "தீர்க்கப்பட்டது (ஆம்/இல்லை) *", "form_marks": "மதிப்பெண்கள் (1 முதல் 10) *", "form_task_status": "பணியின் நிலை *", "form_remarks": "பயணியின் கருத்துகள்", "btn_validate": "சரிபார்க்கவும்", "btn_verified": "சரிபார்க்கப்பட்டது", "btn_verify_email": "மின்னஞ்சலை சரிபார்க்கவும்", "btn_verify_otp": "OTP சரிபார்க்கவும்", "btn_submit_feedback": "கருத்தை சமர்ப்பிக்கவும்", "btn_upload_pnr_image": "PNR படத்தை பதிவேற்றவும்", "btn_pick_media": "கருத்துக்காக படங்கள்/வீடியோக்களை தேர்ந்தெடுக்கவும்", "btn_camera": "கேமரா", "btn_gallery": "கேலரி", "btn_image": "படம்", "btn_video": "வீடியோ", "btn_i_understand": "நான் புரிந்துகொண்டேன்", "btn_ok": "சரி", "status_verified": "சரிபார்க்கப்பட்டது", "status_pending": "நிலுவையில்", "status_completed": "முடிந்தது", "status_yes": "ஆம்", "status_no": "இல்லை", "status_select": "தேர்ந்தெடுக்கவும்", "section_email_verification": "மின்னஞ்சல் சரிபார்ப்பு (விருப்பமானது)", "section_selected_images": "தேர்ந்தெடுக்கப்பட்ட படங்கள்:", "section_selected_videos": "தேர்ந்தெடுக்கப்பட்ட வீடியோக்கள்:", "dialog_email_verification_info": "மின்னஞ்சல் சரிபார்ப்பு தகவல்", "dialog_select_media_type": "மீடியா வகையை தேர்ந்தெடுக்கவும்", "validation_fill_all_fields": "தயவுசெய்து அனைத்து புலங்களையும் சரியான தகவலுடன் நிரப்பவும்.", "validation_pnr_digits": "PNR எண் 8 அல்லது 10 இலக்கங்களாக இருக்க வேண்டும்", "validation_berth_number": "பெர்த் எண் சரியான எண்ணாக இருக்க வேண்டும்", "validation_feedback_length": "கருத்து 100 எழுத்துக்களுக்கு மேல் இருக்க முடியாது", "validation_email_required": "தயவுசெய்து சரியான மின்னஞ்சல் ஐடியை உள்ளிடவும்.", "validation_otp_required": "தயவுசெய்து OTP உள்ளிடவும்.", "validation_train_no_required": "ரயில் எண் தேவை", "validation_train_name_required": "ரயிலின் பெயர் தேவை", "validation_passenger_name_required": "பயணியின் பெயர் தேவை", "validation_mobile_required": "மொபைல் எண் தேவை", "validation_mobile_digits": "மொபைல் எண் 10 இலக்கங்களாக இருக்க வேண்டும்", "validation_issue_type_required": "தயவுசெய்து பிரச்சினையின் வகையை தேர்ந்தெடுக்கவும்", "validation_sub_issue_required": "தயவுசெய்து துணை-பிரச்சினையின் வகையை தேர்ந்தெடுக்கவும்", "validation_resolved_required": "தயவுசெய்து தீர்வின் நிலையை தேர்ந்தெடுக்கவும்", "validation_marks_required": "தயவுசெய்து மதிப்பெண்களை தேர்ந்தெடுக்கவும்", "msg_pnr_images_limit": "நீங்கள் 3 PNR படங்களை மட்டுமே தேர்ந்தெடுக்க முடியும்", "msg_feedback_images_limit": "அதிகபட்சம் 3 கருத்து படங்களுக்கு அனுமதி உள்ளது", "msg_images_added_limit": "வெறும் {count} படங்கள் சேர்க்கப்பட்டன. அதிகபட்சம் 3 இன் வரம்பு அடைந்தது.", "msg_error_picking_media": "மீடியா தேர்ந்தெடுப்பதில் பிழை: {error}", "msg_failed_fetch_train_name": "ரயிலின் பெயரை கொண்டுவருவதில் தோல்வி", "msg_invalid_pnr": "தவறான PNR எண்.", "msg_pnr_success": "PNR விவரங்கள் வெற்றிகரமாக கொண்டுவரப்பட்டன.", "msg_pnr_validation_failed": "PNR விவரங்களை சரிபார்ப்பதில் தோல்வி. தவறான PNR எண்.", "msg_email_verification_sent": "மின்னஞ்சல் சரிபார்ப்பு தொடங்கப்பட்டது. தயவுசெய்து உங்கள் இன்பாக்ஸ் மற்றும் ஸ்பேம் ஃபோல்டர் இரண்டையும் சரிபார்க்கவும்..", "msg_otp_verified": "OTP வெற்றிகரமாக சரிபார்க்கப்பட்டது.", "msg_feedback_submitted": "கருத்து வெற்றிகரமாக சமர்ப்பிக்கப்பட்டது!", "msg_feedback_failed": "கருத்தை சமர்ப்பிப்பதில் தோல்வி", "msg_unexpected_error": "எதிர்பாராத பிழை ஏற்பட்டது. தயவுசெய்து மீண்டும் முயற்சிக்கவும்.", "info_spam_folder_note": "தயவுசெய்து கவனிக்கவும் சரிபார்ப்பு மின்னஞ்சல்கள் சில நேரங்களில் உங்கள் ஸ்பேம்/ஜங்க் ஃபோல்டரில் வரலாம்.", "info_after_requesting_otp": "OTP கோரிய பிறகு:", "info_check_inbox": "முதலில் உங்கள் இன்பாக்ஸை சரிபார்க்கவும்", "info_check_spam": "கிடைக்கவில்லை என்றால், ஸ்பேம்/ஜங்க் ஃபோல்டரை சரிபார்க்கவும்", "info_add_safe_sender": "எங்கள் டொமைனை உங்கள் பாதுகாப்பான அனுப்புநர் பட்டியலில் சேர்க்கவும்", "text_no_feedback_images": "எந்த கருத்து படங்களும் தேர்ந்தெடுக்கப்படவில்லை", "text_no_pnr_images": "எந்த PNR படங்களும் தேர்ந்தெடுக்கப்படவில்லை", "text_character_count": "{count}/100 எழுத்துக்கள்", "loading_sending_otp": "OTP அனுப்பப்படுகிறது", "loading_verifying_otp": "OTP சரிபார்க்கப்படுகிறது", "loading_submitting_feedback": "கருத்து சமர்ப்பிக்கப்படுகிறது", "attendance_api_summary": "API சுருக்கம் விவரங்கள்", "@attendance_api_summary": {"description": "Attendance: API Summary Details", "context": "attendance"}, "attendance_assigned_coaches": "🚆 ஒதுக்கப்பட்டது Coaches:", "@attendance_assigned_coaches": {"description": "Attendance: 🚆 Assigned Coaches:", "context": "attendance"}, "attendance_available_label": "கிடைக்கிறது: {count}", "@attendance_available_label": {"description": "Attendance: Available: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_available_section": "கிடைக்கிறது", "@attendance_available_section": {"description": "Attendance: Available", "context": "attendance"}, "attendance_berths_count": "{count} berths", "@attendance_berths_count": {"description": "Attendance: {count} berths", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_buffer_time_restriction": "The நேரம் between the current நேரம் and the ரயில்'s arrival நேரம் is not within the 2-hour buffer.", "@attendance_buffer_time_restriction": {"description": "Attendance: The time between the current time and the train's arrival time is not within the 2-hour buffer.", "context": "attendance"}, "attendance_cancel": "ரத்துசெய்", "@attendance_cancel": {"description": "Attendance: Cancel", "context": "attendance"}, "attendance_cancelled": "Cancelled", "@attendance_cancelled": {"description": "Attendance: Cancelled", "context": "attendance"}, "attendance_chart_not_prepared": "பட்டியல் has not been prepared for this நிலையம்", "@attendance_chart_not_prepared": {"description": "Attendance: Chart has not been prepared for this station", "context": "attendance"}, "attendance_charting_refreshed": "பட்டியலிடல் refreshed at: {time}", "@attendance_charting_refreshed": {"description": "Attendance: Charting refreshed at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_started": "பட்டியலிடல் started at: {time}", "@attendance_charting_started": {"description": "Attendance: Charting started at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_time": "பட்டியலிடல் நேரம்: {time}", "@attendance_charting_time": {"description": "Attendance: Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_click_more": "click for more...", "@attendance_click_more": {"description": "Attendance: click for more...", "context": "attendance"}, "attendance_coach_label": "🚃 பெட்டி: {coach}", "@attendance_coach_label": {"description": "Attendance: 🚃 Coach: {coach}", "context": "attendance", "placeholders": {"coach": {"type": "String"}}}, "attendance_coach_occupancy": "பெட்டி Occupancy விவரங்கள்", "@attendance_coach_occupancy": {"description": "Attendance: Coach Occupancy <PERSON>", "context": "attendance"}, "attendance_coach_type": "பெட்டி Type:", "@attendance_coach_type": {"description": "Attendance: Coach Type:", "context": "attendance"}, "attendance_coaches": "Coaches: {coaches}", "@attendance_coaches": {"description": "Attendance: Coaches: {coaches}", "context": "attendance", "placeholders": {"coaches": {"type": "String"}}}, "attendance_concise_view": "Concise காண்க", "@attendance_concise_view": {"description": "Attendance: Concise View", "context": "attendance"}, "attendance_count_label": "A: {count}", "@attendance_count_label": {"description": "Attendance: A: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_daily": "Daily", "@attendance_daily": {"description": "Attendance: Daily", "context": "attendance"}, "attendance_data_refreshed": "தரவு refreshed வெற்றிகரமாக", "@attendance_data_refreshed": {"description": "Attendance: Data refreshed successfully", "context": "attendance"}, "attendance_deboarding": "🔴 இறங்குதல்:", "@attendance_deboarding": {"description": "Attendance: 🔴 Deboarding:", "context": "attendance"}, "attendance_deboarding_none": "🔴 இறங்குதல்: None", "@attendance_deboarding_none": {"description": "Attendance: 🔴 Deboarding: None", "context": "attendance"}, "attendance_detailed_view": "Detailed காண்க", "@attendance_detailed_view": {"description": "Attendance: Detailed View", "context": "attendance"}, "attendance_ehk_assigned": "EHK ஒதுக்கப்பட்டது for ரயில்:{ehkName}", "@attendance_ehk_assigned": {"description": "Attendance: EHK Assigned for train:{ehkName}", "context": "attendance", "placeholders": {"ehkName": {"type": "String"}}}, "attendance_expected_charting": "Expected பட்டியலிடல் நேரம்: {time}", "@attendance_expected_charting": {"description": "Attendance: Expected Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_failed_load_data": "தோல்வி to load detailed தரவு: {error}", "@attendance_failed_load_data": {"description": "Attendance: Failed to load detailed data: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_failed_update_status": "தோல்வி to புதுப்பிக்கவும் ரயில் நிலை", "@attendance_failed_update_status": {"description": "Attendance: Failed to update train status", "context": "attendance"}, "attendance_go": "செல்லவும்", "@attendance_go": {"description": "Attendance: Go", "context": "attendance"}, "attendance_in_route": "In-route", "@attendance_in_route": {"description": "Attendance: In-route", "context": "attendance"}, "attendance_inside": "உள்ளே", "@attendance_inside": {"description": "Attendance: Inside", "context": "attendance"}, "attendance_inside_train": "You are now குறிக்கப்பட்டது as உள்ளே the ரயில்", "@attendance_inside_train": {"description": "Attendance: You are now marked as inside the train", "context": "attendance"}, "attendance_journey_status_updated": "பயணம் நிலை updated to {status}", "@attendance_journey_status_updated": {"description": "Attendance: Journey status updated to {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_last_fetched": "Last fetched: {time}", "@attendance_last_fetched": {"description": "Attendance: Last fetched: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_loading": "ஏற்றப்படுகிறது...", "@attendance_loading": {"description": "Attendance: Loading...", "context": "attendance"}, "attendance_location_not_fetched": "ரயில் இடம் is not fetched yet, தயவுசெய்து try again later", "@attendance_location_not_fetched": {"description": "Attendance: Train Location is not fetched yet, please try again later", "context": "attendance"}, "attendance_na": "N/A", "@attendance_na": {"description": "Attendance: N/A", "context": "attendance"}, "attendance_near_stations": "You're near the following நிலையம்(s):", "@attendance_near_stations": {"description": "Attendance: You're near the following station(s):", "context": "attendance"}, "attendance_nearby_station_alert": "🛤️ அருகிலுள்ள நிலையம் எச்சரிக்கை", "@attendance_nearby_station_alert": {"description": "Attendance: 🛤️ Nearby Station Alert", "context": "attendance"}, "attendance_non_sleeper": "Non-Sleeper", "@attendance_non_sleeper": {"description": "Attendance: Non-Sleeper", "context": "attendance"}, "attendance_not_attendance_station": "வருகை cannot be குறிக்கப்பட்டது for நிலையம் {stationCode} as it is not an வருகை நிலையம்.", "@attendance_not_attendance_station": {"description": "Attendance: Attendance cannot be marked for station {stationCode} as it is not an attendance station.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_not_inside_train": "You are not உள்ளே the ரயில். தயவுசெய்து செல்லவும் உள்ளே the ரயில் first.", "@attendance_not_inside_train": {"description": "Attendance: You are not inside the train. Please go inside the train first.", "context": "attendance"}, "attendance_off_label": "Off: {count}", "@attendance_off_label": {"description": "Attendance: Off: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_offboarding_section": "Offboarding", "@attendance_offboarding_section": {"description": "Attendance: Offboarding", "context": "attendance"}, "attendance_ok": "சரி", "@attendance_ok": {"description": "Attendance: OK", "context": "attendance"}, "attendance_on_label": "On: {count}", "@attendance_on_label": {"description": "Attendance: On: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_onboarding": "🟢 ஏறுதல்:", "@attendance_onboarding": {"description": "Attendance: 🟢 Onboarding:", "context": "attendance"}, "attendance_onboarding_none": "🟢 ஏறுதல்: None", "@attendance_onboarding_none": {"description": "Attendance: 🟢 Onboarding: None", "context": "attendance"}, "attendance_onboarding_section": "ஏறுதல்", "@attendance_onboarding_section": {"description": "Attendance: Onboarding", "context": "attendance"}, "attendance_other_ca": "Other CA", "@attendance_other_ca": {"description": "Attendance: Other CA", "context": "attendance"}, "attendance_other_ehk": "Other EHK/OBHS", "@attendance_other_ehk": {"description": "Attendance: Other EHK/OBHS", "context": "attendance"}, "attendance_outside_train": "You are now குறிக்கப்பட்டது as வெளியே the ரயில்", "@attendance_outside_train": {"description": "Attendance: You are now marked as outside the train", "context": "attendance"}, "attendance_passenger_chart": "பயணி பட்டியல்   Atten..", "@attendance_passenger_chart": {"description": "Attendance: Passenger Chart   Atten..", "context": "attendance"}, "attendance_refresh_failed": "புதுப்பிப்பு தோல்வி: {error}", "@attendance_refresh_failed": {"description": "Attendance: Refresh failed: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_screen_title": "வருகை", "@attendance_screen_title": {"description": "Attendance: Attendance", "context": "attendance"}, "attendance_select_date": "தேர்ந்தெடுக்கவும் தேதி", "@attendance_select_date": {"description": "Attendance: Select date", "context": "attendance"}, "attendance_select_train_date": "தயவுசெய்து தேர்ந்தெடுக்கவும் a ரயில் எண் and தேதி.", "@attendance_select_train_date": {"description": "Attendance: Please select a train number and date.", "context": "attendance"}, "attendance_select_train_first": "தயவுசெய்து தேர்ந்தெடுக்கவும் a ரயில் first", "@attendance_select_train_first": {"description": "Attendance: Please select a train first", "context": "attendance"}, "attendance_self": "Self", "@attendance_self": {"description": "Attendance: Self", "context": "attendance"}, "attendance_sleeper": "Sleeper", "@attendance_sleeper": {"description": "Attendance: Sleeper", "context": "attendance"}, "attendance_station_details": "நிலையம் விவரங்கள் - {stationCode}", "@attendance_station_details": {"description": "Attendance: Station Details - {stationCode}", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_stoppages": "Stoppages", "@attendance_stoppages": {"description": "Attendance: Stoppages", "context": "attendance"}, "attendance_timings": "Tim<PERSON>", "@attendance_timings": {"description": "Attendance: Timings", "context": "attendance"}, "attendance_today": "Today", "@attendance_today": {"description": "Attendance: Today", "context": "attendance"}, "attendance_too_far_from_station": "You're over 50 KM away from the selected நிலையம் {stationCode}. வருகை can only be குறிக்கப்பட்டது when you're within the allowed range.", "@attendance_too_far_from_station": {"description": "Attendance: You're over 50 KM away from the selected station {stationCode}. Attendance can only be marked when you're within the allowed range.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_train": "ரயில்", "@attendance_train": {"description": "Attendance: Train", "context": "attendance"}, "attendance_train_date": "ரயில் {trainNo} - {date}", "@attendance_train_date": {"description": "Attendance: Train {trainNo} - {date}", "context": "attendance", "placeholders": {"trainNo": {"type": "String"}, "date": {"type": "String"}}}, "attendance_train_depot": "ரயில் கிடங்கு:{depot}", "@attendance_train_depot": {"description": "Attendance: Train Depot:{depot}", "context": "attendance", "placeholders": {"depot": {"type": "String"}}}, "attendance_train_not_running": "ரயில் Not Running", "@attendance_train_not_running": {"description": "Attendance: Train Not Running", "context": "attendance"}, "attendance_train_not_running_message": "ரயில் {trainNumber} is NOT running on {dayOfWeek}", "@attendance_train_not_running_message": {"description": "Attendance: Train {trainNumber} is NOT running on {dayOfWeek}", "context": "attendance", "placeholders": {"trainNumber": {"type": "String"}, "dayOfWeek": {"type": "String"}}}, "attendance_update": "புதுப்பிக்கவும்", "@attendance_update": {"description": "Attendance: Update", "context": "attendance"}, "attendance_update_journey_status": "புதுப்பிக்கவும் பயணம் நிலை", "@attendance_update_journey_status": {"description": "Attendance: Update Journey Status", "context": "attendance"}, "attendance_update_message": "A new பதிப்பு of the app is கிடைக்கிறது. You must புதுப்பிக்கவும் to continue using the app.", "@attendance_update_message": {"description": "Attendance: A new version of the app is available. You must update to continue using the app.", "context": "attendance"}, "attendance_update_now": "புதுப்பிக்கவும் Now", "@attendance_update_now": {"description": "Attendance: Update Now", "context": "attendance"}, "attendance_update_required": "புதுப்பிக்கவும் தேவை", "@attendance_update_required": {"description": "Attendance: Update Required", "context": "attendance"}, "attendance_user_location": "பயனர் இடம்:{locationStatus}", "@attendance_user_location": {"description": "Attendance: User Location:{locationStatus}", "context": "attendance", "placeholders": {"locationStatus": {"type": "String"}}}, "attendance_details_distance": "தூரம்: {distance} km", "@attendance_details_distance": {"description": "Attendance: Distance: {distance} km", "context": "attendance", "placeholders": {"distance": {"type": "String"}}}, "attendance_details_error": "பிழை: {error}", "@attendance_details_error": {"description": "Attendance: Error: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_details_journey_date": "பயணம் தேதி:", "@attendance_details_journey_date": {"description": "Attendance: Journey Date:", "context": "attendance"}, "attendance_details_latitude": "latitude: {latitude}", "@attendance_details_latitude": {"description": "Attendance: latitude: {latitude}", "context": "attendance", "placeholders": {"latitude": {"type": "String"}}}, "attendance_details_longitude": "longitude: {longitude}", "@attendance_details_longitude": {"description": "Attendance: longitude: {longitude}", "context": "attendance", "placeholders": {"longitude": {"type": "String"}}}, "attendance_details_match_percentage": "Match சதவீதம்: {percentage}", "@attendance_details_match_percentage": {"description": "Attendance: Match Percentage: {percentage}", "context": "attendance", "placeholders": {"percentage": {"type": "String"}}}, "attendance_details_nearest_station": "nearest நிலையம்: {station}", "@attendance_details_nearest_station": {"description": "Attendance: nearest Station: {station}", "context": "attendance", "placeholders": {"station": {"type": "String"}}}, "attendance_details_no_attendance": "No வருகை found.", "@attendance_details_no_attendance": {"description": "Attendance: No attendance found.", "context": "attendance"}, "attendance_details_no_data": "No தரவு கிடைக்கிறது.", "@attendance_details_no_data": {"description": "Attendance: No data available.", "context": "attendance"}, "attendance_details_no_human_detected": "No human detected", "@attendance_details_no_human_detected": {"description": "Attendance: No human detected", "context": "attendance"}, "attendance_details_station_code": "நிலையம் Code:", "@attendance_details_station_code": {"description": "Attendance: Station Code:", "context": "attendance"}, "attendance_details_status": "நிலை: {status}", "@attendance_details_status": {"description": "Attendance: Status: {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_details_status_marked": "குறிக்கப்பட்டது", "@attendance_details_status_marked": {"description": "Attendance: Marked", "context": "attendance"}, "attendance_details_status_pending": "Pending", "@attendance_details_status_pending": {"description": "Attendance: Pending", "context": "attendance"}, "attendance_details_title": "வருகை விவரங்கள்", "@attendance_details_title": {"description": "Attendance: Attendance Details", "context": "attendance"}, "attendance_details_train_number": "ரயில் எண்:", "@attendance_details_train_number": {"description": "Attendance: Train Number:", "context": "attendance"}, "attendance_details_updated": "All விவரங்கள் updated வெற்றிகரமாக.", "@attendance_details_updated": {"description": "Attendance: All details updated successfully.", "context": "attendance"}, "attendance_details_updated_at": "Updated At: {updatedAt}", "@attendance_details_updated_at": {"description": "Attendance: Updated At: {updatedAt}", "context": "attendance", "placeholders": {"updatedAt": {"type": "String"}}}, "attendance_details_updated_by": "Updated By: {updatedBy}", "@attendance_details_updated_by": {"description": "Attendance: Updated By: {updatedBy}", "context": "attendance", "placeholders": {"updatedBy": {"type": "String"}}}, "attendance_details_username": "Username: {username}", "@attendance_details_username": {"description": "Attendance: Username: {username}", "context": "attendance", "placeholders": {"username": {"type": "String"}}}, "btn_no_attendance_found": "No வருகை found.", "@btn_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}, "text_attendance_already_submitted": "வருகை Already Submitted", "@text_attendance_already_submitted": {"description": "Attendance: Attendance Already Submitted", "context": "attendance"}, "text_attendance_marked_successfully": "வருகை குறிக்கப்பட்டது வெற்றிகரமாக!", "@text_attendance_marked_successfully": {"description": "Attendance: Attendance marked successfully!", "context": "attendance"}, "text_no_attendance_found": "No வருகை found.", "@text_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}}