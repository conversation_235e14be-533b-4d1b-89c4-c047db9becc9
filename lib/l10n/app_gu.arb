{"@@locale": "gu", "attendance_api_summary": "API સારાંશ વિગતો", "@attendance_api_summary": {"description": "Attendance: API Summary Details", "context": "attendance"}, "attendance_assigned_coaches": "🚆 સોંપેલ Coaches:", "@attendance_assigned_coaches": {"description": "Attendance: 🚆 Assigned Coaches:", "context": "attendance"}, "attendance_available_label": "ઉપલબ્ધ: {count}", "@attendance_available_label": {"description": "Attendance: Available: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_available_section": "ઉપલબ્ધ", "@attendance_available_section": {"description": "Attendance: Available", "context": "attendance"}, "attendance_berths_count": "{count} berths", "@attendance_berths_count": {"description": "Attendance: {count} berths", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_buffer_time_restriction": "The સમય between the current સમય and the ટ્રેન's arrival સમય is not within the 2-hour buffer.", "@attendance_buffer_time_restriction": {"description": "Attendance: The time between the current time and the train's arrival time is not within the 2-hour buffer.", "context": "attendance"}, "attendance_cancel": "રદ કરો", "@attendance_cancel": {"description": "Attendance: Cancel", "context": "attendance"}, "attendance_cancelled": "Cancelled", "@attendance_cancelled": {"description": "Attendance: Cancelled", "context": "attendance"}, "attendance_chart_not_prepared": "ચાર્ટ has not been prepared for this સ્ટેશન", "@attendance_chart_not_prepared": {"description": "Attendance: Chart has not been prepared for this station", "context": "attendance"}, "attendance_charting_refreshed": "ચાર્ટિંગ refreshed at: {time}", "@attendance_charting_refreshed": {"description": "Attendance: Charting refreshed at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_started": "ચાર્ટિંગ started at: {time}", "@attendance_charting_started": {"description": "Attendance: Charting started at: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_charting_time": "ચાર્ટિંગ સમય: {time}", "@attendance_charting_time": {"description": "Attendance: Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_click_more": "click for more...", "@attendance_click_more": {"description": "Attendance: click for more...", "context": "attendance"}, "attendance_coach_label": "🚃 કોચ: {coach}", "@attendance_coach_label": {"description": "Attendance: 🚃 Coach: {coach}", "context": "attendance", "placeholders": {"coach": {"type": "String"}}}, "attendance_coach_occupancy": "કોચ Occupancy વિગતો", "@attendance_coach_occupancy": {"description": "Attendance: Coach Occupancy <PERSON>", "context": "attendance"}, "attendance_coach_type": "કોચ Type:", "@attendance_coach_type": {"description": "Attendance: Coach Type:", "context": "attendance"}, "attendance_coaches": "Coaches: {coaches}", "@attendance_coaches": {"description": "Attendance: Coaches: {coaches}", "context": "attendance", "placeholders": {"coaches": {"type": "String"}}}, "attendance_concise_view": "Concise જુઓ", "@attendance_concise_view": {"description": "Attendance: Concise View", "context": "attendance"}, "attendance_count_label": "A: {count}", "@attendance_count_label": {"description": "Attendance: A: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_daily": "Daily", "@attendance_daily": {"description": "Attendance: Daily", "context": "attendance"}, "attendance_data_refreshed": "ડેટા refreshed સફળતાપૂર્વક", "@attendance_data_refreshed": {"description": "Attendance: Data refreshed successfully", "context": "attendance"}, "attendance_deboarding": "🔴 ઉતરવું:", "@attendance_deboarding": {"description": "Attendance: 🔴 Deboarding:", "context": "attendance"}, "attendance_deboarding_none": "🔴 ઉતરવું: None", "@attendance_deboarding_none": {"description": "Attendance: 🔴 Deboarding: None", "context": "attendance"}, "attendance_detailed_view": "Detailed જુઓ", "@attendance_detailed_view": {"description": "Attendance: Detailed View", "context": "attendance"}, "attendance_ehk_assigned": "EHK સોંપેલ for ટ્રેન:{ehkName}", "@attendance_ehk_assigned": {"description": "Attendance: EHK Assigned for train:{ehkName}", "context": "attendance", "placeholders": {"ehkName": {"type": "String"}}}, "attendance_expected_charting": "Expected ચાર્ટિંગ સમય: {time}", "@attendance_expected_charting": {"description": "Attendance: Expected Charting Time: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_failed_load_data": "નિષ્ફળ to load detailed ડેટા: {error}", "@attendance_failed_load_data": {"description": "Attendance: Failed to load detailed data: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_failed_update_status": "નિષ્ફળ to અપડેટ કરો ટ્રેન સ્થિતિ", "@attendance_failed_update_status": {"description": "Attendance: Failed to update train status", "context": "attendance"}, "attendance_go": "જાઓ", "@attendance_go": {"description": "Attendance: Go", "context": "attendance"}, "attendance_in_route": "In-route", "@attendance_in_route": {"description": "Attendance: In-route", "context": "attendance"}, "attendance_inside": "અંદર", "@attendance_inside": {"description": "Attendance: Inside", "context": "attendance"}, "attendance_inside_train": "You are now ચિહ્નિત as અંદર the ટ્રેન", "@attendance_inside_train": {"description": "Attendance: You are now marked as inside the train", "context": "attendance"}, "attendance_journey_status_updated": "પ્રવાસ સ્થિતિ updated to {status}", "@attendance_journey_status_updated": {"description": "Attendance: Journey status updated to {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_last_fetched": "Last fetched: {time}", "@attendance_last_fetched": {"description": "Attendance: Last fetched: {time}", "context": "attendance", "placeholders": {"time": {"type": "String"}}}, "attendance_loading": "લોડ થઈ રહ્યું છે...", "@attendance_loading": {"description": "Attendance: Loading...", "context": "attendance"}, "attendance_location_not_fetched": "ટ્રેન સ્થાન is not fetched yet, કૃપા કરીને try again later", "@attendance_location_not_fetched": {"description": "Attendance: Train Location is not fetched yet, please try again later", "context": "attendance"}, "attendance_na": "N/A", "@attendance_na": {"description": "Attendance: N/A", "context": "attendance"}, "attendance_near_stations": "You're near the following સ્ટેશન(s):", "@attendance_near_stations": {"description": "Attendance: You're near the following station(s):", "context": "attendance"}, "attendance_nearby_station_alert": "🛤️ નજીકના સ્ટેશન ચેતવણી", "@attendance_nearby_station_alert": {"description": "Attendance: 🛤️ Nearby Station Alert", "context": "attendance"}, "attendance_non_sleeper": "Non-Sleeper", "@attendance_non_sleeper": {"description": "Attendance: Non-Sleeper", "context": "attendance"}, "attendance_not_attendance_station": "હાજરી cannot be ચિહ્નિત for સ્ટેશન {stationCode} as it is not an હાજરી સ્ટેશન.", "@attendance_not_attendance_station": {"description": "Attendance: Attendance cannot be marked for station {stationCode} as it is not an attendance station.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_not_inside_train": "You are not અંદર the ટ્રેન. કૃપા કરીને જાઓ અંદર the ટ્રેન first.", "@attendance_not_inside_train": {"description": "Attendance: You are not inside the train. Please go inside the train first.", "context": "attendance"}, "attendance_off_label": "Off: {count}", "@attendance_off_label": {"description": "Attendance: Off: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_offboarding_section": "Offboarding", "@attendance_offboarding_section": {"description": "Attendance: Offboarding", "context": "attendance"}, "attendance_ok": "બરાબર", "@attendance_ok": {"description": "Attendance: OK", "context": "attendance"}, "attendance_on_label": "On: {count}", "@attendance_on_label": {"description": "Attendance: On: {count}", "context": "attendance", "placeholders": {"count": {"type": "String"}}}, "attendance_onboarding": "🟢 ચઢવું:", "@attendance_onboarding": {"description": "Attendance: 🟢 Onboarding:", "context": "attendance"}, "attendance_onboarding_none": "🟢 ચઢવું: None", "@attendance_onboarding_none": {"description": "Attendance: 🟢 Onboarding: None", "context": "attendance"}, "attendance_onboarding_section": "ચઢવું", "@attendance_onboarding_section": {"description": "Attendance: Onboarding", "context": "attendance"}, "attendance_other_ca": "Other CA", "@attendance_other_ca": {"description": "Attendance: Other CA", "context": "attendance"}, "attendance_other_ehk": "Other EHK/OBHS", "@attendance_other_ehk": {"description": "Attendance: Other EHK/OBHS", "context": "attendance"}, "attendance_outside_train": "You are now ચિહ્નિત as બહાર the ટ્રેન", "@attendance_outside_train": {"description": "Attendance: You are now marked as outside the train", "context": "attendance"}, "attendance_passenger_chart": "મુસાફર ચાર્ટ   Atten..", "@attendance_passenger_chart": {"description": "Attendance: Passenger Chart   Atten..", "context": "attendance"}, "attendance_refresh_failed": "રિફ્રેશ નિષ્ફળ: {error}", "@attendance_refresh_failed": {"description": "Attendance: Refresh failed: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_screen_title": "હાજરી", "@attendance_screen_title": {"description": "Attendance: Attendance", "context": "attendance"}, "attendance_select_date": "પસંદ કરો તારીખ", "@attendance_select_date": {"description": "Attendance: Select date", "context": "attendance"}, "attendance_select_train_date": "કૃપા કરીને પસંદ કરો a ટ્રેન નંબર and તારીખ.", "@attendance_select_train_date": {"description": "Attendance: Please select a train number and date.", "context": "attendance"}, "attendance_select_train_first": "કૃપા કરીને પસંદ કરો a ટ્રેન first", "@attendance_select_train_first": {"description": "Attendance: Please select a train first", "context": "attendance"}, "attendance_self": "Self", "@attendance_self": {"description": "Attendance: Self", "context": "attendance"}, "attendance_sleeper": "Sleeper", "@attendance_sleeper": {"description": "Attendance: Sleeper", "context": "attendance"}, "attendance_station_details": "સ્ટેશન વિગતો - {stationCode}", "@attendance_station_details": {"description": "Attendance: Station Details - {stationCode}", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_stoppages": "Stoppages", "@attendance_stoppages": {"description": "Attendance: Stoppages", "context": "attendance"}, "attendance_timings": "Tim<PERSON>", "@attendance_timings": {"description": "Attendance: Timings", "context": "attendance"}, "attendance_today": "Today", "@attendance_today": {"description": "Attendance: Today", "context": "attendance"}, "attendance_too_far_from_station": "You're over 50 KM away from the selected સ્ટેશન {stationCode}. હાજરી can only be ચિહ્નિત when you're within the allowed range.", "@attendance_too_far_from_station": {"description": "Attendance: You're over 50 KM away from the selected station {stationCode}. Attendance can only be marked when you're within the allowed range.", "context": "attendance", "placeholders": {"stationCode": {"type": "String"}}}, "attendance_train": "ટ્રેન", "@attendance_train": {"description": "Attendance: Train", "context": "attendance"}, "attendance_train_date": "ટ્રેન {trainNo} - {date}", "@attendance_train_date": {"description": "Attendance: Train {trainNo} - {date}", "context": "attendance", "placeholders": {"trainNo": {"type": "String"}, "date": {"type": "String"}}}, "attendance_train_depot": "ટ્રેન ડિપો:{depot}", "@attendance_train_depot": {"description": "Attendance: Train Depot:{depot}", "context": "attendance", "placeholders": {"depot": {"type": "String"}}}, "attendance_train_not_running": "ટ્રેન Not Running", "@attendance_train_not_running": {"description": "Attendance: Train Not Running", "context": "attendance"}, "attendance_train_not_running_message": "ટ્રેન {trainNumber} is NOT running on {dayOfWeek}", "@attendance_train_not_running_message": {"description": "Attendance: Train {trainNumber} is NOT running on {dayOfWeek}", "context": "attendance", "placeholders": {"trainNumber": {"type": "String"}, "dayOfWeek": {"type": "String"}}}, "attendance_update": "અપડેટ કરો", "@attendance_update": {"description": "Attendance: Update", "context": "attendance"}, "attendance_update_journey_status": "અપડેટ કરો પ્રવાસ સ્થિતિ", "@attendance_update_journey_status": {"description": "Attendance: Update Journey Status", "context": "attendance"}, "attendance_update_message": "A new આવૃત્તિ of the app is ઉપલબ્ધ. You must અપડેટ કરો to continue using the app.", "@attendance_update_message": {"description": "Attendance: A new version of the app is available. You must update to continue using the app.", "context": "attendance"}, "attendance_update_now": "અપડેટ કરો Now", "@attendance_update_now": {"description": "Attendance: Update Now", "context": "attendance"}, "attendance_update_required": "અપડેટ કરો જરૂરી", "@attendance_update_required": {"description": "Attendance: Update Required", "context": "attendance"}, "attendance_user_location": "વપરાશકર્તા સ્થાન:{locationStatus}", "@attendance_user_location": {"description": "Attendance: User Location:{locationStatus}", "context": "attendance", "placeholders": {"locationStatus": {"type": "String"}}}, "attendance_details_distance": "અંતર: {distance} km", "@attendance_details_distance": {"description": "Attendance: Distance: {distance} km", "context": "attendance", "placeholders": {"distance": {"type": "String"}}}, "attendance_details_error": "ભૂલ: {error}", "@attendance_details_error": {"description": "Attendance: Error: {error}", "context": "attendance", "placeholders": {"error": {"type": "String"}}}, "attendance_details_journey_date": "પ્રવાસ તારીખ:", "@attendance_details_journey_date": {"description": "Attendance: Journey Date:", "context": "attendance"}, "attendance_details_latitude": "latitude: {latitude}", "@attendance_details_latitude": {"description": "Attendance: latitude: {latitude}", "context": "attendance", "placeholders": {"latitude": {"type": "String"}}}, "attendance_details_longitude": "longitude: {longitude}", "@attendance_details_longitude": {"description": "Attendance: longitude: {longitude}", "context": "attendance", "placeholders": {"longitude": {"type": "String"}}}, "attendance_details_match_percentage": "Match ટકાવારી: {percentage}", "@attendance_details_match_percentage": {"description": "Attendance: Match Percentage: {percentage}", "context": "attendance", "placeholders": {"percentage": {"type": "String"}}}, "attendance_details_nearest_station": "nearest સ્ટેશન: {station}", "@attendance_details_nearest_station": {"description": "Attendance: nearest Station: {station}", "context": "attendance", "placeholders": {"station": {"type": "String"}}}, "attendance_details_no_attendance": "No હાજરી found.", "@attendance_details_no_attendance": {"description": "Attendance: No attendance found.", "context": "attendance"}, "attendance_details_no_data": "No ડેટા ઉપલબ્ધ.", "@attendance_details_no_data": {"description": "Attendance: No data available.", "context": "attendance"}, "attendance_details_no_human_detected": "No human detected", "@attendance_details_no_human_detected": {"description": "Attendance: No human detected", "context": "attendance"}, "attendance_details_station_code": "સ્ટેશન Code:", "@attendance_details_station_code": {"description": "Attendance: Station Code:", "context": "attendance"}, "attendance_details_status": "સ્થિતિ: {status}", "@attendance_details_status": {"description": "Attendance: Status: {status}", "context": "attendance", "placeholders": {"status": {"type": "String"}}}, "attendance_details_status_marked": "ચિહ્નિત", "@attendance_details_status_marked": {"description": "Attendance: Marked", "context": "attendance"}, "attendance_details_status_pending": "Pending", "@attendance_details_status_pending": {"description": "Attendance: Pending", "context": "attendance"}, "attendance_details_title": "હાજરી વિગતો", "@attendance_details_title": {"description": "Attendance: Attendance Details", "context": "attendance"}, "attendance_details_train_number": "ટ્રેન નંબર:", "@attendance_details_train_number": {"description": "Attendance: Train Number:", "context": "attendance"}, "attendance_details_updated": "All વિગતો updated સફળતાપૂર્વક.", "@attendance_details_updated": {"description": "Attendance: All details updated successfully.", "context": "attendance"}, "attendance_details_updated_at": "Updated At: {updatedAt}", "@attendance_details_updated_at": {"description": "Attendance: Updated At: {updatedAt}", "context": "attendance", "placeholders": {"updatedAt": {"type": "String"}}}, "attendance_details_updated_by": "Updated By: {updatedBy}", "@attendance_details_updated_by": {"description": "Attendance: Updated By: {updatedBy}", "context": "attendance", "placeholders": {"updatedBy": {"type": "String"}}}, "attendance_details_username": "Username: {username}", "@attendance_details_username": {"description": "Attendance: Username: {username}", "context": "attendance", "placeholders": {"username": {"type": "String"}}}, "btn_no_attendance_found": "No હાજરી found.", "@btn_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}, "text_attendance_already_submitted": "હાજરી Already Submitted", "@text_attendance_already_submitted": {"description": "Attendance: Attendance Already Submitted", "context": "attendance"}, "text_attendance_marked_successfully": "હાજરી ચિહ્નિત સફળતાપૂર્વક!", "@text_attendance_marked_successfully": {"description": "Attendance: Attendance marked successfully!", "context": "attendance"}, "text_no_attendance_found": "No હાજરી found.", "@text_no_attendance_found": {"description": "Attendance: No attendance found.", "context": "attendance"}}