{"@@locale": "bn", "appTitle": "রেলঅপ্স", "@appTitle": {"description": "The title of the application"}, "cancel": "", "confirm": "", "delete": "", "edit": "", "error": "", "home": "", "loading": "", "login": "লগিন", "@login": {"description": "Login button text"}, "logout": "", "next": "", "no": "", "ok": "", "password": "", "previous": "", "refresh": "", "retry": "", "save": "", "search": "", "settings": "", "submit": "", "username": "", "welcome": "রেলঅপ্স-এ আপনাকে স্বাগতম", "@welcome": {"description": "Welcome message displayed on home screen"}, "yes": "", "test_welcome": "স্বাগতম", "@test_welcome": {"description": "Welcome message for testing", "context": "test_context"}, "test_login": "লগইন", "@test_login": {"description": "Login button text", "context": "button_labels"}, "text_menu": "মেনু", "@text_menu": {"description": "Text for drawer menu header", "context": "custom_drawer"}, "text_train_tracker": "ট্রেন ট্র্যাকার", "@text_train_tracker": {"description": "Text from text_widgets: Train Tracker", "context": "text_widgets"}, "text_assign_ca": "CA নিয়োগ করুন", "@text_assign_ca": {"description": "Text from text_widgets: Assign CA", "context": "text_widgets"}, "text_assign_cs": "CS নিয়োগ করুন", "@text_assign_cs": {"description": "Text from text_widgets: Assign CS", "context": "text_widgets"}, "text_pnr_details": "PNR বিবরণ", "@text_pnr_details": {"description": "Text from text_widgets: PNR Details", "context": "text_widgets"}, "text_passenger_chart": "যাত্রী চার্ট", "@text_passenger_chart": {"description": "Text from text_widgets: Passenger Chart", "context": "text_widgets"}, "text_map_screen": "ম্যাপ স্ক্রিন", "@text_map_screen": {"description": "Text from text_widgets: Map Screen", "context": "text_widgets"}, "text_configuration": "কনফিগারেশন", "@text_configuration": {"description": "Text from text_widgets: Configuration", "context": "text_widgets"}, "text_reports": "রিপোর্ট", "@text_reports": {"description": "Text from text_widgets: Reports", "context": "text_widgets"}, "text_passenger_feedback": "যাত্রী ফিডব্যাক", "@text_passenger_feedback": {"description": "Text from text_widgets: Passenger Feedback", "context": "text_widgets"}, "text_rake_deficiency_report_1": "রেক ঘাটতি রিপোর্ট", "@text_rake_deficiency_report_1": {"description": "Text from text_widgets: Rake Deficiency Report", "context": "text_widgets"}, "text_obhs_to_mcc": "OBHS থেকে MCC হস্তান্তর", "@text_obhs_to_mcc": {"description": "Text from text_widgets: OBHS to MCC Handover", "context": "text_widgets"}, "text_mcc_to_obhs": "MCC থেকে OBHS হস্তান্তর", "@text_mcc_to_obhs": {"description": "Text from text_widgets: MCC to OBHS Handover", "context": "text_widgets"}, "text_upload_data": "ডেটা আপলোড করুন", "@text_upload_data": {"description": "Text from text_widgets: Upload data", "context": "text_widgets"}, "text_user_management": "ব্যব<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ব্যবস্থাপনা", "@text_user_management": {"description": "Text from text_widgets: User Management", "context": "text_widgets"}, "text_issue_management": "সমস্যা ব্যবস্থাপনা", "@text_issue_management": {"description": "Text from text_widgets: Issue Management", "context": "text_widgets"}, "text_rail_sathi_qr": "রেল সাথী QR", "@text_rail_sathi_qr": {"description": "Text from text_widgets: Rail Sathi Qr", "context": "text_widgets"}, "text_customer_care": "গ্র<PERSON><PERSON><PERSON> সেবা", "@text_customer_care": {"description": "Text from text_widgets: Customer Care", "context": "text_widgets"}}