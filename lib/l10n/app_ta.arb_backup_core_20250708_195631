{"@@locale": "ta", "appTitle": "ரெயில்ஆப்ஸ்", "@appTitle": {"description": "The title of the application"}, "cancel": "", "confirm": "", "delete": "", "edit": "", "error": "", "home": "", "loading": "", "login": "உள்நুழைவு", "@login": {"description": "Login button text"}, "logout": "", "next": "", "no": "", "ok": "", "password": "", "previous": "", "refresh": "", "retry": "", "save": "", "search": "", "settings": "", "submit": "", "username": "", "welcome": "ரெயில்ஆப்ஸ்க்கு உங்களை வரவேற்கிறோம்", "@welcome": {"description": "Welcome message displayed on home screen"}, "yes": "", "test_welcome": "வரவேற்கிறோம்", "@test_welcome": {"description": "Welcome message for testing", "context": "test_context"}, "test_login": "உள்நுழைவு", "@test_login": {"description": "Login button text", "context": "button_labels"}, "text_menu": "மெனு", "@text_menu": {"description": "Text for drawer menu header", "context": "custom_drawer"}, "text_train_tracker": "ரயில் டிராக்கர்", "@text_train_tracker": {"description": "Text from text_widgets: Train Tracker", "context": "text_widgets"}, "text_assign_ca": "CA ஒதுக்கீடு", "@text_assign_ca": {"description": "Text from text_widgets: Assign CA", "context": "text_widgets"}, "text_assign_cs": "CS ஒதுக்கீடு", "@text_assign_cs": {"description": "Text from text_widgets: Assign CS", "context": "text_widgets"}, "text_pnr_details": "PNR விவரங்கள்", "@text_pnr_details": {"description": "Text from text_widgets: PNR Details", "context": "text_widgets"}, "text_passenger_chart": "பயணிகள் அட்டவணை", "@text_passenger_chart": {"description": "Text from text_widgets: Passenger Chart", "context": "text_widgets"}, "text_map_screen": "வரைபட திரை", "@text_map_screen": {"description": "Text from text_widgets: Map Screen", "context": "text_widgets"}, "text_configuration": "கட்டமைப்பு", "@text_configuration": {"description": "Text from text_widgets: Configuration", "context": "text_widgets"}, "text_reports": "அறிக்கைகள்", "@text_reports": {"description": "Text from text_widgets: Reports", "context": "text_widgets"}, "text_passenger_feedback": "பயணிகள் கருத்து", "@text_passenger_feedback": {"description": "Text from text_widgets: Passenger Feedback", "context": "text_widgets"}, "text_rake_deficiency_report_1": "ரேக் குறைபாடு அறிக்கை", "@text_rake_deficiency_report_1": {"description": "Text from text_widgets: Rake Deficiency Report", "context": "text_widgets"}, "text_obhs_to_mcc": "OBHS இலிருந்து MCC ஒப்படைப்பு", "@text_obhs_to_mcc": {"description": "Text from text_widgets: OBHS to MCC Handover", "context": "text_widgets"}, "text_mcc_to_obhs": "MCC இலிருந்து OBHS ஒப்படைப்பு", "@text_mcc_to_obhs": {"description": "Text from text_widgets: MCC to OBHS Handover", "context": "text_widgets"}, "text_upload_data": "தரவு பதிவேற்றம்", "@text_upload_data": {"description": "Text from text_widgets: Upload data", "context": "text_widgets"}, "text_user_management": "பயனர் மேலாண்மை", "@text_user_management": {"description": "Text from text_widgets: User Management", "context": "text_widgets"}, "text_issue_management": "சிக்கல் மேலாண்மை", "@text_issue_management": {"description": "Text from text_widgets: Issue Management", "context": "text_widgets"}, "text_rail_sathi_qr": "ரயில் சாதி QR", "@text_rail_sathi_qr": {"description": "Text from text_widgets: Rail Sathi Qr", "context": "text_widgets"}, "text_customer_care": "வாடிக்கையாளர் சேவை", "@text_customer_care": {"description": "Text from text_widgets: Customer Care", "context": "text_widgets"}}