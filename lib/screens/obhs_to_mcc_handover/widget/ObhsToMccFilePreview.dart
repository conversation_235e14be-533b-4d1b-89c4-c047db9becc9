import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/routes.dart';
import 'package:railops/screens/pages/image_video_detail_page.dart';
import 'package:railops/screens/trip_report/widget/CoachIssueStatus.dart';
import 'package:railops/screens/trip_report/widget/StatusSelectionModal.dart';
import 'package:railops/services/assign_ehk_ca_services/assign_ehk_ca_services.dart';
import 'package:railops/services/trip_report_services/trip_report_services.dart';
import 'package:railops/types/trip_report_type/trip_report_type.dart';
import 'package:railops/widgets/error_modal.dart';
import 'package:railops/widgets/success_modal.dart';
import 'package:video_player/video_player.dart';
import 'package:railops/screens/pages/image_detail_page.dart';
import 'package:railops/types/assign_ehk_ca_types/jobchart_type.dart';
import 'package:intl/intl.dart';
import 'package:railops/types/image_detail_types/image_detail_types.dart';

class ImgVideoDetailPage extends StatefulWidget {
  final ImageResponse imgVideoResponse;
  final String url;
  final String trainNumber;
  final bool isImage;

  const ImgVideoDetailPage({
    Key? key,
    required this.imgVideoResponse,
    required this.url,
    required this.trainNumber,
    required this.isImage,
  }) : super(key: key);

  @override
  State<ImgVideoDetailPage> createState() => _ImgVideoDetailPageState();
}

class _ImgVideoDetailPageState extends State<ImgVideoDetailPage> {
  late VideoPlayerController? _videoController;

  @override
  void initState() {
    super.initState();
    if (!widget.isImage) {
      _videoController = VideoPlayerController.network(widget.url)
        ..initialize().then((_) {
          setState(() {});
          _videoController?.play();
        });
    } else {
      _videoController = null;
    }
  }

  @override
  void dispose() {
    _videoController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isImage ? "Image Detail" : "Video Detail"),
      ),
      body: Center(
        child: widget.isImage
            ? InteractiveViewer(
                panEnabled: true,
                minScale: 1.0,
                maxScale: 4.0,
                child: Image.network(
                  widget.url,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) =>
                      const Icon(Icons.error),
                ),
              )
            : _videoController != null && _videoController!.value.isInitialized
                ? AspectRatio(
                    aspectRatio: _videoController!.value.aspectRatio,
                    child: VideoPlayer(_videoController!),
                  )
                : const CircularProgressIndicator(),
      ),
      floatingActionButton: widget.isImage || _videoController == null
          ? null
          : FloatingActionButton(
              onPressed: () {
                setState(() {
                  if (_videoController!.value.isPlaying) {
                    _videoController!.pause();
                  } else {
                    _videoController!.play();
                  }
                });
              },
              child: Icon(
                _videoController!.value.isPlaying
                    ? Icons.pause
                    : Icons.play_arrow,
              ),
            ),
    );
  }
}

class TripReportFilePreview extends StatefulWidget {
  final TripReportType imageResponse;
  final bool showStatus;
  final String journeyDate;
  final String trainNumber;
  final String coach;
  final List<String> users;
  final String? reportFor;

  const TripReportFilePreview({
    Key? key,
    required this.imageResponse,
    required this.showStatus,
    required this.journeyDate,
    required this.trainNumber,
    required this.coach,
    required this.users,
    this.reportFor,
  }) : super(key: key);

  @override
  State<TripReportFilePreview> createState() => _TripReportFilePreviewState();
}

class _TripReportFilePreviewState extends State<TripReportFilePreview> {
  bool _isVideoFile(String url) {
    final videoExtensions = ['.mp4', '.mov', '.avi', '.mkv'];
    return videoExtensions.any((ext) => url.toLowerCase().contains(ext));
  }

  String getFormattedDate(String? datetime) {
    DateTime? fm_datetime = DateTime.parse(datetime!);
    return DateFormat('yyyy-MM-dd - kk:mm:ss').format(fm_datetime);
  }

  List<String> get _allMediaUrls {
    return [
      ...widget.imageResponse.imageUrls,
      ...?widget.imageResponse.videoUrls,
    ];
  }

  Widget _buildMediaThumbnail(String url) {
    bool isVideo = _isVideoFile(url);
    return Container(
      width: 100,
      height: 100,
      margin: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey[900],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Stack(
          fit: StackFit.expand,
          children: [
            if (!isVideo)
              Image.network(
                url,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  color: Colors.grey,
                  child: const Icon(Icons.error, color: Colors.red),
                ),
              ),
            if (isVideo)
              const Center(
                child: Icon(Icons.play_arrow, color: Colors.white, size: 40),
              ),
          ],
        ),
      ),
    );
  }

  int? _selectedMediaIndex;

  Future<void> _deleteFile(String mediaUrl) async {
    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;

      final response = await TripReportServices.deleteJourneyReportMedia(
        fileId: widget.imageResponse.id,
        mediaUrl: mediaUrl,
        token: token,
        reportFor: 'obhs_to_mcc',
      );

      if (response?['message']?.toLowerCase().contains('success') ?? false) {
        showSuccessModal(
          context,
          '${response?['message']}',
          "Success",
          () => Navigator.pushNamed(context, Routes.tripReport),
        );
      }
    } catch (e) {
      showErrorModal(context, e.toString(), "Error", () {});
    }
  }

  @override
  Widget build(BuildContext context) {
    DateTime? createdAt = DateTime.parse(widget.imageResponse.createdAt);
    DateTime localTime = createdAt.toLocal();
    String formattedDate =
        DateFormat('yyyy-MM-dd - kk:mm:ss').format(localTime);

    return Card(
      margin: const EdgeInsets.all(5),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Modified media gallery section without header overlay
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _allMediaUrls.length,
              itemBuilder: (context, index) {
                final url = _allMediaUrls[index];
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedMediaIndex = index;
                    });

                    ImageResponse detailPageImageResponse =
                        ImageResponse.fromTripReportType(widget.imageResponse);
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => ImgVideoDetailPage(
                          imgVideoResponse: detailPageImageResponse,
                          url: url,
                          trainNumber: widget.trainNumber,
                          isImage: _isVideoFile(url) ? false : true,
                        ),
                      ),
                    );
                  },
                  child: _buildMediaThumbnail(url),
                );
              },
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (widget.showStatus == false) ...[
                  Text(
                    'Uploaded at: $formattedDate',
                    style: const TextStyle(fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                    softWrap: true,
                  ),
                  Text(
                    'Uploaded by: ${widget.imageResponse.createdBy}',
                    style: const TextStyle(fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                    softWrap: true,
                  ),
                  Text(
                    'Latitude: ${widget.imageResponse.latitude}',
                    style: const TextStyle(fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                    softWrap: true,
                  ),
                  Text(
                    'Longitude: ${widget.imageResponse.longitude}',
                    style: const TextStyle(fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                    softWrap: true,
                  ),
                  if (widget.imageResponse.issueNames != null)
                    Text(
                      'Issues: ${widget.imageResponse.issueNames}',
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                      maxLines: 2,
                    ),
                  if (widget.imageResponse.subIssueNames != null)
                    Text(
                      'Sub Issues: ${widget.imageResponse.subIssueNames}',
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                      maxLines: 2,
                    ),
                  if (widget.imageResponse.comment != null ||
                      widget.imageResponse.comment!.isNotEmpty)
                    Text(
                      'Comment: ${widget.imageResponse.comment}',
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                      maxLines: 2,
                    ),
                  const SizedBox(height: 8),
                  // Keep only the standalone delete button at the bottom of the card
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      ElevatedButton.icon(
                        onPressed: () async {
                          final confirmed = await showDialog<bool>(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('Confirm Deletion'),
                              content: const Text(
                                  'Are you sure you want to delete this report?'),
                              actions: [
                                TextButton(
                                  onPressed: () =>
                                      Navigator.pop(context, false),
                                  child: const Text('Cancel'),
                                ),
                                ElevatedButton(
                                  onPressed: () => Navigator.pop(context, true),
                                  child: const Text('Delete'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.redAccent,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          );

                          if (confirmed ?? false) {
                            if (_allMediaUrls.isNotEmpty) {
                              await _deleteFile(_allMediaUrls[0]);
                            }
                          }
                        },
                        icon: const Icon(Icons.delete, color: Colors.white),
                        label: const Text('Delete Report'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.redAccent,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ],
                if (widget.showStatus) ...[
                  if (widget.imageResponse.coach != null)
                    Text(
                      'Coach: ${widget.imageResponse.coach}',
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                    ),
                  if (widget.imageResponse.issue != null)
                    Text(
                      'Issue: ${widget.imageResponse.issue}',
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                      maxLines: 2,
                    ),
                  if (widget.imageResponse.reportedBy != null)
                    Text(
                      'Reported by: ${widget.imageResponse.reportedBy}',
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                    ),
                  if (widget.imageResponse.reportedAt != null)
                    Text(
                      'Reported at: ${getFormattedDate(widget.imageResponse.reportedAt)}',
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                    ),
                  if (widget.imageResponse.fixedBy != null)
                    Text(
                      'Fixed by: ${widget.imageResponse.fixedBy}',
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                    ),
                  if (widget.imageResponse.fixedAt != null)
                    Text(
                      'Fixed at: ${getFormattedDate(widget.imageResponse.fixedAt)}',
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                    ),
                  if (widget.imageResponse.resolvedBy != null)
                    Text(
                      'Resolved by: ${widget.imageResponse.resolvedBy}',
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                    ),
                  if (widget.imageResponse.resolvedAt != null)
                    Text(
                      'Resolved at: ${getFormattedDate(widget.imageResponse.resolvedAt)}',
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                      softWrap: true,
                    ),
                ],
              ],
            ),
          ),
          if (widget.showStatus) ...[
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildStatusButton(
                      context,
                      statusType: "Reported",
                      isDisabled: widget.imageResponse.reportedAt != null,
                    ),
                    const SizedBox(width: 8),
                    _buildStatusButton(
                      context,
                      statusType: "Fixed",
                      isDisabled: widget.imageResponse.fixedAt != null,
                    ),
                    const SizedBox(width: 8),
                    _buildStatusButton(
                      context,
                      statusType: "Resolved",
                      isDisabled: widget.imageResponse.resolvedAt != null,
                    ),
                  ],
                ),
              ),
            ),
            // Status view Delete button
            Center(
              child: ElevatedButton.icon(
                onPressed: () async {
                  final confirmed = await showDialog<bool>(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Confirm Deletion'),
                      content: const Text(
                          'Are you sure you want to delete this report?'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context, false),
                          child: const Text('Cancel'),
                        ),
                        ElevatedButton(
                          onPressed: () => Navigator.pop(context, true),
                          child: const Text('Delete'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.redAccent,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  );

                  if (confirmed ?? false) {
                    if (_selectedMediaIndex != null) {
                      final mediaUrl = _allMediaUrls[_selectedMediaIndex!];
                      await _deleteFile(mediaUrl);
                    } else if (_allMediaUrls.isNotEmpty) {
                      await _deleteFile(_allMediaUrls[0]);
                    }
                  }
                },
                icon: const Icon(Icons.delete, color: Colors.white),
                label: const Text('Delete Report'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.redAccent,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  Widget _buildStatusButton(
    BuildContext context, {
    required String statusType,
    required bool isDisabled,
  }) {
    return SizedBox(
      height: 30,
      child: ElevatedButton(
        onPressed: isDisabled
            ? null
            : () async {
                final clicked = await showDialog(
                  context: context,
                  builder: (context) => SelectionModalWidget(
                    statusType: statusType,
                    id: widget.imageResponse.id,
                    users: widget.users,
                  ),
                );
                if (clicked == true) {
                  Navigator.pushReplacement(
                    context,
                    MaterialPageRoute(
                      builder: (context) => CoachIssueStatus(
                        journeyDate: widget.journeyDate,
                        trainNumber: widget.trainNumber,
                        coach: widget.coach,
                      ),
                    ),
                  );
                }
              },
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          textStyle: const TextStyle(fontSize: 12),
          backgroundColor: isDisabled ? null : Colors.blueAccent,
          foregroundColor: Colors.white,
        ),
        child: Text('$statusType By'),
      ),
    );
  }
}
