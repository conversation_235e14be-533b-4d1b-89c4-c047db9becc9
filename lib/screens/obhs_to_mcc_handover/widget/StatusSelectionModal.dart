import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/auth_model.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/services/assign_ehk_ca_services/assign_ehk_ca_services.dart';
import 'package:railops/services/trip_report_services/trip_report_services.dart';
import 'package:railops/types/assign_ehk_ca_types/jobchart_type.dart';

class SelectionModalWidget extends StatefulWidget {
  final String statusType;
  final int statusId;
  final bool isSubIssue;
  final int id;
  final String name;
  final String journeyDate;
  final String coach;
  final String trainNumber;
  final List<String> users;
  final bool revert;

  const SelectionModalWidget({
    Key? key,
    required this.statusType,
    this.statusId = 0,
    this.isSubIssue = false,
    this.id = 0,
    this.name = "",
    this.journeyDate = "",
    this.trainNumber = "",
    this.coach = "",
    this.revert = false,
    required this.users,
  }) : super(key: key);

  @override
  _SelectionModalWidgetState createState() => _SelectionModalWidgetState();
}

class _SelectionModalWidgetState extends State<SelectionModalWidget> {
  String? selectedPerson;
  DateTime? selectedDateTime;
  late TextEditingController _dateController;

  @override
  void initState() {
    super.initState();
    _dateController = TextEditingController();
  }

  @override
  void dispose() {
    _dateController.dispose();
    super.dispose();
  }

  Future<void> _pickDateTime() async {
    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (pickedDate != null) {
      TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );

      if (pickedTime != null) {
        final DateTime finalDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          pickedTime.hour,
          pickedTime.minute,
        );
        setState(() {
          selectedDateTime = finalDateTime;
          _dateController.text =
              DateFormat('yyyy-MM-dd HH:mm').format(finalDateTime);
        });
      }
    }
  }

  Future<void> _updateCoachIssue({String? selectedPerson, String? date}) async {
    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;
      final response = await TripReportServices.updateCoachIssue(widget.id,
          widget.statusType.toLowerCase(), selectedPerson!, date!, token);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating issue: $e')),
      );
    }
  }

  Future<void> _updateCoachStatus(
      {String? selectedPerson, String? date, isSubIssue}) async {
    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;
      final String updated_by = userModel.userName;
      final int subIssueId = isSubIssue ? widget.id : 0;
      final int issueId = isSubIssue ? 0 : widget.id;
      final bool revert = false;
      final response = await TripReportServices.updateCoachStatus(
          issueId,
          subIssueId,
          widget.journeyDate,
          widget.coach,
          widget.trainNumber,
          widget.statusId,
          widget.statusType.toLowerCase(),
          selectedPerson!,
          date!,
          updated_by,
          token,
          revert
          );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error updating issue: $e')),
      );
    }
  }

  Future<void> _confirm() async {
    if (selectedPerson == null || selectedDateTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text("Both person and date/time are required."),
        ),
      );
      return;
    }
    // _updateCoachIssue(
    //   selectedPerson: selectedPerson,
    //   date: selectedDateTime!.toIso8601String(),
    // );
    _updateCoachStatus(
      selectedPerson: selectedPerson,
      date: selectedDateTime!.toIso8601String(),
      isSubIssue: widget.isSubIssue,
    );
    Navigator.pop(context, true);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text("Select ${widget.statusType} By & Date"),
      titleTextStyle: const TextStyle(fontSize: 18, color: Colors.black),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.isSubIssue)
            Text("SubIssue : ${widget.name} ", style: TextStyle(fontSize: 14)),
          if (!widget.isSubIssue)
            Text("Issue : ${widget.name} ", style: TextStyle(fontSize: 14)),
          const SizedBox(height: 10),
          DropdownSearch<String>(
            popupProps: PopupProps.menu(
              showSearchBox: true,
              searchFieldProps: TextFieldProps(
                decoration: InputDecoration(
                  hintText: "Search...",
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            dropdownDecoratorProps: DropDownDecoratorProps(
              dropdownSearchDecoration: InputDecoration(
                labelText: "${widget.statusType} by",
                border: OutlineInputBorder(),
              ),
            ),
            items: widget.users,
            selectedItem: selectedPerson,
            onChanged: (newValue) {
              setState(() {
                selectedPerson = newValue;
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please select a person';
              }
              return null;
            },
          ),
          const SizedBox(height: 20),
          // Read-only text field for selecting & displaying date & time.
          TextFormField(
            controller: _dateController,
            readOnly: true,
            onTap: _pickDateTime,
            decoration: InputDecoration(
              labelText: "Select Date & Time",
              border: const OutlineInputBorder(),
              suffixIcon: const Icon(Icons.calendar_today),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            textStyle: const TextStyle(fontSize: 12),
            foregroundColor: Colors.black,
            backgroundColor: Colors.white,
            side: BorderSide(color: Colors.black, width: 0.5),
          ),
          onPressed: () => Navigator.pop(context),
          child: const Text("Cancel"),
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            textStyle: const TextStyle(fontSize: 12),
            foregroundColor: Colors.black,
            backgroundColor: Colors.white,
            side: BorderSide(color: Colors.black, width: 0.5),
          ),
          onPressed: _confirm,
          child: const Text("Confirm"),
        ),
      ],
    );
  }
}
