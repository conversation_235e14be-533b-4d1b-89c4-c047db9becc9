import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:railops/core/utilities/color_constants.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/services/pdf_service/download_pdf_service.dart';
import 'package:railops/widgets/index.dart';
import "package:universal_html/html.dart" as html;
import 'dart:io' as io;
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';

class PdfButtons extends StatefulWidget {
  final String date;
  final String buttonText;
  final String urlLink;
  final bool isMail;
  final List<String> trainNumbers;
  final String division;
  final List<String> userType; // Added userType parameter as List
  final bool? showPhoneColumn;
  final Icon? buttonIcon;
  final String? pdfPreviewImagePath;
  final List<String>? pdfPreviewImageList;
  final bool? showDownloadButtonForMonthly;
  final bool directMail;

  const PdfButtons({
    required this.date,
    required this.buttonText,
    required this.urlLink,
    required this.isMail,
    required this.trainNumbers,
    required this.division,
    required this.userType, // Added required userType parameter as List
    this.showPhoneColumn,
    required this.buttonIcon,
    this.pdfPreviewImagePath,
    this.showDownloadButtonForMonthly,
    this.pdfPreviewImageList,
    this.directMail = false,
    super.key,
  });

  @override
  State<PdfButtons> createState() => _PdfButtonsState();
}

class _PdfButtonsState extends State<PdfButtons> {
  final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  bool _notificationsEnabled = false;

  @override
  void initState() {
    super.initState();
    _initializeNotifications();
    _checkNotificationPermission();
  }

  Future<void> _checkNotificationPermission() async {
    if (!io.Platform.isAndroid) return;

    var androidInfo = await deviceInfoPlugin.androidInfo;
    int androidVersion = int.parse(androidInfo.version.release);

    if (androidVersion >= 13) {
      final status = await Permission.notification.status;
      setState(() {
        _notificationsEnabled = status == PermissionStatus.granted;
      });
    } else {
      setState(() {
        _notificationsEnabled = true;
      });
    }
  }

  Future<void> _requestNotificationPermission() async {
    if (!io.Platform.isAndroid) return;

    var androidInfo = await deviceInfoPlugin.androidInfo;
    int androidVersion = int.parse(androidInfo.version.release);

    if (androidVersion >= 13) {
      final status = await Permission.notification.request();
      setState(() {
        _notificationsEnabled = status == PermissionStatus.granted;
      });
    }
  }

  Future<void> _initializeNotifications() async {
    const androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    const initializationSettings =
        InitializationSettings(android: androidSettings);

    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: (NotificationResponse response) async {
        final String? payload = response.payload;
        if (payload != null) {
          final result = await OpenFilex.open(payload);
          if (result.type != ResultType.done) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                    content: Text('Could not open file: ${result.message}')),
              );
            }
          }
        }
      },
    );
  }

  Future<void> _showNotification(String filePath) async {
    if (!_notificationsEnabled) {
      await _requestNotificationPermission();
      if (!_notificationsEnabled) {
        // If user denied notification permission, just return
        return;
      }
    }

    const androidDetails = AndroidNotificationDetails(
      'pdf_download_channel',
      'PDF Downloads',
      channelDescription: 'Notifications for downloaded PDF files',
      importance: Importance.high,
      priority: Priority.high,
      actions: [
        AndroidNotificationAction('open_file', 'Open File'),
      ],
    );

    const notificationDetails = NotificationDetails(android: androidDetails);

    await flutterLocalNotificationsPlugin.show(
      0,
      'Download Complete',
      'Tap to open the PDF file',
      notificationDetails,
      payload: filePath,
    );
  }

  Future<bool> _requestPermissions() async {
    var androidInfo = await deviceInfoPlugin.androidInfo;
    int androidVersion = int.parse(androidInfo.version.release);
    if (io.Platform.isAndroid && !(androidVersion >= 13)) {
      if (await Permission.storage.request().isGranted) {
        return true;
      } else {
        print("Storage permission denied");
        return false;
      }
    } else {
      return true;
    }
  }

  Future<void> _mailReport() async {
    loader(context, "Sending Mail... Please wait.");
    try {
      if (widget.trainNumbers.isEmpty) {
        Navigator.of(context).pop();
        showErrorModal(
            context,
            "No train numbers selected. Please select at least one train number.",
            "Error",
            () {});
        return;
      }
      String trains = widget.trainNumbers.join(',').toString();
      String userTypes = widget.userType.join(',').toString(); // Convert List to comma-separated string
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;
      final response = await DownloadPdfService.mailAllTrainPdf(
        widget.date,
        widget.urlLink,
        true,
        token,
        trains,
        division: widget.division,
        showPhoneColumn: widget.showPhoneColumn,
        userType: userTypes, // Pass userTypes as comma-separated string
      );
      Navigator.of(context).pop();
      if (response
          .contains("A pdf is already being sent try after some time")) {
        showErrorModal(context, response, "Wait", () {});
      } else {
        // Handle success case
        showErrorModal(context, response, "Success", () {});
      }
    } catch (e) {
      Navigator.of(context).pop();
      showErrorModal(context, "$e", "Error", () {});
    }
  }

  Future<void> _downloadReport() async {
    loader(context, "Downloading the Pdf , Please Wait.");
    try {
      if (widget.trainNumbers.isEmpty) {
        Navigator.of(context).pop();
        showErrorModal(
            context,
            "No train numbers selected. Please select at least one train number.",
            "Error",
            () {});
        return;
      }

      String trains = widget.trainNumbers.join(',').toString();
      String userTypes = widget.userType.join(',').toString(); // Convert List to comma-separated string
      ScaffoldMessenger.of(this.context).showSnackBar(
        const SnackBar(content: Text('Download Started!')),
      );
      final result = (await DownloadPdfService.downloadPdf(
        widget.date,
        widget.urlLink,
        false,
        trains,
        division: widget.division,
        showPhoneColumn: widget.showPhoneColumn,
        userType: userTypes, // Pass userTypes as comma-separated string
      ));
      final Uint8List pdfBytes = result['bytes'];
      final String fileName = result['filename'];
      final String baseFileName = fileName.replaceAll('.pdf', '');
      Navigator.of(context).pop();
      if (kIsWeb) {
        final blob = html.Blob([pdfBytes], 'application/pdf');
        final url = html.Url.createObjectUrlFromBlob(blob);
        final anchor = html.AnchorElement(href: url)
          ..setAttribute('download', fileName)
          ..click();
        html.Url.revokeObjectUrl(url);
      } else {
        if (await _requestPermissions()) {
          String path = '/storage/emulated/0/Download/$fileName';

          int counter = 1;
          while (io.File(path).existsSync()) {
            path = '/storage/emulated/0/Download//${baseFileName}_$counter.pdf';
            counter++;
          }

          final file = io.File(path);
          await file.writeAsBytes(pdfBytes);
          await _triggerMediaScanner(path);

          await _showNotification(path);
          print(path);
          ScaffoldMessenger.of(this.context).showSnackBar(
            SnackBar(content: Text('PDF downloaded successfully to ${path}')),
          );
        }
      }
    } catch (e) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(this.context).showSnackBar(
        SnackBar(content: Text('$e')),
      );
    }
  }

  Future<void> _triggerMediaScanner(String filePath) async {
    try {
      const platform = MethodChannel('com.biputri.railops/media_scanner');
      await platform.invokeMethod('scanMedia', {"path": filePath});
    } on PlatformException catch (e) {
      print("Failed to trigger media scanner: '${e.message}'.");
    }
  }

  void _showReportOptions(
    BuildContext context,
    String title,
  ) {
    final paths = widget.pdfPreviewImageList ??
      (widget.pdfPreviewImagePath != null?[widget.pdfPreviewImagePath!]: []);
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      title,
                      style: TextStyle(fontSize: 18),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: paths.isNotEmpty ?  Container(
                    height: 200,
                    child: SingleChildScrollView(
                    scrollDirection: Axis.vertical,
                    child: Column(
                      children: paths.map((path) {
                        return Image.asset(
                            path,
                            fit: BoxFit.contain,
                          );
                      }).toList(),
                    ),
                  ))
                      : const Icon(
                          Icons.picture_as_pdf_rounded,
                          size: 100,
                        ),
                ),
                const SizedBox(height: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    if (widget.trainNumbers.length == 1 &&
                        (widget.showDownloadButtonForMonthly ?? true))
                      ElevatedButton.icon(
                        onPressed: () async {
                          Navigator.of(context).pop();
                          _downloadReport();
                        },
                        icon: const Icon(Icons.download),
                        label: const Text("Download"),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue.shade100,
                          foregroundColor: Colors.black,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5),
                          ),
                        ),
                      ),
                    ElevatedButton.icon(
                      onPressed: () async {
                        Navigator.of(context).pop();
                        _mailReport();
                      },
                      icon: const Icon(Icons.email),
                      label: const Text("Get in Email"),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.purple.shade100,
                        foregroundColor: Colors.black,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5),
                        ),
                      ),
                    ),
                    if (widget.trainNumbers.length != 1 &&
                        (widget.showDownloadButtonForMonthly ?? true))
                      GestureDetector(
                        onTap: () {
                          if (widget.trainNumbers.isEmpty) {
                            showErrorModal(context,
                                "Please Select At Least One Train.", "", () {});
                          } else {
                            showErrorModal(
                                context,
                                "You have selected more than one train. Please use the mail option.",
                                "",
                                () {});
                          }
                        },
                        child: const Align(
                          alignment: Alignment.centerRight,
                          child: Icon(Icons.info),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 10),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: (){
          if(widget.directMail){
            _mailReport();
          } else {
            _showReportOptions(
              context,
              widget.buttonText,
            );
          }
        },
        child: Container(
          decoration: BoxDecoration(
            color: kWhiteColor,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.3),
                spreadRadius: 1,
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            mainAxisSize:
                MainAxisSize.min, // Make the column take minimum space
            mainAxisAlignment:
                MainAxisAlignment.center, // Center content vertically
            children: [
              widget.buttonIcon!,
              const SizedBox(height: 8),
              Flexible(
                // Use Flexible to allow text to take only needed space
                child: Text(
                  widget.buttonText,
                  textAlign: TextAlign.center,
                  maxLines: 3, // Limit to 2 lines
                  overflow: TextOverflow.ellipsis, // Show ellipsis for overflow
                  style: const TextStyle(
                    fontSize: 9, // Smaller font size
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}