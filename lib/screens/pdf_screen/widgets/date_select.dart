import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class DateSelect extends StatefulWidget {
  final void Function(DateTime) onDateSelected;
  final DateTime initialDate;

  DateSelect({required this.onDateSelected, required this.initialDate});

  @override
  _DateSelectState createState() => _DateSelectState();
}

class _DateSelectState extends State<DateSelect> {
  late TextEditingController _dateController;
  late DateTime _selectedDate;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate;
    _dateController = TextEditingController(
      text: _formatDateToCustomFormat(_selectedDate),
    );
  }

  // Custom date formatter function
  String _formatDateToCustomFormat(DateTime date) {
    final day = date.day.toString().padLeft(2, '0');
    final month =
        DateFormat('MMM').format(date); // Short month name (Jan, Feb, etc.)
    final year = date.year.toString();
    return '$day-$month-$year';
  }

  void _updateDateController() {
    _dateController.text = _formatDateToCustomFormat(_selectedDate);
  }

  Future<void> _selectDate() async {
    final DateTime today = DateTime.now();
    DateTime tempSelectedDate = _selectedDate;
    bool isEditingDate = false;
TextEditingController _dateController = TextEditingController();

    DateTime? selectedDate = await showDialog<DateTime>(
      context: context,
      builder: (BuildContext context) {
        _dateController.text = DateFormat('MMM d').format(tempSelectedDate);
        return StatefulBuilder(
          builder: (context, setStateDialog) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: const ColorScheme.light(
                  primary: Colors.blue,
                  onPrimary: Colors.white,
                  onSurface: Colors.black,
                ),
              ),
              child: AlertDialog(
                contentPadding: EdgeInsets.zero,
                content: Container(
                  width: 320,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header with selected date
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.fromLTRB(24, 24, 24, 0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Select date',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            SizedBox(height: 8),
                            Row(
                            children: [
                              isEditingDate
                                  ? Expanded(
                                      child: TextField(
                                        controller: _dateController,
                                        autofocus: true,
                                        style: const TextStyle(fontSize: 28),
                                        decoration: const InputDecoration(
                                          hintText: 'e.g. Jun 27',
                                          border: InputBorder.none,
                                        ),
                                        onChanged: (value) {
                                          try {
                                            final input = value.trim().toLowerCase();
                                            final normalizedInput = input.split(' ').map((word) {
                                              if (word.isEmpty) return '';
                                              return word[0].toUpperCase() + word.substring(1);
                                            }).join(' ');

                                            final parsed = DateFormat('MMM d').parseStrict(normalizedInput);

                                            final updated = DateTime(
                                              tempSelectedDate.year, parsed.month, parsed.day);
                                            setStateDialog(() {
                                              tempSelectedDate = updated;
                                            });
                                          } catch (e) {
                                            // Invalid input; you can handle this if you want
                                          }
                                        },
                                        onSubmitted: (_) {
                                          setStateDialog(() {
                                            isEditingDate = false;
                                          });
                                        },
                                      ),
                                    )
                                  : Text(
                                      DateFormat('EEE, MMM d').format(tempSelectedDate),
                                      style: const TextStyle(
                                        color: Colors.black,
                                        fontSize: 32,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                              const Spacer(),
                              IconButton(
                                onPressed: () {
                                  setStateDialog(() {
                                    isEditingDate = !isEditingDate;
                                  });
                                },
                                icon: Icon(isEditingDate ? Icons.check : Icons.edit, size: 20),
                              ),
                            ],
                          ),
                          ],
                        ),
                      ),
                      Divider(height: 1),

                      // Calendar with key to force rebuild when tempSelectedDate changes
                      Container(
                        height: 280,
                        child: CalendarDatePicker(
                          key: ValueKey(tempSelectedDate), // Force rebuild when date changes
                          initialDate: tempSelectedDate,
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2100),
                          currentDate: today, // This ensures today's date is highlighted

                          onDateChanged: (DateTime date) {
                            setStateDialog(() {
                              tempSelectedDate = date;
                            });
                          },
                        ),
                      ),
                      // Action buttons - Today, Cancel, OK
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 8),
                        child: Row(
                          children: [
                            // Today button on the left
                            TextButton(
                              onPressed: () {
                                setStateDialog(() {
                                  tempSelectedDate = DateTime(
                                    today.year,
                                    today.month,
                                    today.day,
                                  ); // Ensure we're using today's date without time

                                  tempSelectedDate = today;
                                });
                              },
                              child: const Text(
                                'Today',
                                style: TextStyle(
                                  color: Colors.blue,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            Spacer(),
                            // Cancel and OK buttons on the right
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              child: const Text(
                                'Cancel',
                                style: TextStyle(color: Colors.grey),
                              ),
                            ),
                            SizedBox(width: 8),
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop(tempSelectedDate);
                              },
                              child: const Text(
                                'OK',
                                style: TextStyle(
                                  color: Colors.blue,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );

    if (selectedDate != null) {
      setState(() {
        _selectedDate = selectedDate;
        _updateDateController();
      });
      widget.onDateSelected(selectedDate);
    }
  }

  @override
  void dispose() {
    _dateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 4.0),
          TextField(
            controller: _dateController,
            decoration: const InputDecoration(
              prefixIcon: Icon(Icons.calendar_today_rounded),
              labelText: "Select Date (DD-MMM-YYYY)",
              border: OutlineInputBorder(),
            ),
            readOnly: true,
            onTap: _selectDate,
          ),
        ],
      ),
    );
  }
}