import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:railops/routes.dart';
import 'widgets/change_email_form.dart';
import 'package:railops/widgets/index.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ChangeEmailScreen extends StatefulWidget {
  const ChangeEmailScreen({super.key});

  @override
  _ChangeEmailScreenState createState() => _ChangeEmailScreenState();
}

class _ChangeEmailScreenState extends State<ChangeEmailScreen> {
  late Future<void> _authFuture;

  @override
  void initState() {
    super.initState();
    _authFuture = _loadAuthState(); // Initial load
  }

  Future<void> _loadAuthState() async {
    await Provider.of<AuthModel>(context, listen: false).loadAuthState();
  }

  Future<void> _refreshData() async {
    setState(() {
      _authFuture = _loadAuthState(); // Reload authentication state
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
          CustomAppBar(title: AppLocalizations.of(context).text_change_email),
      drawer: const CustomDrawer(),
      body: RefreshIndicator(
        onRefresh: _refreshData, // Trigger refresh
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Center(
            child: FutureBuilder(
              future: _authFuture, // Use the future defined earlier
              builder: (context, AsyncSnapshot<void> snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const CircularProgressIndicator();
                } else if (snapshot.connectionState == ConnectionState.done) {
                  final authModel =
                      Provider.of<AuthModel>(context, listen: false);
                  if (authModel.isAuthenticated) {
                    return ChangeEmail(
                        userModel: Provider.of<UserModel>(context));
                  } else {
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      Navigator.pushReplacementNamed(context, Routes.login);
                    });
                    return const CircularProgressIndicator();
                  }
                } else if (snapshot.hasError) {
                  // Handle error state here
                  return Text(AppLocalizations.of(context)
                      .text_error_loading_authentication_state);
                }
                return const CircularProgressIndicator();
              },
            ),
          ),
        ),
      ),
    );
  }
}
