import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:railops/routes.dart';
import 'widgets/change_password_form.dart';
import 'package:railops/widgets/index.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ChangePasswordScreen extends StatelessWidget {
  const ChangePasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
          title: AppLocalizations.of(context).text_change_password),
      drawer: const CustomDrawer(),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Center(
          child: FutureBuilder(
            future:
                Provider.of<AuthModel>(context, listen: false).loadAuthState(),
            builder: (context, AsyncSnapshot<void> snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const CircularProgressIndicator();
              } else if (snapshot.connectionState == ConnectionState.done) {
                final authModel =
                    Provider.of<AuthModel>(context, listen: false);
                if (authModel.isAuthenticated) {
                  return ChangePassword(
                      userModel: Provider.of<UserModel>(context));
                } else {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    Navigator.pushReplacementNamed(context, Routes.login);
                  });
                  return const CircularProgressIndicator();
                }
              } else if (snapshot.hasError) {
                // Handle error state here
                return Text(AppLocalizations.of(context)
                    .text_error_loading_authentication_state);
              }
              return const CircularProgressIndicator();
            },
          ),
        ),
      ),
    );
  }
}
