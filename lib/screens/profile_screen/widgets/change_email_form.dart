import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:railops/services/profile_services/profile_services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'dart:async';

class ChangeEmail extends StatefulWidget {
  final UserModel userModel;
  const ChangeEmail({super.key, required this.userModel});
  @override
  _ChangeEmailState createState() => _ChangeEmailState();
}

class _ChangeEmailState extends State<ChangeEmail> {
  late UserModel userModel;
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _currentEmailController;
  late TextEditingController _newEmailController;
  late TextEditingController _otpController;
  bool _isOTPSent = false;
  bool _isGeneratingOTP = false;
  bool _isVerifyingOTP = false;
  bool _showModal = false;
  late Timer? _timer;
  int _timerDuration = 60;
  bool _isResendEnabled = false;

  @override
  void initState() {
    super.initState();
    userModel = Provider.of<UserModel>(context, listen: false);
    _currentEmailController = TextEditingController();
    _newEmailController = TextEditingController();
    _otpController = TextEditingController();
    _getProfile();
  }

  @override
  void dispose() {
    _currentEmailController.dispose();
    _newEmailController.dispose();
    _otpController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _getProfile() async {
    try {
      final token = userModel.token;
      final profileResponse = await ProfileService.getProfile(token);
      setState(() {
        _currentEmailController.text =
            profileResponse.user?.email ?? '<EMAIL>';
      });
    } catch (e) {
      setState(() {
        _showErrorModal(context, '$e');
      });
    }
  }

  void _startTimer() {
    _isResendEnabled = false;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timerDuration == 0) {
        setState(() {
          _isResendEnabled = true;
          _timer?.cancel();
        });
      } else {
        setState(() {
          _timerDuration--;
        });
      }
    });
  }

  Future<void> _generateOTP() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      final token = userModel.token;
      final data = {'email': _newEmailController.text};
      setState(() {
        _isGeneratingOTP = true;
        _timerDuration = 60;
      });
      try {
        final message = await ProfileService.changeEmail(token, data);
        setState(() {
          _isOTPSent = true;
        });
        _showErrorModal(context, message);
        _startTimer();
      } catch (e) {
        setState(() {
          _showErrorModal(context, '$e');
        });
      } finally {
        setState(() {
          _isGeneratingOTP = false;
        });
      }
    }
  }

  Future<void> _verifyOTP() async {
    final token = userModel.token;
    final data = {
      'email': _newEmailController.text,
      'otp': _otpController.text
    };
    setState(() {
      _isVerifyingOTP = true;
    });
    try {
      final message = await ProfileService.changeEmailOTP(token, data);
      setState(() {
        _currentEmailController.text = _newEmailController.text;
        _newEmailController.clear();
        _otpController.clear();
        _isOTPSent = false;
      });
      _showErrorModal(context, message);
    } catch (e) {
      setState(() {
        _showErrorModal(context, '$e');
      });
    } finally {
      setState(() {
        _isVerifyingOTP = false;
      });
    }
  }

  void _showErrorModal(BuildContext context, String errorMessage) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).text_alert),
          content: Text(errorMessage),
          actions: <Widget>[
            TextButton(
              child: Text(AppLocalizations.of(context).text_close),
              onPressed: () {
                setState(() {
                  _showModal = false;
                });
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  @override
  @override
  Widget build(BuildContext context) {
    double sidebarWidth = 20.0;
    bool displaySidebar = true;
    double screenWidth = MediaQuery.of(context).size.width;
    double marginLeft =
        displaySidebar ? (screenWidth > 991 ? sidebarWidth : 0.0) : 0.0;

    return Scaffold(
      body: Stack(
        children: [
          Container(
            margin: EdgeInsets.only(left: marginLeft),
            padding: const EdgeInsets.only(bottom: 32.0),
            child: Column(
              children: <Widget>[
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Center(
                        child: SizedBox(
                          width: screenWidth > 600 ? 600 : screenWidth * 0.9,
                          child: Form(
                            key: _formKey,
                            child: Card(
                              color: Colors.white,
                              elevation: 4,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.0),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  children: <Widget>[
                                    Center(
                                      child: Text(
                                        AppLocalizations.of(context)
                                            .text_change_your_email,
                                        style: const TextStyle(fontSize: 16),
                                      ),
                                    ),
                                    _buildTextField(
                                      label: AppLocalizations.of(context)
                                          .text_current_email,
                                      controller: _currentEmailController,
                                      readOnly: true,
                                    ),
                                    const SizedBox(height: 20),
                                    _buildTextField(
                                      label: AppLocalizations.of(context)
                                          .text_new_email,
                                      controller: _newEmailController,
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return AppLocalizations.of(context)
                                              .text_please_enter_new_email;
                                        }
                                        return null;
                                      },
                                    ),
                                    if (_isOTPSent) ...[
                                      const SizedBox(height: 20),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: _buildTextField(
                                              label:
                                                  AppLocalizations.of(context)
                                                      .text_otp,
                                              controller: _otpController,
                                            ),
                                          ),
                                          const SizedBox(width: 10),
                                          ElevatedButton(
                                            onPressed: _isResendEnabled
                                                ? _generateOTP
                                                : null,
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  const Color(0xFF313256),
                                              foregroundColor: Colors.white,
                                              minimumSize: const Size(100, 50),
                                            ),
                                            child: Text(
                                              _isResendEnabled
                                                  ? AppLocalizations.of(context)
                                                      .text_resend_otp
                                                  : AppLocalizations.of(context)
                                                      .text_resend_in_seconds(
                                                          _timerDuration),
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 20),
                                      ElevatedButton(
                                        onPressed:
                                            _isVerifyingOTP ? null : _verifyOTP,
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.white,
                                          foregroundColor: Colors.black,
                                          side: const BorderSide(
                                              color: Colors.black, width: 0.5),
                                          minimumSize:
                                              const Size(double.infinity, 50),
                                        ),
                                        child: _isVerifyingOTP
                                            ? const CircularProgressIndicator(
                                                color: Colors.white)
                                            : Text(AppLocalizations.of(context)
                                                .text_verify_otp),
                                      ),
                                    ] else ...[
                                      const SizedBox(height: 20),
                                      ElevatedButton(
                                        onPressed: _isGeneratingOTP
                                            ? null
                                            : _generateOTP,
                                        style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.white,
                                          foregroundColor: Colors.black,
                                          side: const BorderSide(
                                              color: Colors.black, width: 0.5),
                                          minimumSize:
                                              const Size(double.infinity, 50),
                                        ),
                                        child: _isGeneratingOTP
                                            ? const CircularProgressIndicator(
                                                color: Colors.white)
                                            : Text(AppLocalizations.of(context)
                                                .text_generate_otp),
                                      ),
                                    ],
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required String label,
    required TextEditingController controller,
    bool readOnly = false,
    FormFieldValidator<String?>? validator,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
        ),
        validator: validator,
        readOnly: readOnly,
      ),
    );
  }
}
