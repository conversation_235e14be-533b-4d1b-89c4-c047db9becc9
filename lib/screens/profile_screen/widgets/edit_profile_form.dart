import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import 'package:provider/provider.dart';
import 'package:railops/routes.dart';
import 'package:railops/models/index.dart';
import 'package:railops/screens/profile_screen/widgets/change_email_modal.dart';
import 'package:railops/screens/user_screen/auth_provider.dart';
import 'package:railops/services/otp_services/sign_up_otp.dart';
import 'package:railops/services/profile_services/profile_services.dart';
import 'package:railops/types/profile_types/request_profile.dart';
import 'package:railops/services/authentication_services/auth_service.dart';
import 'package:railops/widgets/call_widget.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'toggle_switch_widget.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:railops/services/train_services/train_details_services.dart';
import 'package:railops/services/profile_services/profile_train_services.dart';
import 'package:railops/screens/user_screen/widgets/login_page/privacy_policies.dart';
import 'package:url_launcher/url_launcher.dart';

class EditProfileForm extends StatefulWidget {
  final UserModel userModel;
  const EditProfileForm({super.key, required this.userModel});

  @override
  State<EditProfileForm> createState() => _EditProfileFormState();
}

class _EditProfileFormState extends State<EditProfileForm> {
  late UserModel userModel;
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _firstNameController;
  late TextEditingController _middleNameController;
  late TextEditingController _lastNameController;
  late TextEditingController _roleController;
  late TextEditingController _depotController;
  late TextEditingController _phoneNumberController;
  late TextEditingController _whatsappNumberController;
  late TextEditingController _emailController;
  late TextEditingController _empNumberController;
  late TextEditingController _secondaryPhoneController;

  bool _isLoadingProfile = false;
  bool _isUpdatingProfile = false;
  bool _isLoadingChangeEmail = false;
  bool _isLoadingChangePassword = false;
  bool _isLoadingChangeMobile = false;
  bool _isLoadingChangeWhatsapp = false;
  bool _isLoadingAddTrains = false;
  bool _isLogOuting = false;
  List<String> _trainNumbers = [];
  String? _selectedTrainNumber;
  bool _insideTrain = false;
  bool _needAlarm = false;
  String? insideTrainDate;
  String? userType;

  @override
  void initState() {
    super.initState();
    userModel = Provider.of<UserModel>(context, listen: false);
    userType = userModel.userType;
    _firstNameController = TextEditingController();
    _middleNameController = TextEditingController();
    _lastNameController = TextEditingController();
    _roleController = TextEditingController();
    _depotController = TextEditingController();
    _phoneNumberController = TextEditingController();
    _whatsappNumberController = TextEditingController();
    _emailController = TextEditingController();
    _empNumberController = TextEditingController();
    _secondaryPhoneController = TextEditingController();
    _getProfile();
    _fetchTrainNumbers();
    _fetchToggleSwitchStatuses();
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _middleNameController.dispose();
    _lastNameController.dispose();
    _roleController.dispose();
    _depotController.dispose();
    _phoneNumberController.dispose();
    _whatsappNumberController.dispose();
    _emailController.dispose();
    _empNumberController.dispose();
    _secondaryPhoneController.dispose();
    super.dispose();
  }

  Future<void> _fetchTrainNumbers() async {
    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;

      final response = await TrainService.getTrainDetails(token);
      _trainNumbers = response.trainDetails.values
          .map((detail) => detail.trainNumber)
          .toList();

      setState(() {});
    } catch (e) {
      // Handle the error appropriately
      debugPrint('Error fetching train numbers: $e');
    }
  }

  void _onTrainNumberChanged(String? trainNumber) async {
    setState(() {
      _selectedTrainNumber = trainNumber;
    });
  }

  Future<void> _fetchToggleSwitchStatuses() async {
    try {
      final token = userModel.token;
      final insideTrainStatus =
          await ProfileTrainServices.getInsideTrainStatus(token);
      final needAlarmStatus =
          await ProfileTrainServices.getNeedAlarmStatus(token);

      setState(() {
        _insideTrain = insideTrainStatus['inside_train'];
        _selectedTrainNumber = insideTrainStatus['inside_train_number'];
        insideTrainDate = insideTrainStatus['inside_train_date'];
        _needAlarm = needAlarmStatus;
      });
    } catch (e) {
      debugPrint('Error fetching toggle switch statuses: $e');
    }
  }

  void _onInsideTrainToggle(int index) async {
    setState(() {
      _insideTrain = index == 0;
    });
    try {
      final token = userModel.token;
      await ProfileTrainServices.toggleInsideTrain(
        token,
        _insideTrain,
        _selectedTrainNumber ?? '',
      );
    } catch (e) {
      debugPrint('Error toggling inside train status: $e');
    }
  }

  void _onNeedAlarmToggle(int index) async {
    setState(() {
      _needAlarm = index == 0;
    });
    try {
      final token = userModel.token;
      await ProfileTrainServices.toggleNeedAlarm(token, _needAlarm);
    } catch (e) {
      debugPrint('Error toggling need alarm status: $e');
    }
  }

  Future<void> _getProfile() async {
    setState(() {
      _isLoadingProfile = true;
    });
    try {
      final token = userModel.token;
      final profileResponse = await ProfileService.getProfile(token);
      setState(() {
        _firstNameController.text = profileResponse.user?.firstName ?? '';
        _middleNameController.text = profileResponse.user?.middleName ?? '';
        _lastNameController.text = profileResponse.user?.lastName ?? '';
        _roleController.text = profileResponse.role ?? '';
        _depotController.text = profileResponse.user?.depo ?? '';
        _phoneNumberController.text = profileResponse.user?.phone ?? '';
        _whatsappNumberController.text =
            profileResponse.user?.whatsappNumber ?? '';
        _emailController.text = profileResponse.user?.email ?? '';
        _empNumberController.text = profileResponse.user?.empNumber ?? '';
        _secondaryPhoneController.text =
            profileResponse.user?.secondaryPhone ?? '';
      });
    } catch (e) {
      // Handle the error appropriately
      debugPrint('Error fetching profile: $e');
    } finally {
      setState(() {
        _isLoadingProfile = false;
      });
    }
  }

  Future<void> _updateProfile() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      setState(() {
        _isUpdatingProfile = true;
      });
      try {
        final token = userModel.token;
        final updatedProfile = RequestProfile(
          firstName: _firstNameController.text,
          middleName: _middleNameController.text,
          lastName: _lastNameController.text,
          empNumber: _empNumberController.text,
          secondaryPhone: _secondaryPhoneController.text,
        );
        final message =
            await ProfileService.updateProfile(token, updatedProfile);
        debugPrint('Profile update result: $message');

        _showSuccessDialog(message);
      } catch (e) {
        debugPrint('Error updating profile: $e');
        _showSuccessDialog('$e');
      } finally {
        setState(() {
          _isUpdatingProfile = false;
        });
      }
    } else {
      debugPrint("Form validation failed");
    }
  }

  Future<void> _changeEmail() async {
    setState(() {
      _isLoadingChangeEmail = true;
    });
    try {
      await Future.delayed(const Duration(seconds: 1)); // Simulate delay
      if (mounted) {
        Navigator.pushNamed(context, '/change_mail');
      }
    } catch (e) {
      debugPrint('Error changing email: $e');
    } finally {
      setState(() {
        _isLoadingChangeEmail = false;
      });
    }
  }

  Future<void> _changePassword() async {
    setState(() {
      _isLoadingChangePassword = true;
    });
    try {
      await Future.delayed(const Duration(seconds: 1));
      if (mounted) {
        Navigator.pushNamed(context, '/change_password');
      }
    } catch (e) {
      debugPrint('Error changing password: $e');
    } finally {
      setState(() {
        _isLoadingChangePassword = false;
      });
    }
  }

  Future<void> _changeMobile() async {
    setState(() {
      _isLoadingChangeMobile = true;
    });
    try {
      await Future.delayed(const Duration(seconds: 1)); // Simulate delay
      if (mounted) {
        Navigator.pushNamed(context, '/change_mobile');
      }
    } catch (e) {
      debugPrint('Error changing mobile: $e');
    } finally {
      setState(() {
        _isLoadingChangeMobile = false;
      });
    }
  }

  Future<void> _changeWhatsapp() async {
    setState(() {
      _isLoadingChangeWhatsapp = true;
    });
    try {
      await Future.delayed(const Duration(seconds: 1)); // Simulate delay
      if (mounted) {
        Navigator.pushNamed(context, '/change_whatsapp');
      }
    } catch (e) {
      debugPrint('Error changing whatsapp: $e');
    } finally {
      setState(() {
        _isLoadingChangeWhatsapp = false;
      });
    }
  }

  Future<void> _addTrains() async {
    setState(() {
      _isLoadingAddTrains = true;
    });
    try {
      await Future.delayed(const Duration(seconds: 1)); // Simulate delay
      if (mounted) {
        Navigator.pushNamed(context, '/add-train-profile');
      }
    } catch (e) {
      debugPrint('Error adding trains: $e');
    } finally {
      setState(() {
        _isLoadingAddTrains = false;
      });
    }
  }

  Future<void> _showChangeEmailModalOnce(String email) async {
    if (email.startsWith("noemail")) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showDialog(
          context: context,
          builder: (BuildContext context) => const ChangeEmailModal(),
        );
      });
    }
  }

  Future<void> _deactivateAccount() async {
    final token = userModel.token;
    final profileResponse = await ProfileService.getProfile(token);
    final email = profileResponse.user?.email ?? '';

    final bool isNoEmail = email.toLowerCase().startsWith("noemail");

    if (!mounted) return;

    final bool confirmDeactivation = await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).text_confirm_deactivation),
          content: Text(isNoEmail
              ? AppLocalizations.of(context)
                  .text_need_valid_email_before_deactivation
              : AppLocalizations.of(context).text_send_otps_for_verification),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(AppLocalizations.of(context).text_cancel),
            ),
            if (!isNoEmail)
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: Text(AppLocalizations.of(context).text_proceed),
              ),
            if (isNoEmail)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(false);
                  if (isNoEmail) _showChangeEmailModalOnce(email);
                  _getProfile();
                },
                child: Text(AppLocalizations.of(context).text_add_email),
              ),
          ],
        );
      },
    );

    if (confirmDeactivation && !isNoEmail) {
      await _getProfile();
      _sendDeactivationOTP();
    }
  }

  Future<void> _sendDeactivationOTP() async {
    setState(() => _isUpdatingProfile = true);
    final token = userModel.token;
    final profileResponse = await ProfileService.getProfile(token);
    final email = profileResponse.user?.email ?? '';

    try {
      final token = userModel.token;
      // Send both OTPs simultaneously
      await Future.wait([
        AuthService.sendDeactivationOTP(token, email),
        SignUpOtp.sendOtp(_phoneNumberController.text, "deactivate_account")
      ]);

      setState(() => _isUpdatingProfile = false);
      _showVerificationFlow();
    } catch (e) {
      setState(() => _isUpdatingProfile = false);
      _showSuccessDialog(
          AppLocalizations.of(context).text_failed_to_send_otps('$e'));
    }
  }

  void _showVerificationFlow() {
    final emailOtpController = TextEditingController();
    final phoneOtpController = TextEditingController();
    int currentStep = 0;
    bool isVerifying = false;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          child: Material(
            borderRadius: BorderRadius.circular(16),
            child: StatefulBuilder(
              builder: (context, setState) {
                return Stepper(
                  currentStep: currentStep,
                  controlsBuilder: (context, details) {
                    if (currentStep == 0) {
                      return Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          TextButton(
                            onPressed: isVerifying
                                ? null
                                : () async {
                                    if (emailOtpController.text.isEmpty) return;

                                    setState(() => isVerifying = true);
                                    try {
                                      await AuthService.verifyDeactivationOTP(
                                          userModel.token, {
                                        'otp': emailOtpController.text,
                                        "email": _emailController.text
                                      });
                                      setState(() => currentStep = 1);
                                    } catch (e) {
                                      _showSuccessDialog(
                                          AppLocalizations.of(context)
                                              .text_email_otp_error('$e'));
                                    }
                                    setState(() => isVerifying = false);
                                  },
                            child: Text(AppLocalizations.of(context)
                                .text_verify_email_button),
                          ),
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child:
                                Text(AppLocalizations.of(context).text_cancel),
                          ),
                        ],
                      );
                    }
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        TextButton(
                          style: TextButton.styleFrom(
                            backgroundColor: Colors.red,
                          ),
                          onPressed: isVerifying
                              ? null
                              : () async {
                                  if (phoneOtpController.text.isEmpty) return;

                                  setState(() => isVerifying = true);
                                  final currentContext = context;
                                  try {
                                    await AuthService.loginByMobile(
                                        phoneOtpController.text,
                                        _phoneNumberController.text);

                                    // Both OTPs verified - deactivate
                                    final message =
                                        await AuthService.deactivateAccount(
                                            userModel.token);

                                    if (currentContext.mounted) {
                                      Navigator.pop(currentContext);
                                      _showSuccessDialog(message);

                                      Provider.of<AuthModel>(currentContext,
                                              listen: false)
                                          .logout();
                                      Navigator.pushReplacementNamed(
                                          currentContext, Routes.login);
                                    }
                                  } catch (e) {
                                    _showSuccessDialog(
                                        AppLocalizations.of(context)
                                            .text_phone_otp_error('$e'));
                                  }
                                  setState(() => isVerifying = false);
                                },
                          child: Text(
                            AppLocalizations.of(context).text_deactivate,
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                        TextButton(
                          style: TextButton.styleFrom(
                            backgroundColor: Colors.green,
                          ),
                          onPressed: () => Navigator.pop(context),
                          child: Text(AppLocalizations.of(context).text_back,
                              style: const TextStyle(color: Colors.white)),
                        ),
                      ],
                    );
                  },
                  steps: [
                    Step(
                      title: Text(
                          AppLocalizations.of(context).text_email_verification),
                      content: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: TextField(
                          controller: emailOtpController,
                          decoration: InputDecoration(
                            labelText: AppLocalizations.of(context)
                                .text_enter_email_otp,
                            border: const OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ),
                    Step(
                      title: Text(
                          AppLocalizations.of(context).text_phone_verification),
                      content: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: TextField(
                          controller: phoneOtpController,
                          decoration: InputDecoration(
                            labelText: AppLocalizations.of(context)
                                .text_enter_phone_otp,
                            border: const OutlineInputBorder(),
                          ),
                          keyboardType: TextInputType.number,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        );
      },
    );
  }

  Future<void> _handleLogout() async {
    final authModel = Provider.of<AuthModel>(context, listen: false);

    final bool confirmLogout = await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).text_logout_confirmation),
          content:
              Text(AppLocalizations.of(context).text_do_you_want_to_logout),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(false);
              },
              style: TextButton.styleFrom(
                backgroundColor: Colors.green,
              ),
              child: Text(
                AppLocalizations.of(context).text_no,
                style: const TextStyle(color: Colors.white),
              ),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop(true);
              },
              style: TextButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: Text(
                AppLocalizations.of(context).text_yes,
                style: const TextStyle(color: Colors.white),
              ),
            )
          ],
        );
      },
    );

    if (confirmLogout) {
      setState(() {
        _isLogOuting = true;
      });
      final prefs = await SharedPreferences.getInstance();

      final isFingerprintEnabled = prefs.getBool('use_fingerprint') ?? false;

      try {
        // Optional: Call backend logout
        await AuthService.logout(
          authModel.loginResponse!.refreshToken,
          authModel.loginResponse!.token,
        );
      } catch (e) {
        // handle error or log
      } finally {
        await prefs.remove('lastChangeEmailModalTime');

        if (!isFingerprintEnabled) {
          // 🔴 If fingerprint not enabled, clear all login data
          await prefs.remove('loginResponse');
          await prefs.remove('user_name');
          await prefs.remove('user_email');
          await prefs.setBool('is_logged_in', false);
        }

        // ✅ Clear auth state in app
        authModel.logout();

        // ✅ Navigate to login screen
        if (mounted) {
          Navigator.pushReplacementNamed(context, Routes.login);
        }
      }
    }
  }

  Future<void> _resetForm() async {
    // Clear all form fields
    _firstNameController.clear();
    _middleNameController.clear();
    _lastNameController.clear();
    _roleController.clear();
    _depotController.clear();
    _phoneNumberController.clear();
    _whatsappNumberController.clear();
    _emailController.clear();
    _empNumberController.clear();
    _secondaryPhoneController.clear();

    // Reset state variables
    setState(() {
      _selectedTrainNumber = null;
      _insideTrain = false;
      _needAlarm = false;
    });

    // Re-fetch profile and train numbers to reset the state
    await _getProfile();
    await _fetchTrainNumbers();
    await _fetchToggleSwitchStatuses();
  }

  Widget _buildProfileForm() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          _buildTextField(
            AppLocalizations.of(context).text_first_name,
            controller: _firstNameController,
            isEditable: true,
            isPhoneNumber: false,
          ),
          _buildTextField(
            AppLocalizations.of(context).text_middle_name,
            controller: _middleNameController,
            isEditable: true,
            isPhoneNumber: false,
            isRequired: false,
          ),
          _buildTextField(
            AppLocalizations.of(context).text_last_name,
            controller: _lastNameController,
            isEditable: true,
            isPhoneNumber: false,
          ),
          _buildTextField(
            AppLocalizations.of(context).text_employee_number,
            controller: _empNumberController,
            isEditable: true,
            isPhoneNumber: false,
          ),
          _buildTextField(
            AppLocalizations.of(context).text_role,
            controller: _roleController,
            isEditable: false,
            isPhoneNumber: false,
          ),
          _buildTextField(
            AppLocalizations.of(context).text_depot,
            controller: _depotController,
            isEditable: false,
            isPhoneNumber: false,
          ),
          _buildTextField(
            AppLocalizations.of(context).text_phone_number,
            controller: _phoneNumberController,
            isEditable: false,
            isPhoneNumber: true,
          ),
          _buildTextField(AppLocalizations.of(context).text_whatsapp_number,
              controller: _whatsappNumberController,
              isEditable: false,
              isWhatsappNumber: true,
              isRequired: false),
          _buildTextField(
            AppLocalizations.of(context).text_secondary_phone_number,
            controller: _secondaryPhoneController,
            isEditable: true,
            isPhoneNumber: true,
            isRequired: false,
          ),
          _buildTextField(
            AppLocalizations.of(context).text_email,
            controller: _emailController,
            keyboardType: TextInputType.emailAddress,
            isEditable: false,
            isPhoneNumber: false,
          ),
        ],
      ),
    );
  }

  Widget _buildTextField(
    String label, {
    required TextEditingController controller,
    TextInputType keyboardType = TextInputType.text,
    bool isEditable = true,
    bool isPhoneNumber = false,
    bool isWhatsappNumber = false,
    bool isRequired = true,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: TextFormField(
        controller: controller,
        readOnly: !isEditable,
        style: TextStyle(
          color: isEditable ? Colors.black : Colors.grey,
        ),
        decoration: InputDecoration(
          labelText: label,
          border: OutlineInputBorder(
            borderSide: BorderSide(
              color: isEditable ? Colors.blue : Colors.grey[600]!,
            ),
          ),
          filled: !isEditable,
          fillColor: Colors.grey[200],
          suffixIcon: isPhoneNumber
              ? IconButton(
                  icon: Icon(
                    Icons.phone,
                    // Make the icon grey when the field is not editable
                    color: !isEditable ? Colors.grey[400] : null,
                  ),
                  // Disable the button if the field is not editable
                  onPressed: !isEditable
                      ? null // Always disable the call button when field is read-only
                      : () {
                          // Only allow calling if the field is editable
                          showCallModal(context, controller.text);
                        },
                )
              : null,
        ),
        keyboardType: keyboardType,
        inputFormatters: isPhoneNumber
            ? [
                LengthLimitingTextInputFormatter(10),
                FilteringTextInputFormatter.digitsOnly,
              ]
            : null,
        validator: (value) {
          // Check if field is required and empty
          if (isRequired && (value == null || value.isEmpty)) {
            return AppLocalizations.of(context).text_please_enter_field(label);
          }

          // For phone number validation, only validate if there's actually a value
          if (isPhoneNumber && value != null && value.isNotEmpty) {
            if (value.length != 10) {
              return AppLocalizations.of(context)
                  .text_phone_number_must_be_10_digits;
            }
          }

          // Special validation for secondary phone number
          // This only runs if secondary phone number has a value (not empty)
          if (label ==
                  AppLocalizations.of(context).text_secondary_phone_number &&
              value != null &&
              value.isNotEmpty) {
            String primaryPhone = _phoneNumberController.text.trim();
            String secondaryPhone = value.trim();

            if (primaryPhone == secondaryPhone) {
              return AppLocalizations.of(context)
                  .text_phone_number_and_secondary_phone_number_must_be_different;
            }
          }

          return null; // No error - validation passed
        },
      ),
    );
  }

  Widget _buildButton(String label, bool isLoading, VoidCallback onPressed,
      {Color? buttonColor, Color? textColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor:
              buttonColor ?? const Color.fromARGB(255, 223, 224, 236),
          foregroundColor: textColor ?? Colors.black,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          side: const BorderSide(color: Colors.black, width: 0.5),
          minimumSize: const Size(double.infinity, 50),
        ),
        child: isLoading
            ? const CircularProgressIndicator(color: Colors.white)
            : Text(label),
      ),
    );
  }

  Widget _buildButtonsColumn(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        _buildButton(AppLocalizations.of(context).text_change_email,
            _isLoadingChangeEmail, _changeEmail),
        _buildButton(AppLocalizations.of(context).text_change_password,
            _isLoadingChangePassword, _changePassword),
        _buildButton(AppLocalizations.of(context).text_change_mobile_number,
            _isLoadingChangeMobile, _changeMobile),
        _buildButton(AppLocalizations.of(context).text_change_whatsapp_number,
            _isLoadingChangeWhatsapp, _changeWhatsapp),
        Consumer<AuthProvider>(
          builder: (context, fp, _) {
            return Container(
              margin: const EdgeInsets.symmetric(vertical: 12),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade400),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Text(
                      AppLocalizations.of(context)
                          .text_enable_fingerprint_login,
                      style: const TextStyle(
                          fontSize: 16, fontWeight: FontWeight.w500),
                    ),
                  ),
                  Switch.adaptive(
                    value: fp.isEnabled,
                    onChanged: (val) => fp.toggle(val),
                    activeColor: Colors.indigo,
                  ),
                ],
              ),
            );
          },
        ),

        const SizedBox(height: 16.0), // Space before Deactivate Account button
        _buildButton(
          AppLocalizations.of(context).text_deactivate_account,
          _isUpdatingProfile, _deactivateAccount,
          buttonColor: const Color.fromARGB(255, 186, 44, 34),
          textColor: Colors.white, // Set text color to white
        ),
        _buildButton(
          AppLocalizations.of(context).text_logout, _isLogOuting, _handleLogout,
          buttonColor: const Color.fromARGB(255, 186, 44, 34),
          textColor: Colors.white, // Set text color to white
        ),
        privacyPolicyRender(context),
      ],
    );
  }

  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).text_success),
          content: Text(message),
          actions: [
            TextButton(
              child: Text(AppLocalizations.of(context).text_ok),
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    double padding = 16.0;
    double screenWidth = MediaQuery.of(context).size.width;

    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _resetForm, // Set the onRefresh callback to reset the form
        child: Stack(
          children: [
            SingleChildScrollView(
              physics:
                  const AlwaysScrollableScrollPhysics(), // Ensure scrollable content
              padding: EdgeInsets.all(padding),
              child: screenWidth > 600
                  ? Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Expanded(
                          child: Card(
                            color: Colors.white,
                            child: Padding(
                              padding: EdgeInsets.all(padding),
                              child: _isLoadingProfile
                                  ? const Center(
                                      child: CircularProgressIndicator())
                                  : Column(
                                      children: [
                                        _buildProfileForm(),
                                        SizedBox(height: padding),
                                        _buildButton(
                                          AppLocalizations.of(context)
                                              .text_update_profile,
                                          _isUpdatingProfile,
                                          _updateProfile,
                                        ),
                                      ],
                                    ),
                            ),
                          ),
                        ),
                        SizedBox(width: padding),
                        Expanded(
                          child: Column(
                            children: [
                              Card(
                                color: Colors.white,
                                child: Padding(
                                  padding: EdgeInsets.all(padding),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      DropdownSearch<String>(
                                        items: _trainNumbers,
                                        selectedItem: _selectedTrainNumber,
                                        onChanged: (value) {
                                          _onTrainNumberChanged(value);
                                        },
                                        dropdownDecoratorProps:
                                            DropDownDecoratorProps(
                                          dropdownSearchDecoration:
                                              InputDecoration(
                                            labelText:
                                                AppLocalizations.of(context)
                                                    .text_select_train_number,
                                            border: const OutlineInputBorder(),
                                          ),
                                        ),
                                        popupProps: PopupProps.menu(
                                          showSearchBox: true,
                                          itemBuilder:
                                              (context, item, isSelected) {
                                            return ListTile(
                                              title: Text(item),
                                              selected: isSelected,
                                            );
                                          },
                                        ),
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return AppLocalizations.of(context)
                                                .text_please_select_a_train_number;
                                          }
                                          return null;
                                        },
                                      ),
                                      const SizedBox(height: 10),
                                      Column(
                                        children: [
                                          Text(
                                            AppLocalizations.of(context)
                                                .text_inside_train,
                                            style: const TextStyle(
                                              fontSize: 18.0,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(width: 10),
                                          ToggleSwitchWidget(
                                            onToggleCallback:
                                                _onInsideTrainToggle,
                                            enabled:
                                                _selectedTrainNumber != null &&
                                                    _selectedTrainNumber!
                                                        .isNotEmpty,
                                            initialIndex: _insideTrain ? 0 : 1,
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 10),
                                      Column(
                                        children: [
                                          Text(
                                            AppLocalizations.of(context)
                                                .text_need_alarm,
                                            style: const TextStyle(
                                              fontSize: 18.0,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          const SizedBox(width: 10),
                                          ToggleSwitchWidget(
                                            onToggleCallback:
                                                _onNeedAlarmToggle,
                                            initialIndex: _needAlarm ? 0 : 1,
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 10),
                                      _buildButton(
                                          AppLocalizations.of(context)
                                              .text_add_trains,
                                          _isLoadingAddTrains,
                                          _addTrains),
                                      SizedBox(height: padding),
                                    ],
                                  ),
                                ),
                              ),
                              Card(
                                color: Colors.white,
                                child: Padding(
                                  padding: EdgeInsets.all(padding),
                                  child: Column(
                                    children: [
                                      _buildButtonsColumn(context),
                                      SizedBox(height: padding),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
                  : Column(
                      children: <Widget>[
                        Card(
                          color: Colors.white,
                          child: Padding(
                            padding: EdgeInsets.all(padding),
                            child: _isLoadingProfile
                                ? const Center(
                                    child: CircularProgressIndicator())
                                : Column(
                                    children: [
                                      _buildProfileForm(),
                                      SizedBox(height: padding),
                                      _buildButton(
                                        AppLocalizations.of(context)
                                            .text_update_profile,
                                        _isUpdatingProfile,
                                        _updateProfile,
                                      ),
                                    ],
                                  ),
                          ),
                        ),
                        SizedBox(height: padding),
                        Card(
                          color: Colors.white,
                          child: Padding(
                            padding: EdgeInsets.all(padding),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                DropdownSearch<String>(
                                  items: _trainNumbers,
                                  selectedItem: _selectedTrainNumber,
                                  onChanged: (value) {
                                    _onTrainNumberChanged(value);
                                  },
                                  dropdownDecoratorProps:
                                      DropDownDecoratorProps(
                                    dropdownSearchDecoration: InputDecoration(
                                      labelText: AppLocalizations.of(context)
                                          .text_select_train_number,
                                      border: const OutlineInputBorder(),
                                    ),
                                  ),
                                  popupProps: PopupProps.menu(
                                    showSearchBox: true,
                                    itemBuilder: (context, item, isSelected) {
                                      return ListTile(
                                        title: Text(item),
                                        selected: isSelected,
                                      );
                                    },
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return AppLocalizations.of(context)
                                          .text_please_select_a_train_number;
                                    }
                                    return null;
                                  },
                                  enabled: !["EHK", "coach attendent", "OBHS"]
                                      .contains(userType),
                                ),
                                const SizedBox(height: 10),
                                TextFormField(
                                  decoration: InputDecoration(
                                    labelText: AppLocalizations.of(context)
                                        .text_select_date,
                                    border: const OutlineInputBorder(),
                                  ),
                                  style: const TextStyle(color: Colors.black),
                                  enabled: false,
                                  controller: TextEditingController(
                                      text: insideTrainDate),
                                ),
                                const SizedBox(height: 10),
                                Column(
                                  children: [
                                    Text(
                                      AppLocalizations.of(context)
                                          .text_inside_train,
                                      style: const TextStyle(
                                        fontSize: 18.0,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    ToggleSwitchWidget(
                                      onToggleCallback: _onInsideTrainToggle,
                                      enabled: _selectedTrainNumber != null &&
                                          _selectedTrainNumber!.isNotEmpty &&
                                          !["EHK", "coach attendent", "OBHS"]
                                              .contains(userType),
                                      initialIndex: _insideTrain ? 0 : 1,
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 10),
                                Column(
                                  children: [
                                    Text(
                                      AppLocalizations.of(context)
                                          .text_need_alarm,
                                      style: const TextStyle(
                                        fontSize: 18.0,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(width: 10),
                                    ToggleSwitchWidget(
                                      onToggleCallback: _onNeedAlarmToggle,
                                      initialIndex: _needAlarm ? 0 : 1,
                                      // enabled: !["EHK", "coach attendent", "OBHS"].contains(userType),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 10),
                                !["EHK", "coach attendent", "OBHS"]
                                        .contains(userType)
                                    ? _buildButton(
                                        AppLocalizations.of(context)
                                            .text_add_trains,
                                        _isLoadingAddTrains,
                                        _addTrains)
                                    : const SizedBox(height: 0),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: padding),
                        Card(
                          color: Colors.white,
                          child: Padding(
                            padding: EdgeInsets.all(padding),
                            child: Column(
                              children: [
                                _buildButtonsColumn(context),
                                SizedBox(height: padding),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
