import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:railops/models/index.dart'; // Assuming UserModel is defined here
import 'package:railops/services/profile_services/profile_services.dart'; // Assuming ProfileService is defined here

class ChangeEmailModal extends StatefulWidget {
  const ChangeEmailModal({super.key});

  @override
  _ChangeEmailModalState createState() => _ChangeEmailModalState();
}

class _ChangeEmailModalState extends State<ChangeEmailModal> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _newEmailController;
  late TextEditingController _otpController;

  bool _isOTPSent = false;
  bool _isGeneratingOTP = false;
  bool _isVerifyingOTP = false;
  late Timer? _timer;
  int _timerDuration = 60;
  bool _isResendEnabled = false;

  @override
  void initState() {
    super.initState();
    _newEmailController = TextEditingController();
    _otpController = TextEditingController();
  }

  @override
  void dispose() {
    _newEmailController.dispose();
    _otpController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  void _startTimer() {
    _isResendEnabled = false;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timerDuration == 0) {
        setState(() {
          _isResendEnabled = true;
          _timer?.cancel();
        });
      } else {
        setState(() {
          _timerDuration--;
        });
      }
    });
  }

  Future<void> _generateOTP(UserModel userModel) async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isGeneratingOTP = true;
        _timerDuration = 60;
      });
      try {
        final token = userModel.token; // Access token from UserModel
        final data = {'email': _newEmailController.text};
        final message =
            await ProfileService.changeEmail(token, data); // Call the service
        setState(() {
          _isOTPSent = true;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  AppLocalizations.of(context).text_otp_sent_successfully)),
        );
        _startTimer();
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text(
                  AppLocalizations.of(context).text_failed_to_send_otp('$e'))),
        );
      } finally {
        setState(() {
          _isGeneratingOTP = false;
        });
      }
    }
  }

  Future<void> _verifyOTP(UserModel userModel) async {
    if (_otpController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(AppLocalizations.of(context).text_please_enter_otp)),
      );
      return;
    }
    setState(() {
      _isVerifyingOTP = true;
    });
    try {
      final token = userModel.token; // Access token from UserModel
      final data = {
        'email': _newEmailController.text,
        'otp': _otpController.text
      };
      final message =
          await ProfileService.changeEmailOTP(token, data); // Call the service

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                AppLocalizations.of(context).text_email_saved_successfully)),
      );
      Navigator.of(context).pop(); // Close the modal after success
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(
                AppLocalizations.of(context).text_failed_to_verify_otp('$e'))),
      );
    } finally {
      setState(() {
        _isVerifyingOTP = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final userModel =
        Provider.of<UserModel>(context, listen: false); // Access UserModel

    return AlertDialog(
      title: Text(
        AppLocalizations.of(context).text_please_enter_your_email,
        style: const TextStyle(fontSize: 16), // Adjust the font size here
      ),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _newEmailController,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).text_email,
                border: const OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppLocalizations.of(context)
                      .text_please_enter_valid_email;
                }
                if (!RegExp(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
                    .hasMatch(value)) {
                  return AppLocalizations.of(context).text_invalid_email_format;
                }
                return null;
              },
            ),
            if (_isOTPSent) ...[
              const SizedBox(height: 20),
              TextFormField(
                controller: _otpController,
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context).text_otp,
                  border: const OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: _isVerifyingOTP ? null : () => _verifyOTP(userModel),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.black,
                  side: const BorderSide(color: Colors.black, width: 0.5),
                  minimumSize: const Size(double.infinity, 50),
                ),
                child: _isVerifyingOTP
                    ? const CircularProgressIndicator(color: Colors.white)
                    : Text(AppLocalizations.of(context).text_verify_otp),
              ),
            ] else ...[
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed:
                    _isGeneratingOTP ? null : () => _generateOTP(userModel),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.black,
                  side: const BorderSide(color: Colors.black, width: 0.5),
                  minimumSize: const Size(double.infinity, 50),
                ),
                child: _isGeneratingOTP
                    ? const CircularProgressIndicator(color: Colors.white)
                    : Text(AppLocalizations.of(context).text_send_otp),
              ),
            ],
            if (_isOTPSent) ...[
              const SizedBox(height: 10),
              TextButton(
                onPressed:
                    _isResendEnabled ? () => _generateOTP(userModel) : null,
                child: Text(_isResendEnabled
                    ? AppLocalizations.of(context).text_resend_otp
                    : AppLocalizations.of(context)
                        .text_resend_in_seconds(_timerDuration)),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(); // Close the modal
          },
          child: Text(AppLocalizations.of(context).text_cancel),
        ),
      ],
    );
  }
}
