import 'package:flutter/material.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:multi_dropdown/multi_dropdown.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/services/train_services/index.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class AddTrainForm extends StatefulWidget {
  const AddTrainForm({super.key});

  @override
  _AddTrainFormState createState() => _AddTrainFormState();
}

class Coach {
  final String name;
  final int id;

  Coach({required this.name, required this.id});

  @override
  String toString() {
    return 'Coach(name: $name, id: $id)';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Coach && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

class _AddTrainFormState extends State<AddTrainForm> {
  final _formKey = GlobalKey<FormState>();
  final controller = MultiSelectController<Coach>();

  String? _selectedTrainNumber;
  List<String> _trainNumbers = [];
  List<DropdownItem<Coach>> coachItems = [];
  DateTime? _selectedDate;

  bool _isSelectingAll = false;
  Set<Coach> _previousSelection = {};

  @override
  void initState() {
    super.initState();
    _fetchTrainNumbers();
  }

  Future<void> _fetchTrainNumbers() async {
    try {
      _trainNumbers = await TrainService.getTrainNumbers();
      setState(() {});
    } catch (e) {
      print('Error fetching train numbers: $e');
    }
  }

  Future<List<DropdownItem<Coach>>> fetchCoaches(String? trainNumber) async {
    if (trainNumber == null) return [];

    try {
      List<String> coachNames =
          await TrainServiceSignup.getCoaches(trainNumber);

      List<DropdownItem<Coach>> coachItems = [
        DropdownItem<Coach>(
          label: 'Select All', // This will be handled in the UI
          value: Coach(name: 'Select All', id: 0),
        ),
        ...coachNames.asMap().entries.map((entry) => DropdownItem<Coach>(
              label: entry.value,
              value: Coach(name: entry.value, id: entry.key + 1),
            ))
      ];

      controller.clearAll();
      controller.setItems(coachItems);
      return coachItems;
    } catch (e) {
      print('Error fetching coaches: $e');
      return [];
    }
  }

  void _onTrainNumberChanged(String? trainNumber) async {
    final coaches = await fetchCoaches(trainNumber);

    setState(() {
      _selectedTrainNumber = trainNumber;
      coachItems = coaches;
    });
  }

  Future<void> printSelectedFilters() async {
    final selectedTrainNumber = _selectedTrainNumber ?? 'No train selected';
    final selectedCoaches =
        controller.selectedItems.map((item) => item.value.name).toList();
    final selectedDate = _selectedDate != null
        ? "${_selectedDate!.year}-${_selectedDate!.month}-${_selectedDate!.day}"
        : 'No date selected';

    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;

    try {
      await TrainService.addTrainDetails(
        trainNumber: selectedTrainNumber,
        coachNumbers: selectedCoaches,
        originDate: selectedDate,
        token: token,
      );
      print('Train details added successfully');

      Navigator.pushNamed(context, '/add-train-profile');
    } catch (e) {
      print('Error adding train details: $e');

      showErrorDialog(
          context,
          AppLocalizations.of(context)
              .text_an_error_occurred_while_adding('$e'));
    }
  }

  void showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(AppLocalizations.of(context).text_error),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close the dialog
              },
              child: Text(AppLocalizations.of(context).text_ok),
            ),
          ],
        );
      },
    );
  }

  Future<void> _refreshPage() async {
    await _fetchTrainNumbers();
    // You can also clear or fetch coach items if needed
    if (_selectedTrainNumber != null) {
      await fetchCoaches(_selectedTrainNumber);
    }
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _refreshPage,
      child: SingleChildScrollView(
        physics:
            const AlwaysScrollableScrollPhysics(), // Ensure it's always scrollable
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 8),
              // Train Number Dropdown with Search
              DropdownSearch<String>(
                items: _trainNumbers,
                onChanged: (value) {
                  _onTrainNumberChanged(value);
                },
                dropdownDecoratorProps: DropDownDecoratorProps(
                  dropdownSearchDecoration: InputDecoration(
                    labelText:
                        AppLocalizations.of(context).text_select_train_number,
                    border: const OutlineInputBorder(),
                  ),
                ),
                popupProps: PopupProps.menu(
                  showSearchBox: true,
                  itemBuilder: (context, item, isSelected) {
                    return ListTile(
                      title: Text(item),
                      selected: isSelected,
                    );
                  },
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppLocalizations.of(context)
                        .text_please_select_a_train_number;
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              // MultiDropdown for Coaches with 'Select All' functionality
              MultiDropdown<Coach>(
                items: coachItems,
                controller: controller,
                enabled: coachItems.isNotEmpty,
                searchEnabled: true,
                chipDecoration: ChipDecoration(
                  backgroundColor: Colors.white,
                  border: Border.all(
                    color: Colors.blue,
                    width: 1.0,
                  ),
                  wrap: false,
                  runSpacing: 4,
                  spacing: 8,
                ),
                fieldDecoration: FieldDecoration(
                  hintText:
                      AppLocalizations.of(context).text_select_coaches_optional,
                  hintStyle: const TextStyle(color: Colors.black54),
                  showClearIcon: true,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.grey),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: const BorderSide(color: Colors.blueAccent),
                  ),
                ),
                dropdownDecoration: DropdownDecoration(
                  marginTop: 2,
                  maxHeight: 300,
                  header: Padding(
                    padding: const EdgeInsets.all(8),
                    child: Text(
                      AppLocalizations.of(context)
                          .text_select_coaches_from_list,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                dropdownItemDecoration: DropdownItemDecoration(
                  selectedIcon:
                      const Icon(Icons.check_box, color: Colors.green),
                  disabledIcon: Icon(Icons.lock, color: Colors.grey.shade300),
                ),
                validator: (value) {
                  // if (value == null || value.isEmpty) {
                  //   return 'Please select at least one coach';
                  // }
                  return null;
                },
                onSelectionChange: (selectedItems) {
                  final selectedItemsSet = Set<Coach>.from(selectedItems);
                  final addedItems =
                      selectedItemsSet.difference(_previousSelection);
                  final removedItems =
                      _previousSelection.difference(selectedItemsSet);

                  if (addedItems.contains(DropdownItem<Coach>(
                    label: 'Select All',
                    value: Coach(name: 'Select All', id: 0),
                  ).value)) {
                    if (!_isSelectingAll) {
                      _isSelectingAll = true;
                      setState(() {
                        controller.selectAll();
                      });
                    }
                  } else if (removedItems.contains(DropdownItem<Coach>(
                    label: 'Select All',
                    value: Coach(name: 'Select All', id: 0),
                  ).value)) {
                    if (_isSelectingAll) {
                      _isSelectingAll = false;
                      setState(() {
                        controller.clearAll();
                      });
                    }
                  } else if (_isSelectingAll && removedItems.isNotEmpty) {
                    _isSelectingAll = false;
                    setState(() {
                      controller.clearAll();
                      controller.selectWhere((item) =>
                          selectedItemsSet.contains(item.value) &&
                          item.value.id != 0);
                    });
                  }

                  if (!(selectedItemsSet.length == 1 &&
                      selectedItemsSet.contains(DropdownItem<Coach>(
                        label: 'Select All',
                        value: Coach(name: 'Select All', id: 0),
                      ).value))) {
                    _previousSelection = selectedItemsSet;
                  }
                },
              ),
              const SizedBox(height: 16),
              // Date Picker
              TextFormField(
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context).text_select_date,
                  border: const OutlineInputBorder(),
                ),
                readOnly: true,
                onTap: () async {
                  DateTime? selectedDate = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2100),
                  );
                  if (selectedDate != null) {
                    setState(() {
                      _selectedDate = selectedDate;
                    });
                  }
                },
                controller: TextEditingController(
                  text: _selectedDate != null
                      ? "${_selectedDate!.day}-${_selectedDate!.month}-${_selectedDate!.year}"
                      : '',
                ),
                validator: (value) {
                  if (_selectedDate == null) {
                    return AppLocalizations.of(context).text_please_select_date;
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              // Submit Button
              Center(
                child: ElevatedButton(
                  onPressed: () {
                    if (_formKey.currentState?.validate() ?? false) {
                      printSelectedFilters(); // Call the function to print the selected filters
                      Navigator.pushReplacementNamed(context, '/attendance');
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 32, vertical: 16),
                    textStyle: const TextStyle(fontSize: 16),
                  ),
                  child: Text(AppLocalizations.of(context).text_add_update),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
