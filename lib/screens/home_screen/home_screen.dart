import 'package:flutter/material.dart';
import 'package:railops/widgets/index.dart';
import 'package:railops/routes.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Redirect to AttendanceScreen since that's the main functionality
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Navigator.pushReplacementNamed(context, Routes.attendance);
    });

    return Scaffold(
      appBar: CustomAppBar(
        title: AppLocalizations.of(context).text_home,
        actions: const [
          // Compact language selector in AppBar
          Padding(
            padding: EdgeInsets.only(right: 8.0),
            child: LanguageSelector(isCompact: true, showLabel: false),
          ),
        ],
      ),
      drawer: const CustomDrawer(),
      body: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}
