import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:railops/screens/edit_train/widgets/index.dart';
import 'package:railops/types/train_types/zone_division_type.dart';

class EditTrainForm extends StatefulWidget {
  final TextEditingController trainNameController;
  final TextEditingController trainTypeController;
  final TextEditingController relatedTrainController;
  final TextEditingController startTimeController;
  final TextEditingController endTimeController;
  final TextEditingController chartingTimeController;
  final TextEditingController fromStationController;
  final TextEditingController toStationController;
  final TextEditingController stoppageController;

  final String? relatedTrain;
  final String? zone;
  final String? depot;
  final String? division;
  final List<int> frequency;
  final String? chartingDay;
  final List<String> stoppages;
  final String? updown;
  final Map<String, String> extraInfo;

  final List<ZoneDivision> zones;
  final List<String> divisions;
  final List<String> depots;

  final bool zonesLoading;
  final bool divisionsLoading;
  final bool depotsLoading;

  final bool isFormEnabled;

  final List<String> daysOfWeek;
  final Function() onSubmit;
  final Function(String?) onZoneChanged;
  final Function(String?) onDivisionChanged;
  final Function(String?) onDepotChanged;
  final Function(List<int>) onFrequencyChanged;
  final Function(String?) onChartingDayChanged;
  final Function(String?) onRelatedTrainChanged;
  final Function(String?) onUpDownChanged;
  final Function(List<String>) onStoppagesChanged;
  final Function(String?) onFromStationChanged;
  final Function(String?) onToStationChanged;
  final Function(String?) onTrainTypeChanged;
  final bool isBufferTimeRestriction;
  final bool isLocationRestriction;
  final bool isMediaUploadEnabled;

  const EditTrainForm({
    Key? key,
    required this.trainNameController,
    required this.trainTypeController,
    required this.relatedTrainController,
    required this.startTimeController,
    required this.endTimeController,
    required this.chartingTimeController,
    required this.fromStationController,
    required this.toStationController,
    required this.stoppageController,
    required this.relatedTrain,
    required this.zone,
    required this.depot,
    required this.division,
    required this.frequency,
    required this.chartingDay,
    required this.stoppages,
    required this.updown,
    required this.extraInfo,
    required this.zones,
    required this.divisions,
    required this.depots,
    required this.zonesLoading,
    required this.divisionsLoading,
    required this.depotsLoading,
    required this.isFormEnabled,
    required this.daysOfWeek,
    required this.onSubmit,
    required this.onZoneChanged,
    required this.onDivisionChanged,
    required this.onDepotChanged,
    required this.onFrequencyChanged,
    required this.onChartingDayChanged,
    required this.onRelatedTrainChanged,
    required this.onUpDownChanged,
    required this.onStoppagesChanged,
    required this.onFromStationChanged,
    required this.onToStationChanged,
    required this.onTrainTypeChanged,
    required this.isBufferTimeRestriction,
    required this.isLocationRestriction,
    required this.isMediaUploadEnabled,
  }) : super(key: key);

  @override
  _EditTrainFormState createState() => _EditTrainFormState();
}

class _EditTrainFormState extends State<EditTrainForm> {
  final _formKey = GlobalKey<FormState>();

  Future<void> _selectTime(
      BuildContext context, TextEditingController controller) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: controller.text.isNotEmpty
          ? TimeOfDay(
              hour: int.parse(controller.text.split(':')[0]),
              minute: int.parse(controller.text.split(':')[1]))
          : TimeOfDay.now(),
    );
    if (picked != null) {
      final now = DateTime.now();
      final dateTime =
          DateTime(now.year, now.month, now.day, picked.hour, picked.minute);
      final formattedTime = DateFormat('HH:mm').format(dateTime);
      controller.text = formattedTime;
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return Form(
      key: _formKey,
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: screenWidth * 0.05,
          vertical: screenHeight * 0.02,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TrainNameField(
              controller: widget.trainNameController,
              enabled: widget.isFormEnabled,
            ),
            SizedBox(height: screenHeight * 0.02),
            CustomDropdownField(
              value: widget.relatedTrain,
              items: const [],
              onChanged: widget.onRelatedTrainChanged,
              labelText: 'Related Train',
              right: 0.0,
              left: 0.0,
              top: 8.0,
              bottom: 8.0,
              enabled: widget.isFormEnabled,
            ),
            SizedBox(height: screenHeight * 0.02),
            Row(
              children: [
                Expanded(
                  child: CustomDropdownField(
                    value: widget.zone,
                    items: widget.zones.map((zone) => zone.code).toList(),
                    onChanged: widget.onZoneChanged,
                    labelText: 'Zone',
                    right: 4.0,
                    left: 0.0,
                    top: 8.0,
                    bottom: 8.0,
                    loading: widget.zonesLoading,
                    enabled: widget.isFormEnabled,
                  ),
                ),
                Expanded(
                  child: CustomDropdownField(
                    value: widget.division,
                    items: widget.divisions,
                    onChanged: widget.onDivisionChanged,
                    labelText: 'Division',
                    right: 4.0,
                    left: 4.0,
                    top: 8.0,
                    bottom: 8.0,
                    loading: widget.divisionsLoading,
                    enabled: widget.isFormEnabled,
                  ),
                ),
                Expanded(
                  child: CustomDropdownField(
                    value: widget.depot,
                    items: widget.depots,
                    onChanged: widget.onDepotChanged,
                    labelText: 'Depot',
                    right: 0.0,
                    left: 4.0,
                    top: 8.0,
                    bottom: 8.0,
                    loading: widget.depotsLoading,
                    enabled: widget.isFormEnabled,
                  ),
                ),
              ],
            ),
            SizedBox(height: screenHeight * 0.03),
            Text(
              "Train Frequency",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey[800],
              ),
            ),
            SizedBox(height: screenHeight * 0.01),
            Wrap(
              spacing: screenWidth * 0.02,
              runSpacing: screenHeight * 0.01,
              children: List.generate(
                7,
                (index) {
                  final day = index + 1;
                  final isSelected = widget.frequency.contains(day);
                  return FilterChip(
                    label: Text(widget.daysOfWeek[index]),
                    selected: isSelected,
                    onSelected: widget.isFormEnabled
                        ? (selected) {
                            final updatedFrequency =
                                List<int>.from(widget.frequency);
                            if (selected) {
                              if (!updatedFrequency.contains(day)) {
                                updatedFrequency.add(day);
                              }
                            } else {
                              updatedFrequency.remove(day);
                            }
                            widget.onFrequencyChanged(updatedFrequency);
                          }
                        : null,
                    backgroundColor: Colors.grey.shade200,
                    selectedColor: Colors.blue.shade100,
                    checkmarkColor: Colors.blue,
                  );
                },
              ),
            ),
            SizedBox(height: screenHeight * 0.03),
            CustomDropdownField(
              value: widget.chartingDay,
              items: const ['today', 'yesterday'],
              onChanged: widget.onChartingDayChanged,
              labelText: 'Charting Day',
              right: 0.0,
              left: 0.0,
              top: 8.0,
              bottom: 8.0,
              enabled: widget.isFormEnabled,
            ),
            SizedBox(height: screenHeight * 0.03),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: widget.fromStationController,
                    decoration: const InputDecoration(
                      labelText: "From Station",
                      border: OutlineInputBorder(),
                    ),
                    onChanged: widget.onFromStationChanged,
                    enabled: widget.isFormEnabled,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a from station';
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: screenWidth * 0.02),
                Expanded(
                  child: TextFormField(
                    controller: widget.toStationController,
                    decoration: const InputDecoration(
                      labelText: "To Station",
                      border: OutlineInputBorder(),
                    ),
                    onChanged: widget.onToStationChanged,
                    enabled: widget.isFormEnabled,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter a to station';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            SizedBox(height: screenHeight * 0.03),
            CustomDropdownField(
              value: widget.updown,
              items: const ['up', 'down'],
              onChanged: widget.onUpDownChanged,
              labelText: "Direction (Up/Down)",
              right: 0.0,
              left: 0.0,
              top: 8.0,
              bottom: 8.0,
              enabled: widget.isFormEnabled,
            ),
            SizedBox(height: screenHeight * 0.02),
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: TextFormField(
                controller: widget.startTimeController,
                decoration: const InputDecoration(
                  labelText: 'Start Time',
                  hintText: 'e.g. 09:00 AM',
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                keyboardType: TextInputType.text,
                enabled: widget.isFormEnabled,
              ),
            ),
            SizedBox(height: screenHeight * 0.02),
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: TextFormField(
                controller: widget.endTimeController,
                decoration: const InputDecoration(
                  labelText: 'End Time',
                  hintText: 'e.g. 05:00 PM',
                  border: OutlineInputBorder(),
                  contentPadding:
                      EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                ),
                keyboardType: TextInputType.text,
                enabled: widget.isFormEnabled,
              ),
            ),
            SizedBox(height: screenHeight * 0.02),
            TimeField(
              controller: widget.chartingTimeController,
              labelText: 'Charting Time',
              onTap: () => _selectTime(context, widget.chartingTimeController),
              enabled: widget.isFormEnabled,
            ),
            SizedBox(height: screenHeight * 0.03),
            TrainTypeField(
              controller: widget.trainTypeController,
              enabled: widget.isFormEnabled,
              onChanged: widget.onTrainTypeChanged,
            ),
            SizedBox(height: screenHeight * 0.03),
            Text(
              "Stoppage Sequence",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey[800],
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(vertical: screenHeight * 0.01),
              child: StoppageSequence(
                stoppages: widget.stoppages,
                stoppageController: widget.stoppageController,
                onStoppagesUpdated: widget.onStoppagesChanged,
                enabled: widget.isFormEnabled,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
