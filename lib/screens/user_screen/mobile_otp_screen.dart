import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:railops/screens/user_screen/form/otp_form.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class MobileOtpScreen extends StatefulWidget {
  const MobileOtpScreen({super.key});

  @override
  _MobileOtpScreenState createState() => _MobileOtpScreenState();
}

class _MobileOtpScreenState extends State<MobileOtpScreen> {
  String appVersion = '';

  @override
  void initState() {
    super.initState();
    _getAppVersion();
  }

  Future<void> _getAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    if (mounted) {
      setState(() {
        appVersion = packageInfo.version;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final String mobileNumber =
        ModalRoute.of(context)?.settings.arguments as String;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.blue.shade100,
        title: SizedBox(
          height: MediaQuery.of(context).size.height * 2,
          child: Container(
            color: Colors.white.withOpacity(0.1),
            child: Center(
              child: Text(
                AppLocalizations.of(context).text_mobile_otp_login,
                style: const TextStyle(
                  color: Colors.black87,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pushReplacementNamed(context, '/mobile-login');
          },
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 30.0, top: 20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(height: 2),
                Text(
                  'v$appVersion',
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 16.0,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                AppLocalizations.of(context).text_enter_otp,
                style: const TextStyle(
                    fontSize: 20,
                    color: Colors.black87,
                    fontWeight: FontWeight.bold),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              AppLocalizations.of(context)
                  .text_otp_sent_to_mobile(mobileNumber),
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            const OtpForm(),
          ],
        ),
      ),
    );
  }
}
