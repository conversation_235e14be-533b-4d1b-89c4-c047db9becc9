import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/services/train_services/upcoming_station_service.dart';
import 'package:railops/types/train_types/upcoming_station_response.dart';
import 'package:railops/widgets/index.dart';
import 'package:railops/types/train_types/train_details_response.dart';
import 'package:railops/services/train_services/index.dart';
import 'package:intl/intl.dart';
import 'package:railops/screens/train_details/widgets/index.dart';
import 'package:railops/routes.dart';
import 'package:railops/models/auth_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:geolocator/geolocator.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class TrainDetailsScreen extends StatefulWidget {
  const TrainDetailsScreen({super.key});

  @override
  _TrainDetailsScreenState createState() => _TrainDetailsScreenState();
}

class _TrainDetailsScreenState extends State<TrainDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  late Future<TrainDataResponse>? _futureTrainData;
  String _trainNumber = '';
  String insideTrainNumber = '';
  String insideTrainDate = '';
  List<String> _selectedStations = [];
  String _selectedDate = '';
  bool _isSubmitted = false;
  bool _isPopupShown = false;
  bool _hasFetchedUpcomingStations = false;
  Map<String, Map<String, List<int>>> detailsOnBoarding = {};
  Map<String, Map<String, List<int>>> detailsOffBoarding = {};
  Future<Position>? _futurePosition;
  bool _hasCheckedTrainNumber = false;

  // app update
  AppUpdateInfo? _updateInfo;
  bool _flexibleUpdateAvailable = false;

  @override
  void initState() {
    super.initState();
    _checkForUpdate();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkPopupCondition();
    });
    _futurePosition = _getCurrentLocation();
    _setInitialTrainNoFromUserModel();
  }

  void _setInitialTrainNoFromUserModel() {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final initialTrainNo = userModel.trainNo;

    if (initialTrainNo.isNotEmpty) {
      _onTrainNumberChanged(initialTrainNo);
    }
  }

  void _onTrainNumberChanged(String? trainNumber) async {
    if (trainNumber != null) {
      setState(() {
        // _trainNumber = trainNumber;
        insideTrainNumber = trainNumber;
      });
    }
  }

  Future<void> _checkForUpdate() async {
    try {
      AppUpdateInfo updateInfo = await InAppUpdate.checkForUpdate();
      if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
        setState(() {
          _updateInfo = updateInfo;
          _flexibleUpdateAvailable = true;
        });
      }
    } catch (e) {
      debugPrint("Error checking for update: $e");
    }
  }

  Future<void> _startFlexibleUpdate() async {
    if (_updateInfo != null && _flexibleUpdateAvailable) {
      try {
        await InAppUpdate.startFlexibleUpdate();
      } catch (e) {
        debugPrint("Error starting flexible update: $e");
      }
    }
  }



  void _handleFormSubmit(String? trainNumber, String? trainName,
      List<String> selectedStations, DateTime? selectedDate) async {
    DateFormat formatter = DateFormat('yyyy-MM-dd');
    String formattedDate = formatter.format(selectedDate!);

      setState(() {
        _trainNumber = trainNumber!;
      _selectedStations = selectedStations
          .where((station) =>
              station != AppLocalizations.of(context).text_select_all)
          .toList();
      _selectedDate = formattedDate;
      _futureTrainData = TrainService.fetchTrainData(
        trainNumber: trainNumber,
        date: formattedDate,
        stationCode:
            _selectedStations.isNotEmpty ? _selectedStations.join(',') : '',
      );
      _isSubmitted = true;
    });
  }

  Future<void> _checkPopupCondition() async {
    final prefs = await SharedPreferences.getInstance();
    final lastShownDate = prefs.getString('lastPopupShownDate');
    final now = DateTime.now();

    if (lastShownDate != now.toIso8601String().split('T').first &&
        now.hour >= 12 &&
        !_isPopupShown) {
      _showTrainPopup();
      await prefs.setString(
          'lastPopupShownDate', now.toIso8601String().split('T').first);
    }
  }

  Future<void> getUpcomingStationDetails(Position position) async {
    try {
      if (_hasFetchedUpcomingStations) return; // Prevent multiple calls
      _hasFetchedUpcomingStations = true; // Set the flag after first fetch

      String lat = position.latitude.toString();
      String lng = position.longitude.toString();

      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;

      UpcomingStationResponse response =
          await UpcomingStationService.fetchUpcomingStationDetails(
        lat: lat,
        lng: lng,
        token: token,
      );

      setState(() {
        detailsOnBoarding = response.details;
        detailsOffBoarding = response.detailsOffBoarding;
        insideTrainNumber = response.trainNumber;
        insideTrainDate = response.date;
        _hasCheckedTrainNumber = true;
      });

      final result = await TrainService.getTrainStations(response.trainNumber);
      List<String> stationNames = result['stationList'];

      _handleFormSubmit(response.trainNumber, "", stationNames,
          DateTime.tryParse(response.date));
    } catch (e) {
      setState(() {
        detailsOnBoarding = {};
        detailsOffBoarding = {};
        // insideTrainNumber = '';
        insideTrainDate = '';
        _hasCheckedTrainNumber = true;
      });
      debugPrint('Error: $e');
    }
  }

  Future<Position> _getCurrentLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      bool userResponse = await showDialog(
        // ignore: use_build_context_synchronously
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text(
                AppLocalizations.of(context).text_location_services_disabled),
            content: Text(AppLocalizations.of(context)
                .text_please_enable_location_services),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(false);
                },
                child: Text(AppLocalizations.of(context).text_cancel),
              ),
              TextButton(
                onPressed: () async {
                  await Geolocator.openLocationSettings();
                  // ignore: use_build_context_synchronously
                  Navigator.of(context).pop(true);
                },
                child: Text(AppLocalizations.of(context).text_enable),
              ),
            ],
          );
        },
      );

      if (!userResponse) {
        return Future.error('Location services are disabled.');
      }
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        bool userResponse = await showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: Text(
                  AppLocalizations.of(context).text_location_permission_denied),
              content: Text(AppLocalizations.of(context)
                  .text_location_permissions_required),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  child: Text(AppLocalizations.of(context).text_cancel),
                ),
                TextButton(
                  onPressed: () async {
                    await Geolocator.openAppSettings();
                    Navigator.of(context).pop(true);
                  },
                  child: Text(AppLocalizations.of(context).text_open_settings),
                ),
              ],
            );
          },
        );

        if (!userResponse) {
          return Future.error('Location permissions are denied.');
        }
      }
    }

    if (permission == LocationPermission.deniedForever) {
      await showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text(AppLocalizations.of(context)
                .text_location_permission_denied_forever),
            content: Text(AppLocalizations.of(context)
                .text_location_permissions_permanently_denied),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(AppLocalizations.of(context).text_cancel),
              ),
              TextButton(
                onPressed: () async {
                  await Geolocator.openAppSettings();
                  Navigator.of(context).pop();
                },
                child: Text(AppLocalizations.of(context).text_open_settings),
              ),
            ],
          );
        },
      );
      return Future.error(
          AppLocalizations.of(context).text_location_permissions_error);
    }

    Position position = await Geolocator.getCurrentPosition();

    setState(() {
      _futurePosition = Future.value(position);
    });

    return position;
  }

  void _showTrainPopup() {
    if (_isPopupShown) return;
    _isPopupShown = true;

    showDialog(
      context: context,
      builder: (context) {
        return TrainPopup();
      },
    ).then((_) async {
      _isPopupShown = false;
      _hasFetchedUpcomingStations = false;
      Position position = await _futurePosition!;
      await getUpcomingStationDetails(position);
    });
  }

  Future<void> _reloadPage() async {
    try {
      if (_trainNumber.isNotEmpty &&
          _selectedDate.isNotEmpty &&
          _selectedStations.isNotEmpty) {

        setState(() {
          _futureTrainData = TrainService.fetchTrainData(
            trainNumber: _trainNumber,
            date: _selectedDate,
            stationCode:
                _selectedStations.isNotEmpty ? _selectedStations.join(',') : '',
          );
          _isSubmitted = true;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
            content: Text(AppLocalizations.of(context)
                .text_refresh_failed(e.toString()))),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_flexibleUpdateAvailable) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _startFlexibleUpdate(); // Start update when available
      });
    }
    return Scaffold(
      appBar:
          CustomAppBar(title: AppLocalizations.of(context).text_train_details),
      drawer: const CustomDrawer(),
      backgroundColor: Colors.white,
      body: RefreshIndicator(
        onRefresh: _reloadPage, // Refresh function that gets called
        child: SingleChildScrollView(
          physics:
              const AlwaysScrollableScrollPhysics(), // Ensure scrolling is enabled
          child: Container(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context)
                  .size
                  .height, // Ensures it fills screen height
            ),
            child: Column(
              children: [
                const SizedBox(height: 10),
                Text(
                  AppLocalizations.of(context).text_passenger_chart,
                  style: const TextStyle(fontSize: 18.0),
                  textAlign: TextAlign.center,
                ),
                if (_hasCheckedTrainNumber)
                  Container(
                    height: 250,
                    width: MediaQuery.of(context).size.width,
                    color: Colors.green,
                    child: TrainFiltersWithStation(
                      formKey: _formKey,
                      onSubmit: _handleFormSubmit,
                      initialTrainNumber: insideTrainNumber,
                      initialDate: insideTrainDate,
                    ),
                  ),
                FutureBuilder<Position>(
                  future: _futurePosition,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    } else if (snapshot.hasError) {
                      return Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Center(
                          child: Text(
                            AppLocalizations.of(context)
                                .text_turn_on_location_services,
                            style: const TextStyle(
                                color: Colors.red, fontSize: 16),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      );
                    } else if (snapshot.hasData) {
                      if (!_hasFetchedUpcomingStations) {
                        getUpcomingStationDetails(snapshot.data!);
                      }
                      if (_hasCheckedTrainNumber) {
                        if (insideTrainNumber.isNotEmpty) {
                          return UpcomingTableWidget(
                            details: detailsOnBoarding,
                            detailsOffBoarding: detailsOffBoarding,
                          );
                        } else {
                          return Container();
                        }
                      } else {
                        return const Center(child: CircularProgressIndicator());
                      }
                    } else {
                      return Center(
                        child: Text(AppLocalizations.of(context)
                            .text_no_location_data_available),
                      );
                    }
                  },
                ),
                _isSubmitted
                    ? Padding(
                        padding: const EdgeInsets.all(0.0),
                        child: FutureBuilder<TrainDataResponse>(
                          future: _futureTrainData,
                          builder: (context, snapshot) {
                            if (snapshot.connectionState ==
                                ConnectionState.waiting) {
                              return const Center(
                                  child: CircularProgressIndicator());
                            } else if (snapshot.hasError) {
                              return Center(
                                  child: Text(AppLocalizations.of(context)
                                      .text_error_with_details(
                                          snapshot.error.toString())));
                            } else if (!snapshot.hasData ||
                                snapshot.data!.trainsData.isEmpty) {
                              return Center(
                                  child: Text(AppLocalizations.of(context)
                                      .text_no_data_available));
                            } else {
                              final authModel = Provider.of<AuthModel>(context,
                                  listen: false);
                              if (authModel.isAuthenticated) {
                                return SingleChildScrollView(
                                  child: SingleChildScrollView(
                                    scrollDirection: Axis.horizontal,
                                    child: Column(
                                      children: [
                                        Center(
                                          child: TrainDetailsTable(
                                            trainData: snapshot.data!,
                                            userModel:
                                                Provider.of<UserModel>(context),
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                );
                              } else {
                                WidgetsBinding.instance.addPostFrameCallback(
                                  (_) {
                                    Navigator.pushReplacementNamed(
                                        context, Routes.login);
                                  },
                                );
                                return const CircularProgressIndicator();
                              }
                            }
                          },
                        ),
                      )
                    : Container(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
