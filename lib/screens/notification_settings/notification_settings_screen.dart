import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/notification_provider.dart';
import 'package:railops/models/notification_preferences_model.dart';
import 'package:railops/screens/profile_screen/widgets/toggle_switch_widget.dart';
import 'package:railops/widgets/index.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  _NotificationSettingsScreenState createState() =>
      _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState
    extends State<NotificationSettingsScreen> {
  late NotificationPreferencesModel _preferences;
  bool _isLoading = false;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _loadPreferences();
  }

  void _loadPreferences() {
    final notificationProvider =
        Provider.of<NotificationProvider>(context, listen: false);
    _preferences = notificationProvider.preferences;
  }

  Future<void> _savePreferences() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final notificationProvider =
          Provider.of<NotificationProvider>(context, listen: false);
      await notificationProvider.updatePreferences(_preferences);

      setState(() {
        _hasChanges = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              AppLocalizations.of(context).msg_notification_preferences_saved),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)
              .msg_error_saving_preferences(e.toString())),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _updatePreferences(NotificationPreferencesModel newPreferences) {
    setState(() {
      _preferences = newPreferences;
      _hasChanges = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context).text_notification_settings),
        actions: [
          if (_hasChanges)
            IconButton(
              icon: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.save),
              onPressed: _isLoading ? null : _savePreferences,
              tooltip: AppLocalizations.of(context).text_save_settings,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildMainSettingsCard(),
                  const SizedBox(height: 16),
                  _buildTimingSettingsCard(),
                  const SizedBox(height: 16),
                  _buildCoachFilterCard(),
                  const SizedBox(height: 16),
                  _buildSoundVibrationCard(),
                  const SizedBox(height: 16),
                  _buildAdvancedSettingsCard(),
                  const SizedBox(height: 32),
                  if (_hasChanges) _buildSaveButton(),
                ],
              ),
            ),
    );
  }

  Widget _buildMainSettingsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context).text_onboarding_notifications,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildToggleRow(
              AppLocalizations.of(context).text_enable_onboarding_notifications,
              _preferences.enableOnboardingNotifications,
              (value) => _updatePreferences(_preferences.copyWith(
                enableOnboardingNotifications: value == 0,
              )),
            ),
            const SizedBox(height: 12),
            _buildToggleRow(
              AppLocalizations.of(context).text_station_approach_alerts,
              _preferences.enableStationApproachNotifications,
              (value) => _updatePreferences(_preferences.copyWith(
                enableStationApproachNotifications: value == 0,
              )),
              enabled: _preferences.enableOnboardingNotifications,
            ),
            const SizedBox(height: 12),
            _buildToggleRow(
              AppLocalizations.of(context).text_boarding_alerts,
              _preferences.enableBoardingAlerts,
              (value) => _updatePreferences(_preferences.copyWith(
                enableBoardingAlerts: value == 0,
              )),
              enabled: _preferences.enableOnboardingNotifications,
            ),
            const SizedBox(height: 12),
            _buildToggleRow(
              AppLocalizations.of(context).text_off_boarding_alerts,
              _preferences.enableOffBoardingAlerts,
              (value) => _updatePreferences(_preferences.copyWith(
                enableOffBoardingAlerts: value == 0,
              )),
              enabled: _preferences.enableOnboardingNotifications,
            ),
            const SizedBox(height: 12),
            _buildToggleRow(
              AppLocalizations.of(context).text_proximity_alerts,
              _preferences.enableProximityAlerts,
              (value) => _updatePreferences(_preferences.copyWith(
                enableProximityAlerts: value == 0,
              )),
              enabled: _preferences.enableOnboardingNotifications,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimingSettingsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context).text_timing_settings,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildSliderSetting(
              AppLocalizations.of(context).text_advance_notice_minutes,
              _preferences.advanceNoticeMinutes.toDouble(),
              1.0,
              60.0,
              (value) => _updatePreferences(_preferences.copyWith(
                advanceNoticeMinutes: value.round(),
              )),
              enabled: _preferences.enableOnboardingNotifications,
            ),
            const SizedBox(height: 16),
            _buildSliderSetting(
              AppLocalizations.of(context).text_proximity_threshold_km,
              _preferences.proximityThresholdKm.toDouble(),
              1.0,
              10.0,
              (value) => _updatePreferences(_preferences.copyWith(
                proximityThresholdKm: value.round(),
              )),
              enabled: _preferences.enableOnboardingNotifications &&
                  _preferences.enableProximityAlerts,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCoachFilterCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context).text_coach_filters,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildToggleRow(
              AppLocalizations.of(context).text_enable_coach_specific_filtering,
              _preferences.enableCoachSpecificFiltering,
              (value) => _updatePreferences(_preferences.copyWith(
                enableCoachSpecificFiltering: value == 0,
              )),
              enabled: _preferences.enableOnboardingNotifications,
            ),
            if (_preferences.enableCoachSpecificFiltering &&
                _preferences.enableOnboardingNotifications) ...[
              const SizedBox(height: 16),
              Text(
                AppLocalizations.of(context).text_enabled_coach_types,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              _buildCoachTypeChips(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSoundVibrationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context).text_sound_vibration,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildToggleRow(
              AppLocalizations.of(context).text_enable_sound,
              _preferences.enableSound,
              (value) => _updatePreferences(_preferences.copyWith(
                enableSound: value == 0,
              )),
            ),
            const SizedBox(height: 12),
            _buildToggleRow(
              AppLocalizations.of(context).text_enable_vibration,
              _preferences.enableVibration,
              (value) => _updatePreferences(_preferences.copyWith(
                enableVibration: value == 0,
              )),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedSettingsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context).text_advanced_settings,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildToggleRow(
              AppLocalizations.of(context).text_background_notifications,
              _preferences.enableBackgroundNotifications,
              (value) => _updatePreferences(_preferences.copyWith(
                enableBackgroundNotifications: value == 0,
              )),
              enabled: _preferences.enableOnboardingNotifications,
            ),
            const SizedBox(height: 12),
            _buildToggleRow(
              AppLocalizations.of(context).text_location_based_notifications,
              _preferences.enableLocationBasedNotifications,
              (value) => _updatePreferences(_preferences.copyWith(
                enableLocationBasedNotifications: value == 0,
              )),
              enabled: _preferences.enableOnboardingNotifications,
            ),
            const SizedBox(height: 16),
            _buildSliderSetting(
              AppLocalizations.of(context).text_max_notifications_per_hour,
              _preferences.maxNotificationsPerHour.toDouble(),
              1.0,
              50.0,
              (value) => _updatePreferences(_preferences.copyWith(
                maxNotificationsPerHour: value.round(),
              )),
              enabled: _preferences.enableOnboardingNotifications,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToggleRow(
    String title,
    bool value,
    Function(int) onChanged, {
    bool enabled = true,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            title,
            style: TextStyle(
              fontSize: 16,
              color: enabled ? Colors.black : Colors.grey,
            ),
          ),
        ),
        const SizedBox(width: 16),
        ToggleSwitchWidget(
          initialIndex: value ? 0 : 1,
          onToggleCallback: enabled ? onChanged : null,
          enabled: enabled,
        ),
      ],
    );
  }

  Widget _buildSliderSetting(
    String title,
    double value,
    double min,
    double max,
    Function(double) onChanged, {
    bool enabled = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                color: enabled ? Colors.black : Colors.grey,
              ),
            ),
            Text(
              '${value.round()}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: enabled ? Colors.blue : Colors.grey,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: (max - min).round(),
          onChanged: enabled ? onChanged : null,
          activeColor: enabled ? Colors.blue : Colors.grey,
          inactiveColor: enabled
              ? Colors.blue.withOpacity(0.3)
              : Colors.grey.withOpacity(0.3),
        ),
      ],
    );
  }

  Widget _buildCoachTypeChips() {
    const availableCoachTypes = ["AC1", "AC2", "AC3", "SL", "CC", "2S"];

    return Wrap(
      spacing: 8.0,
      runSpacing: 4.0,
      children: availableCoachTypes.map((coachType) {
        final isSelected = _preferences.enabledCoachTypes.contains(coachType);
        return FilterChip(
          label: Text(coachType),
          selected: isSelected,
          onSelected: (selected) {
            List<String> newCoachTypes =
                List.from(_preferences.enabledCoachTypes);
            if (selected) {
              newCoachTypes.add(coachType);
            } else {
              newCoachTypes.remove(coachType);
            }
            _updatePreferences(_preferences.copyWith(
              enabledCoachTypes: newCoachTypes,
            ));
          },
          selectedColor: Colors.blue.withOpacity(0.3),
          checkmarkColor: Colors.blue,
        );
      }).toList(),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _savePreferences,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                AppLocalizations.of(context).text_save_settings,
                style:
                    const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }
}
