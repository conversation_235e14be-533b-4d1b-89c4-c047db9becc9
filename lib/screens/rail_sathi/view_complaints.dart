import 'dart:io' as io;
import 'package:flutter/foundation.dart' show Uint8List, kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/services/rail_sathi_services/complain_services.dart';
import 'package:railops/services/rail_sathi_services/train_services.dart';
import 'package:railops/types/rail_sathi_types/complain_type.dart';
import 'package:railops/types/rail_sathi_types/train_type.dart';

class ViewComplaintsTab extends StatefulWidget {
  const ViewComplaintsTab({super.key});

  @override
  State<ViewComplaintsTab> createState() => _ViewComplaintsTabState();
}

class _ViewComplaintsTabState extends State<ViewComplaintsTab> {
  DateTime selectedDate = DateTime.now();
  List<Complain> complaints = [];
  bool isLoading = false;

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(2023),
      lastDate: DateTime(2100),
    );
    if (picked != null && picked != selectedDate) {
      setState(() => selectedDate = picked);
      _fetchComplaints();
    }
  }

  Future<void> _fetchComplaints() async {
    setState(() => isLoading = true);
    final userModel = Provider.of<UserModel>(context, listen: false);
    final formattedDate =
        "${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}";

    try {
      final fetchedComplaints = await ComplainServices.getComplaintsByDate(
        token: userModel.token,
        date: formattedDate,
      );
      setState(() => complaints = fetchedComplaints);
    } catch (_) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Failed to fetch complaints")),
      );
    } finally {
      setState(() => isLoading = false);
    }
  }

  Future<void> _updateComplaintDialog(Complain complaint) async {
    final token = Provider.of<UserModel>(context, listen: false).token;

    final controllerStatus =
        TextEditingController(text: complaint.complainStatus);
    final controllerDescription =
        TextEditingController(text: complaint.complainDescription);
    final controllerCoach = TextEditingController(text: complaint.coach);
    final controllerBerth =
        TextEditingController(text: complaint.berthNo.toString());
    final controllerPNR = TextEditingController(text: complaint.pnrNumber);
    final controllerName = TextEditingController(text: complaint.name);
    final controllerMobile =
        TextEditingController(text: complaint.mobileNumber);
    final controllerType = TextEditingController(text: complaint.complainType);
    final controllerTrainNumber =
        TextEditingController(text: complaint.trainNumber?.toString() ?? "");

    final List<Uint8List> mediaBytes = [];
    final List<String> mediaNames = [];

    List<Map<String, dynamic>> existingImages = List.from(complaint.media);
    final ImagePicker picker = ImagePicker();

    List<Train> trains = [];
    String isPnrValidated = complaint.isPnrValidated;

    // Track selected train
    Train? selectedTrain;
    bool isOtherTrainSelected = false;
    int? trainId;
    String? trainName;

    // Function to fetch trains with IDs
    Future<List<Train>> fetchTrainsWithId() async {
      try {
        return await RailSathiTrainService.getAllTrainsWithId();
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text("Error fetching trains: $e")),
          );
        }
        return [];
      }
    }

    // Load trains before showing the dialog
    trains = await fetchTrainsWithId();

    // First check if the complaint has a train ID
    if (complaint.train != null) {
      // Find the train in the list that matches the ID
      final trainMatch =
          trains.where((train) => train.id == complaint.train).toList();

      if (trainMatch.isNotEmpty) {
        selectedTrain = trainMatch.first;
        trainId = selectedTrain.id;
      }
    }
    // If no train ID or no match found, then check train number
    else if (complaint.trainNumber != null &&
        complaint.trainNumber!.isNotEmpty) {
      final trainMatch = trains
          .where((train) => train.trainNo == complaint.trainNumber)
          .toList();

      if (trainMatch.isNotEmpty) {
        selectedTrain = trainMatch.first;
        trainId = selectedTrain.id;
      } else {
        // If train number doesn't exist in the list, set "Other" as selected
        isOtherTrainSelected = true;
        controllerTrainNumber.text = complaint.trainNumber ?? "";
      }
    }

    Widget buildOutlinedField({
      required TextEditingController controller,
      required String label,
      TextInputType inputType = TextInputType.text,
      int maxLines = 1,
      bool enabled = true,
    }) {
      return TextField(
        controller: controller,
        keyboardType: inputType,
        maxLines: maxLines,
        enabled: enabled,
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
        ),
      );
    }

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (ctx) => StatefulBuilder(
        builder: (ctx, setModalState) {
          // Get screen width to determine layout
          final screenWidth = MediaQuery.of(ctx).size.width;
          final isSmallScreen = screenWidth < 600;

          // Determine appropriate padding based on screen size
          final horizontalPadding = isSmallScreen ? 12.0 : 16.0;

          return Container(
            padding: MediaQuery.of(ctx).viewInsets.add(
                  EdgeInsets.symmetric(
                      horizontal: horizontalPadding, vertical: 16),
                ),
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(ctx).size.height * 0.9,
            ),
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Close button and title
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Center(
                          child: Text(
                            'Edit Complaint #${complaint.complainId}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: const CircleAvatar(
                          radius: 18,
                          backgroundColor: Colors.grey,
                          child:
                              Icon(Icons.close, size: 20, color: Colors.white),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Form fields - Using responsive layout
                  // Name and Mobile fields
                  isSmallScreen
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            buildOutlinedField(
                                controller: controllerName, label: 'Name'),
                            const SizedBox(height: 12),
                            buildOutlinedField(
                              controller: controllerMobile,
                              label: 'Mobile',
                              inputType: TextInputType.phone,
                            ),
                          ],
                        )
                      : Row(
                          children: [
                            Expanded(
                                child: buildOutlinedField(
                                    controller: controllerName, label: 'Name')),
                            const SizedBox(width: 12),
                            Expanded(
                              child: buildOutlinedField(
                                controller: controllerMobile,
                                label: 'Mobile',
                                inputType: TextInputType.phone,
                              ),
                            ),
                          ],
                        ),
                  const SizedBox(height: 12),

                  // PNR and Train fields
                  isSmallScreen
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            buildOutlinedField(
                              controller: controllerPNR,
                              label: 'PNR Number',
                              enabled: !(isPnrValidated == "attempted-success"),
                            ),
                            const SizedBox(height: 12),
                            // Train dropdown with "Other" option
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                DropdownButtonFormField<Train?>(
                                  value: selectedTrain,
                                  items: [
                                    ...trains.map((train) {
                                      return DropdownMenuItem<Train>(
                                        value: train,
                                        child: Text(
                                            '${train.trainNo} - ${train.trainName}'),
                                      );
                                    }),
                                    const DropdownMenuItem<Train?>(
                                      value: null,
                                      child: Text('Other'),
                                    ),
                                  ],
                                  onChanged:
                                      (isPnrValidated == "attempted-success")
                                          ? null
                                          : (value) {
                                              setModalState(() {
                                                selectedTrain = value;
                                                if (value == null) {
                                                  isOtherTrainSelected = true;
                                                  trainId = null;
                                                } else {
                                                  isOtherTrainSelected = false;
                                                  trainId = value.id;
                                                  controllerTrainNumber.text =
                                                      value.trainNo;
                                                  trainName = value.trainName;
                                                }
                                              });
                                            },
                                  decoration: const InputDecoration(
                                    labelText: 'Train',
                                    border: OutlineInputBorder(),
                                  ),
                                  isExpanded: true,
                                ),
                                if (isOtherTrainSelected) ...[
                                  const SizedBox(height: 12),
                                  buildOutlinedField(
                                    controller: controllerTrainNumber,
                                    label: 'Enter Train Number',
                                    inputType: TextInputType.text,
                                    enabled: !(isPnrValidated ==
                                        "attempted-success"),
                                  ),
                                ],
                              ],
                            ),
                          ],
                        )
                      : Row(
                          children: [
                            Expanded(
                              child: buildOutlinedField(
                                controller: controllerPNR,
                                label: 'PNR Number',
                                enabled:
                                    !(isPnrValidated == "attempted-success"),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  DropdownButtonFormField<Train?>(
                                    value: selectedTrain,
                                    items: [
                                      ...trains.map((train) {
                                        return DropdownMenuItem<Train>(
                                          value: train,
                                          child: Text(
                                              '${train.trainNo} - ${train.trainName}'),
                                        );
                                      }),
                                      const DropdownMenuItem<Train?>(
                                        value: null,
                                        child: Text('Other'),
                                      ),
                                    ],
                                    onChanged: (isPnrValidated ==
                                            "attempted-success")
                                        ? null
                                        : (value) {
                                            setModalState(() {
                                              selectedTrain = value;
                                              if (value == null) {
                                                isOtherTrainSelected = true;
                                                trainId = null;
                                              } else {
                                                isOtherTrainSelected = false;
                                                trainId = value.id;
                                                controllerTrainNumber.text =
                                                    value.trainNo;
                                                trainName = value.trainName;
                                              }
                                            });
                                          },
                                    decoration: const InputDecoration(
                                      labelText: 'Train',
                                      border: OutlineInputBorder(),
                                    ),
                                  ),
                                  if (isOtherTrainSelected) ...[
                                    const SizedBox(height: 12),
                                    buildOutlinedField(
                                      controller: controllerTrainNumber,
                                      label: AppLocalizations.of(context)
                                          .form_enter_train_number,
                                      inputType: TextInputType.text,
                                      enabled: !(isPnrValidated ==
                                          "attempted-success"),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ],
                        ),
                  const SizedBox(height: 12),

                  // Coach and Berth fields
                  isSmallScreen
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            buildOutlinedField(
                              controller: controllerCoach,
                              label: AppLocalizations.of(context).form_coach,
                              enabled: !(isPnrValidated == "attempted-success"),
                            ),
                            const SizedBox(height: 12),
                            buildOutlinedField(
                              controller: controllerBerth,
                              label: AppLocalizations.of(context).text_berth_no,
                              inputType: TextInputType.number,
                              enabled: !(isPnrValidated == "attempted-success"),
                            ),
                          ],
                        )
                      : Row(
                          children: [
                            Expanded(
                              child: buildOutlinedField(
                                controller: controllerCoach,
                                label: AppLocalizations.of(context).form_coach,
                                enabled:
                                    !(isPnrValidated == "attempted-success"),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: buildOutlinedField(
                                controller: controllerBerth,
                                label:
                                    AppLocalizations.of(context).text_berth_no,
                                inputType: TextInputType.number,
                                enabled:
                                    !(isPnrValidated == "attempted-success"),
                              ),
                            ),
                          ],
                        ),
                  const SizedBox(height: 12),

                  // Complaint Type and Status fields
                  isSmallScreen
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            DropdownButtonFormField<String>(
                              value: controllerType.text.isNotEmpty
                                  ? controllerType.text
                                  : null,
                              items: ['cleaning', 'linen'].map((type) {
                                return DropdownMenuItem<String>(
                                  value: type,
                                  child: Text(type),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setModalState(() {
                                  controllerType.text = value!;
                                });
                              },
                              decoration: const InputDecoration(
                                labelText: 'Complaint Type',
                                border: OutlineInputBorder(),
                              ),
                              isExpanded: true,
                            ),
                            const SizedBox(height: 12),
                            DropdownButtonFormField<String>(
                              value: controllerStatus.text.isNotEmpty
                                  ? controllerStatus.text
                                  : null,
                              items: ['pending', 'completed'].map((status) {
                                return DropdownMenuItem<String>(
                                  value: status,
                                  child: Text(status),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setModalState(() {
                                  controllerStatus.text = value!;
                                });
                              },
                              decoration: const InputDecoration(
                                labelText: 'Status',
                                border: OutlineInputBorder(),
                              ),
                              isExpanded: true,
                            ),
                          ],
                        )
                      : Row(
                          children: [
                            Expanded(
                              child: DropdownButtonFormField<String>(
                                value: controllerType.text.isNotEmpty
                                    ? controllerType.text
                                    : null,
                                items: ['cleaning', 'linen'].map((type) {
                                  return DropdownMenuItem<String>(
                                    value: type,
                                    child: Text(type),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  setModalState(() {
                                    controllerType.text = value!;
                                  });
                                },
                                decoration: const InputDecoration(
                                  labelText: 'Complaint Type',
                                  border: OutlineInputBorder(),
                                ),
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: DropdownButtonFormField<String>(
                                value: controllerStatus.text.isNotEmpty
                                    ? controllerStatus.text
                                    : null,
                                items: ['pending', 'completed'].map((status) {
                                  return DropdownMenuItem<String>(
                                    value: status,
                                    child: Text(status),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  setModalState(() {
                                    controllerStatus.text = value!;
                                  });
                                },
                                decoration: const InputDecoration(
                                  labelText: 'Status',
                                  border: OutlineInputBorder(),
                                ),
                              ),
                            ),
                          ],
                        ),
                  const SizedBox(height: 16),

                  // Description field
                  buildOutlinedField(
                    controller: controllerDescription,
                    label: 'Description',
                    maxLines: 4,
                  ),
                  const SizedBox(height: 20),

                  // Existing images section
                  if (existingImages.isNotEmpty) ...[
                    const Text('Existing Images:',
                        style: TextStyle(fontWeight: FontWeight.w600)),
                    const SizedBox(height: 10),
                    SizedBox(
                      height: 100,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: existingImages.length,
                        itemBuilder: (context, index) {
                          final image = existingImages[index];
                          return Padding(
                            padding: const EdgeInsets.only(right: 10),
                            child: Stack(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(10),
                                  child: Image.network(
                                    image['media_url'],
                                    height: 100,
                                    width: 120,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                Positioned(
                                  top: 4,
                                  right: 4,
                                  child: GestureDetector(
                                    onTap: () async {
                                      final confirm = await showDialog<bool>(
                                        context: context,
                                        builder: (_) => AlertDialog(
                                          title: const Text("Delete Image"),
                                          content: const Text(
                                              "Are you sure you want to delete this image?"),
                                          actions: [
                                            TextButton(
                                              onPressed: () =>
                                                  Navigator.pop(context, false),
                                              child: const Text("Cancel"),
                                            ),
                                            ElevatedButton(
                                              onPressed: () =>
                                                  Navigator.pop(context, true),
                                              child: const Text("Delete"),
                                            ),
                                          ],
                                        ),
                                      );

                                      if (confirm == true) {
                                        try {
                                          await ComplainServices
                                              .deleteComplaintImage(
                                            token: token,
                                            complainId: complaint.complainId,
                                            mediaIds: [image['id']],
                                          );
                                          if (mounted) {
                                            _fetchComplaints();
                                            setModalState(() {
                                              existingImages.removeAt(index);
                                            });
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              const SnackBar(
                                                  content:
                                                      Text("Image deleted")),
                                            );
                                          }
                                        } catch (e) {
                                          if (mounted) {
                                            ScaffoldMessenger.of(context)
                                                .showSnackBar(
                                              SnackBar(
                                                  content: Text(
                                                      "Failed to delete image: $e")),
                                            );
                                          }
                                        }
                                      }
                                    },
                                    child: const CircleAvatar(
                                      radius: 12,
                                      backgroundColor: Colors.red,
                                      child: Icon(Icons.close,
                                          size: 16, color: Colors.white),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                  const SizedBox(height: 20),

                  // New images section
                  if (mediaBytes.isNotEmpty) ...[
                    const Text('Newly Selected Images:',
                        style: TextStyle(fontWeight: FontWeight.w600)),
                    const SizedBox(height: 10),
                    SizedBox(
                      height: 100,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: mediaBytes.length,
                        itemBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.only(right: 10),
                            child: Stack(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(10),
                                  child: Image.memory(
                                    mediaBytes[index],
                                    height: 100,
                                    width: 120,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                Positioned(
                                  top: 4,
                                  right: 4,
                                  child: GestureDetector(
                                    onTap: () {
                                      setModalState(() {
                                        mediaBytes.removeAt(index);
                                        mediaNames.removeAt(index);
                                      });
                                    },
                                    child: const CircleAvatar(
                                      radius: 12,
                                      backgroundColor: Colors.red,
                                      child: Icon(Icons.close,
                                          size: 16, color: Colors.white),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                  const SizedBox(height: 12),

                  // Add image button
                  Align(
                    alignment: Alignment.centerLeft,
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        final XFile? picked =
                            await picker.pickImage(source: ImageSource.gallery);
                        if (picked != null) {
                          final bytes = await picked.readAsBytes();
                          setModalState(() {
                            mediaBytes.add(bytes);
                            mediaNames.add(picked.name);
                          });
                        }
                      },
                      icon: const Icon(Icons.add_photo_alternate),
                      label: const Text('Add Image'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Save button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () async {
                        Navigator.pop(ctx);
                        // Prepare the payload based on the train selection
                        Map<String, dynamic> payload = {
                          'token': token,
                          'complainId': complaint.complainId,
                          'mediaBytes': mediaBytes,
                          'mediaNames': mediaNames,
                          'name': controllerName.text,
                          'mobile_number': controllerMobile.text,
                          'pnr_number': controllerPNR.text,
                          'coach': controllerCoach.text,
                          'berth_no': int.tryParse(controllerBerth.text),
                          'complain_type': controllerType.text,
                          'complain_status': controllerStatus.text,
                          'complain_description': controllerDescription.text,
                        };

                        // Add train or train_number based on selection
                        if (isOtherTrainSelected) {
                          // For custom train number
                          payload['train_number'] = controllerTrainNumber.text;
                        } else if (trainId != null) {
                          // For selected train from dropdown
                          payload['train'] = trainId;
                        }

                        final updated = await ComplainServices.updateComplaint(
                          token: token,
                          complainId: complaint.complainId,
                          mediaBytes: mediaBytes,
                          mediaNames: mediaNames,
                          name: controllerName.text,
                          mobileNumber: controllerMobile.text,
                          pnrNumber: controllerPNR.text,
                          coach: controllerCoach.text,
                          berthNo: int.tryParse(controllerBerth.text),
                          complainType: controllerType.text,
                          complainStatus: controllerStatus.text,
                          complainDescription: controllerDescription.text,
                          trainNumber: isOtherTrainSelected
                              ? controllerTrainNumber.text
                              : null,
                          train: !isOtherTrainSelected && trainId != null
                              ? trainId
                              : null,
                        );

                        if (mounted) {
                          if (updated) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                  content: Text('Complaint updated')),
                            );
                            _fetchComplaints();
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('Update failed')),
                            );
                          }
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        backgroundColor: Colors.blue,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10)),
                      ),
                      child: const Text('Save Changes',
                          style: TextStyle(fontSize: 16)),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _deleteComplaint(int complainId) async {
    final token = Provider.of<UserModel>(context, listen: false).token;

    final confirm = await showDialog<bool>(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text("Delete Complaint"),
        content: const Text("Are you sure you want to delete this complaint?"),
        actions: [
          TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text("Cancel")),
          ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text("Delete")),
        ],
      ),
    );

    if (confirm == true) {
      final success = await ComplainServices.deleteComplaint(
        token: token,
        complainId: complainId,
      );
      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text("Complaint deleted successfully")),
          );
          _fetchComplaints();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text("Failed to delete complaint")),
          );
        }
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _fetchComplaints();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  'Selected Date: ${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
                ),
              ),
              ElevatedButton(
                onPressed: () => _selectDate(context),
                child: const Text('Select Date'),
              ),
            ],
          ),
        ),
        isLoading
            ? const Center(child: CircularProgressIndicator())
            : complaints.isEmpty
                ? const Center(child: Text("No complaints found"))
                : Expanded(
                    child: ListView.builder(
                      itemCount: complaints.length,
                      itemBuilder: (context, index) {
                        final complaint = complaints[index];

                        return Card(
                          margin: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                          elevation: 3,
                          child: Padding(
                            padding: const EdgeInsets.all(12),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Train No: ${complaint.trainNumber}',
                                    style: const TextStyle(
                                        fontWeight: FontWeight.w500)),
                                Text('Date: ${complaint.complainDate}'),
                                Row(
                                  children: [
                                    Text('PNR: ${complaint.pnrNumber}'),
                                    if ((complaint.isPnrValidated ==
                                        "attempted-success"))
                                      const Padding(
                                        padding: EdgeInsets.only(left: 8.0),
                                        child: Icon(
                                          Icons.check_circle,
                                          color: Colors.green,
                                          size: 20,
                                        ),
                                      ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                if (complaint.mediaUrls.isNotEmpty)
                                  SizedBox(
                                    height: 50,
                                    child: ListView.builder(
                                      scrollDirection: Axis.horizontal,
                                      itemCount: complaint.mediaUrls.length,
                                      itemBuilder: (context, imgIndex) {
                                        final imgUrl =
                                            complaint.mediaUrls[imgIndex];
                                        return Padding(
                                          padding:
                                              const EdgeInsets.only(right: 6.0),
                                          child: ClipRRect(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            child: Image.network(
                                              imgUrl,
                                              height: 50,
                                              width: 60,
                                              fit: BoxFit.cover,
                                              errorBuilder: (_, __, ___) =>
                                                  const Icon(Icons.broken_image,
                                                      size: 30),
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                const SizedBox(height: 8),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    ElevatedButton(
                                      onPressed: () =>
                                          _updateComplaintDialog(complaint),
                                      child: const Text('Edit'),
                                    ),
                                    ElevatedButton(
                                      onPressed: () => _deleteComplaint(
                                          complaint.complainId),
                                      style: ElevatedButton.styleFrom(
                                          backgroundColor: Colors.red),
                                      child: const Text('Delete'),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
      ],
    );
  }
}
