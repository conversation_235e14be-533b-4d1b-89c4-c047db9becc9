import 'dart:io' as io;
import 'package:flutter/foundation.dart' show Uint8List, kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:railops/screens/rail_sathi/view_complaints.dart';
import 'package:railops/screens/rail_sathi/write_complaint.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';

class RailSathi extends StatelessWidget {
  const RailSathi({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: CustomAppBar(
            title: AppLocalizations.of(context).rail_sathi_app_bar_title),
        drawer: const CustomDrawer(),
        body: Column(
          children: [
            TabBar(
              tabs: [
                Tab(text: AppLocalizations.of(context).tab_write_complaint),
                Tab(text: AppLocalizations.of(context).tab_view_complaints),
              ],
            ),
            const Expanded(
              child: Tab<PERSON><PERSON><PERSON>ie<PERSON>(
                children: [
                  WriteComplaintTab(),
                  ViewComplaintsTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
