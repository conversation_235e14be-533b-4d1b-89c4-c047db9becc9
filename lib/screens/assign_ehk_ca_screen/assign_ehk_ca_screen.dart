// ignore_for_file: prefer_collection_literals

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/routes.dart';
import 'package:railops/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart';
import 'package:railops/screens/attendance/image_upload.dart';
import 'package:railops/screens/feedback_screens/passenger_feedback_screen.dart';
import 'package:railops/screens/user_screen/widgets/index.dart';
import 'package:railops/services/assign_ehk_ca_services/assign_ehk_ca_services.dart';
import 'package:railops/services/assign_ehk_ca_services/job_chart_status_services.dart';
import 'package:railops/services/assign_ehk_ca_services/return_gap_services.dart';
import 'package:railops/services/attendance_services/attendance_sevices.dart';
import 'package:railops/services/edit_train_services/edit_train_services.dart';
import 'package:railops/services/train_services/train_details_services.dart';
import 'package:railops/types/assign_ehk_ca_types/job_chart_status_types.dart';
import 'package:railops/types/assign_ehk_ca_types/return_gap_types.dart';
import 'package:railops/widgets/index.dart';
import '../../models/index.dart';
import '../../models/user_model.dart';
import 'widgets/assign_ehk_ca_table.dart';
import 'widgets/assign_ehk_ca_filters.dart';
import 'widgets/return_gap.dart';
import 'package:in_app_update/in_app_update.dart';
import 'widgets/status.dart';

class AssignEhkCaScreen extends StatefulWidget {
  const AssignEhkCaScreen({Key? key}) : super(key: key);

  @override
  _AssignEhkCaScreenState createState() => _AssignEhkCaScreenState();
}

class _AssignEhkCaScreenState extends State<AssignEhkCaScreen> {
  bool displaySidebar = true;
  String? train;
  Map<String, String> stationNames = {};
  List<String> stations = [];
  List<String> coaches = [];
  List<String> ehkUsers = [];
  List<String> caUsers = [];
  String? date;
  bool showLoader = false;
  String loaderText = 'Loading...'; 
  bool msgModalFlag = false;
  String message = '';
  String modalType = '';
  Map<String, dynamic>? upTrainData;
  Map<String, dynamic>? downTrainData;
  bool hasAccess = false;

  List<String> selectedEhk1 = [];
  List<String> selectedEhk2 = [];
  Map<String, List<String>> selectedUsers1 = {};
  Map<String, List<String>> selectedUsers2 = {};
  Map<String, String>cashCarry1={};
  Map<String, String>cashCarry2={};

  ReturnGapData? returnGapData;

  Map<String, List<String>> upPendingDeletions = {};
  Map<String, List<String>> downPendingDeletions = {};
  final GlobalKey<State<AssignEhkCaTable>> upTableKey =
      GlobalKey<State<AssignEhkCaTable>>();
  final GlobalKey<State<AssignEhkCaTable>> downTableKey =
      GlobalKey<State<AssignEhkCaTable>>();

  // app update
  AppUpdateInfo? _updateInfo;
  bool _flexibleUpdateAvailable = false;

  void toggleSideBar() {
    setState(() {
      displaySidebar = !displaySidebar;
    });
  }

  void onSubmit(String trainNumber, String selectedDate) {
    setState(() {
      train = trainNumber;
      date = selectedDate;
      showLoader = true;
      loaderText = 'Loading train data...';
    });
    
    fetchReturnGap(trainNumber).then((_) {
      return fetchSpecificUsers("EHK", trainNumber);
    })
    // .then((_) {
    //   return fetchSpecificUsers("coach attendent", trainNumber);
    // })
    .then((_) {
      return fetchTrainsCoachWise(trainNumber, selectedDate);
    }).then((_) {
      setState(() {
        showLoader = false;
      });
      clearAllTablePendingDeletions();
    }).catchError((error) {
      setState(() {
        showLoader = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading data: ${error.toString()}')),
      );
    });
  }

  void clearSeletections() {
    setState(() {
      selectedEhk1 = [];
      selectedEhk2 = [];
      selectedUsers1 = {};
      selectedUsers2 = {};
      upPendingDeletions = {};
      downPendingDeletions = {};
      (downTableKey.currentState as AssignEhkCaTableState).clearAmount();
      (upTableKey.currentState as AssignEhkCaTableState).clearAmount();
      cashCarry1={};
      cashCarry2={};
    });
  }

  void clearSeletectionsUp() {
    setState(() {
      (upTableKey.currentState as AssignEhkCaTableState).clearAmount();
      selectedEhk1 = [];
      selectedUsers1 = {};
      upPendingDeletions = {};
       cashCarry1={};
    });
  }

  void clearSeletectionsDown() {
    setState(() {
      (downTableKey.currentState as AssignEhkCaTableState).clearAmount();
      selectedEhk2 = [];
      selectedUsers2 = {};
      downPendingDeletions = {};
      cashCarry2={};
    });
  }

  Future<void> fetchCoaches(String? trainNumber) async {
    if (trainNumber != null) {
      setState(() {
        showLoader = true;
        loaderText = 'Loading coaches...';
      });
      
      try {
        final response = await AdminAssignService.fetchCoaches(trainNumber);
        setState(() {
          coaches = response;
        });
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading coaches: ${e.toString()}')),
        );
      } finally {
        setState(() => showLoader = false);
      }
    }
  }

  Future<void> fetchTrainsCoachWise(
      String trainNumber, String selectedDate) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;
    const forUserType = "coach attendent";
    if (token.isNotEmpty) {
      setState(() {
        showLoader = true;
        loaderText = 'Loading train data...';
      });
      
      try {
        final response = await AdminAssignService.fetchTrainsCoachWise(
            trainNumber, selectedDate, forUserType, token);
        setState(() {
          upTrainData = response['out'];
          downTrainData = response['in'];
          hasAccess = response['has_access'];
        });
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading train data: ${e.toString()}')),
        );
      } finally {
        setState(() => showLoader = false);
      }
    }
  }

  Future<void> fetchSpecificUsers(String userType, String trainNumber) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;

    if (token.isNotEmpty) {
      setState(() {
        showLoader = true;
        loaderText = 'Loading users...';
      });
      
      try {
        final response =
            await AdminAssignService.fetchSpecificUsersDivision(userType, token, trainNumber);

        setState(() {
          if (userType == "EHK") {
            ehkUsers = response.map<String>((ehk) => ehk["username"]).toList();
          } else if (userType == "coach attendent") {
            caUsers = response.map<String>((ca) => ca["username"]).toList();
          }
        });
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading ${userType} users: ${e.toString()}')),
        );
      } finally {
        setState(() => showLoader = false);
      }
    }
  }

  Future<void> addTrainsCoachWise(
      Map<String, dynamic> data,
      Map<String, List<String>> dataEhk,
      String trainNumber,
      String dateParam) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;
    const forUserType = "coach attendent";
    setState(() {
      showLoader = true;
      loaderText = 'Updating train assignments...';
    });

    try {
      for (var entry in upPendingDeletions.entries) {
        final keyParts =
            entry.key.split('-'); // Split key into username and userType
        final username = keyParts[0];
        final userType = keyParts[1];

        await removeTrainDetailsOriginDate(
          username,
          entry.value,
          upTrainData!['train_no'],
          upTrainData!['date'],
          userType,
        );
      }

      for (var entry in downPendingDeletions.entries) {
        final keyParts =
            entry.key.split('-'); 
        final username = keyParts[0];
        final userType = keyParts[1];

        await removeTrainDetailsOriginDate(
          username,
          entry.value,
          downTrainData!['train_no'].toString(),
          downTrainData!['date'],
          userType,
        );
      }

      final response = await AdminAssignService.addTrainsCoachWise(
          data, dataEhk, trainNumber, dateParam, forUserType, token);
      setState(() {
        message = response["message"];
        msgModalFlag = true;
      });

      showModal(message);
      onSubmit(train!, date!);
    } catch (e) {
      setState(() {
        message = 'An error occurred: $e';
        msgModalFlag = true;
      });
      showModal(message);
    } finally {
      setState(() => showLoader = false);
    }
  }

  Future<void> removeTrainDetailsOriginDate(
      String username,
      List<String> coachNumbers,
      String trainNumber,
      String dateParam,
      String forUserType) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;
    
    try {
      await AdminAssignService.removeTrainDetailsOriginDate(
          username, coachNumbers, trainNumber, dateParam, forUserType, token);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error removing assignments: ${e.toString()}')),
      );
    }
  }

  Future<String> fetchLastJourneyDate(String username) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;
    
    // setState(() {
    //   showLoader = true;
    //   loaderText = 'Checking last journey...';
    // });
    
    try {
      final response =
          await AdminAssignService.fetchLastJourneyDate(username, token);
      return response;
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error fetching last journey: ${e.toString()}')),
      );
      return "";
    } finally {
      setState(() => showLoader = false);
    }
  }

  void handlePendingDeletionsChanged(
      Map<String, List<String>> deletions, bool isUp) {
    setState(() {
      if (isUp) {
        upPendingDeletions = deletions;
      } else {
        downPendingDeletions = deletions;
      }
    });
  }

  void clearAllTablePendingDeletions() {
    // Access the public method through the widget instance
    (upTableKey.currentWidget as AssignEhkCaTable?)?.clearPendingDeletions();
    (downTableKey.currentWidget as AssignEhkCaTable?)?.clearPendingDeletions();
  }

  @override
  void initState() {
    super.initState();
    _checkForUpdate();
    fetchCoaches(train);
  }

  Future<void> _checkForUpdate() async {
    try {
      AppUpdateInfo updateInfo = await InAppUpdate.checkForUpdate();
      if (updateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
        setState(() {
          _updateInfo = updateInfo;
          _flexibleUpdateAvailable = true;
        });
      }
    } catch (e) {
      print("Error checking for update: $e");
    }
  }

  Future<void> _startFlexibleUpdate() async {
    if (_updateInfo != null && _flexibleUpdateAvailable) {
      try {
        await InAppUpdate.startFlexibleUpdate();
      } catch (e) {
        print("Error starting flexible update: $e");
      }
    }
  }

  // Complete the flexible update
  Future<void> _completeUpdate() async {
    try {
      await InAppUpdate.completeFlexibleUpdate();
      setState(() {
        _flexibleUpdateAvailable = false;
      });
    } catch (e) {
      print("Error completing update: $e");
    }
  }

  void _handleUpchange(String type, dynamic value,Map<String, String> amountInHand) {
    print("Ehk_list: $type , Value: $value");
    if (type == 'ehk-user') {
      bool hasRightCellValueEhk = downTrainData!['ehk_dict'].isNotEmpty &&
          downTrainData!['ehk_dict']
              .entries!
              .any((entry) => entry.value == downTrainData!['date']);

      setState(() {
        selectedEhk1 = List<String>.from(value);

        if (!hasRightCellValueEhk) {
          selectedEhk2 = List<String>.from(value);
        }
      });
    } else if (type.startsWith('ca-user-')) {
      final coach = type.replaceFirst('ca-user-', '');
      setState(() {
        for (var val in value) {
           cashCarry1[val] = amountInHand[val] ?? "0";
           cashCarry2[val] = amountInHand[val] ?? "0";
        }
        // print("CashCarry1 : $cashCarry1");
        selectedUsers1[coach] = List<String>.from(value);
        selectedUsers2[coach] = List<String>.from(value);
        bool hasRightCellValue = downTrainData!['coach_wise_dict']
                .containsKey(coach) &&
            downTrainData!['coach_wise_dict'][coach]!
                .any((user) => user['origin_date'] == downTrainData!['date']);

        if (!hasRightCellValue) {
          selectedUsers2[coach] = List<String>.from(value);
        }
      });
    }

    print("Selected User : $selectedUsers1 and Cash Carry : $cashCarry1");
  }

  void _handleDownchange(String type, dynamic value,Map<String, String> amountInHand) {
    print("Ehk_list: $type , Value: $value");
    if (type == 'ehk-user') {
      // Check if up table's EHK has existing entries for the current date
      bool hasRightCellValueEhkUp = upTrainData!['ehk_dict'].isNotEmpty &&
          upTrainData!['ehk_dict'].entries.any((entry) =>
              entry.value['origin_date'] == upTrainData!['date']);

      setState(() {
        selectedEhk2 = List<String>.from(value);
        // Propagate to up table if no existing EHK
        if (!hasRightCellValueEhkUp) {
          selectedEhk1 = List<String>.from(value);
        }
      });
    } else if (type.startsWith('ca-user-')) {
      final coach = type.replaceFirst('ca-user-', '');
      // Check if up table's coach has existing CA entries
      bool hasRightCellValueUp = upTrainData!['coach_wise_dict'].containsKey(coach) &&
          upTrainData!['coach_wise_dict'][coach]!.any((user) =>
              user['origin_date'] == upTrainData!['date']);

      setState(() {
        selectedUsers2[coach] = List<String>.from(value);
        // cashCarry2[coach] = amountInHand[coach] ?? "0";
        for (var val in value) {
          cashCarry2[val] = amountInHand[val] ?? "0";
        }
        // Propagate to up table if no existing CA for the coach
        if (!hasRightCellValueUp) {
          selectedUsers1[coach] = List<String>.from(value);
        }
      });
    }
  }

  Future<ReturnGapData?> fetchReturnGap(String trainId) async {
    setState(() {
      showLoader = true;
      loaderText = 'Loading return gap data...';
    });
    
    try {
      final tempReturnGapData =
          await ReturnGapService.fetchReturnGapByTrain(trainId);
      setState(() {
        returnGapData = tempReturnGapData;
      });
      if (returnGapData != null) {
        print('Return Gap Data fetched successfully: ${tempReturnGapData}');
      } else {
        print('No Return Gap Data found for Train ID: $trainId');
      }
      return returnGapData;
    } catch (e) {
      print('Error fetching Return Gap Data: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading return gap data: ${e.toString()}')),
      );
      setState(() {
        returnGapData = null;
      });
      return null;
    } finally {
      setState(() => showLoader = false);
    }
  }

  Future<String?> addOrUpdateReturnGapWithParams(
      {required int days, required String inOut}) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;

    try {
      final request = ReturnGapRequest(
          days: days, trainNo: train!, token: token, inOut: inOut);
      final returnGapData =
          await ReturnGapService.addOrUpdateReturnGap(request);
      onSubmit(train!, date!);
      return 'Return Gap Data added/updated successfully';
    } catch (e) {
      print('Error adding/updating Return Gap Data: $e');
      return null;
    }
  }

  void handleReturnGapSubmission(int? gap, String? inOut) {
    if (gap != null) {
      addOrUpdateReturnGapWithParams(days: gap, inOut: inOut!);
    }
  }

  Future<void> getStations(String trainNumber) async {
    setState(() {
      showLoader = true;
      loaderText = 'Loading stations...';
    });
    
    try {
      final result = await TrainService.getTrainStations(trainNumber!);
      List<String> _stations = result['stationList'];
      final Map<String, String> _stationNames = result['stationsDict'];
      setState(() {
        stationNames = _stationNames;
        stations = _stations;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error loading stations: ${e.toString()}')),
      );
    } finally {
      setState(() => showLoader = false);
    }
  }

  Future<void> _reloadPage() async {    
    setState(() {
      showLoader = true;
      loaderText = 'Refreshing data...';
    });
    
    try {
      if (train != null) {
        // Re-fetch all relevant data
        await fetchTrainsCoachWise(train!, date!);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Data refreshed successfully')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Refresh failed: ${e.toString()}')),
      );
    } finally {
      setState(() => showLoader = false);
      await fetchTrainsCoachWise(train!, date!);
    }
  }

  Future<void> submitJobChartStatus(trainNumber, originDate, statusFor, selectedStatus, token) async {
    setState(() {
      showLoader = true;
      loaderText = 'Updating job chart status...';
    });

    final request = JobChartStatusRequest(
      trainNumber: trainNumber,
      date: originDate,
      statusFor: statusFor,
      status: selectedStatus,
      token: token,
    );

    try {
      await JobChartStatusService.addJobChartStatus(request);
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('Job Chart Status Added')));
    } catch (e) {
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('Error: $e')));
    } finally {
      setState(() => showLoader = false);
    }
    fetchTrainsCoachWise(train!, date!);
  }

  @override
  Widget build(BuildContext context) {
    if (_flexibleUpdateAvailable) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _startFlexibleUpdate(); // Start update when available
      });
    }

    final userModel = Provider.of<UserModel>(context, listen: false);
    String userType = userModel.userType;

    return 
    Scaffold(
      appBar: const CustomAppBar(title: 'Assign Multiple Users'),
      drawer: const CustomDrawer(),
      body: Stack(children: [
      RefreshIndicator(
        onRefresh: _reloadPage,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Container(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context)
                  .size
                  .height, // Ensures it fills screen height
            ),
            child: Stack(
              children: [
                Padding(
                  padding: const EdgeInsets.all(1),
                  child: Column(
                    children: [
                      const SizedBox(height: 10),
                      const Text(
                        'Assign CA',
                        style: TextStyle(fontSize: 18.0),
                        textAlign: TextAlign.center,
                      ),
                      AssignEhkCaFilters(
                        onSubmit: (trainNumber, selectedDate) {
                          onSubmit(trainNumber!, selectedDate!);
                          clearSeletections();
                          
                        },
                      ),

                      const SizedBox(height: 16),
                      if (upTrainData != null &&
                          upTrainData!['coaches'] != null &&
                          userType != 'coach attendent')
                        Center(
                          child: Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: ElevatedButton(
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.white,
                                minimumSize: const Size(150, 40),
                                side: const BorderSide(color: Colors.black87, width: 0.5),
                              ),
                              onPressed: () async {
                                _ImageUpload(
                                    context,
                                    JobChartImageUploadPage(
                                      trainNumber: upTrainData!['train_no']!,
                                      journeyDate: upTrainData!['date']!,
                                      uploadedFor:"ca",
                                      status: upTrainData!['status'],
                                    ));
                              },
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Text(
                                    "Upload Job Chart Image",
                                    style: TextStyle(
                                      fontSize: 15.0,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(width: 5.0),
                                  if (upTrainData!["image_uploaded"] == true)
                                    const Row(
                                      children: [
                                        Icon(
                                          Icons.image,
                                          color: Colors.white,
                                          size: 18.0,
                                        ),
                                        Icon(
                                          Icons.check,
                                          color: Colors.white,
                                          size: 18.0,
                                        ),
                                      ],
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ),

                      const SizedBox(height: 16),
                      if (upTrainData != null &&
                          upTrainData!['coaches'] != null)
                        AssignEhkCaTable(
                          key: upTableKey,
                          coaches: List<String>.from(upTrainData!['coaches']),
                          ehkUsers: [...ehkUsers, ...caUsers].toSet().toList()
                            ..sort(
                              (a, b) =>
                                  a.toLowerCase().compareTo(b.toLowerCase()),
                            ),
                          caUsers: [...ehkUsers, ...caUsers].toSet().toList()
                            ..sort(
                              (a, b) =>
                                  a.toLowerCase().compareTo(b.toLowerCase()),
                            ),
                          coachWiseDict: upTrainData!['coach_wise_dict']
                              .cast<String, dynamic>(),
                          ehkDict:
                              upTrainData!['ehk_dict'].cast<String, dynamic>(),
                          date: upTrainData!['date'],
                          train: upTrainData!['train_no'],
                          down: false,
                          hasAccess: hasAccess && upTrainData!['status'] == "pending",
                          onSubmit: addTrainsCoachWise,
                          selectedUsers: selectedUsers1,
                          setSelectedUsers: (value) =>
                              setState(() => selectedUsers1 = value),
                          selectedEhk: selectedEhk1,
                          setSelectedEhk: (value) =>
                              setState(() => selectedEhk1 = value),
                          fetchLastJourneyDate: fetchLastJourneyDate,
                          // removeTrainDetailsOriginDate:
                          //     removeTrainDetailsOriginDate,
                          handleUpchange: _handleUpchange,
                          clearSeletection: clearSeletectionsUp,
                          updateBothTablesVisible: (upTrainData != null &&
                              upTrainData!['coaches'] != null &&
                              downTrainData != null &&
                              downTrainData!['coaches'] != null &&
                              hasAccess),
                          onPendingDeletionsChanged: (deletions) =>
                              handlePendingDeletionsChanged(deletions, true),
                          status: upTrainData!['status']
                        ),

                      // if (upTrainData != null &&
                      //     upTrainData!['coaches'] != null)
                      //   JobChartStatusWidget(trainNumber:  upTrainData!['train_no'],originDate:  upTrainData!['date'],statusFor:  "ca", defaultStatus: upTrainData!['status'],submitJobChartStatus:submitJobChartStatus),

                      const SizedBox(height: 16),
                      if (downTrainData != null &&
                          downTrainData!['coaches'] != null)
                        AssignEhkCaTable(
                          key: downTableKey,
                          coaches: List<String>.from(downTrainData!['coaches']),
                          ehkUsers: [...ehkUsers, ...caUsers].toSet().toList()
                            ..sort(
                              (a, b) =>
                                  a.toLowerCase().compareTo(b.toLowerCase()),
                            ),
                          caUsers: [...ehkUsers, ...caUsers].toSet().toList()
                            ..sort(
                              (a, b) =>
                                  a.toLowerCase().compareTo(b.toLowerCase()),
                            ),
                          coachWiseDict: downTrainData!['coach_wise_dict'],
                          ehkDict: downTrainData!['ehk_dict'],
                          date: downTrainData!['date'],
                          train: downTrainData!['train_no'].toString(),
                          down: true,
                          hasAccess: hasAccess && downTrainData!['status'] == "pending",
                          onSubmit: addTrainsCoachWise,
                          selectedUsers: selectedUsers2,
                          setSelectedUsers: (value) =>
                              setState(() => selectedUsers2 = value),
                          selectedEhk: selectedEhk2,
                          setSelectedEhk: (value) =>
                              setState(() => selectedEhk2 = value),
                          fetchLastJourneyDate: fetchLastJourneyDate,
                          // removeTrainDetailsOriginDate:
                          //     removeTrainDetailsOriginDate,
                          // handleUpchange: (String type, dynamic value) {
                          //   if (type == 'ehk-user') {
                          //     setState(() {
                          //       selectedEhk2 = List<String>.from(value);
                          //     });
                          //   } else if (type.startsWith('ca-user-')) {
                          //     final coach = type.replaceFirst('ca-user-', '');
                          //     setState(() {
                          //       selectedUsers2[coach] =
                          //           List<String>.from(value);
                          //     });
                          //   }
                          // },
                          handleUpchange: _handleDownchange,
                          clearSeletection: clearSeletectionsDown,
                          updateBothTablesVisible: (upTrainData != null &&
                              upTrainData!['coaches'] != null &&
                              downTrainData != null &&
                              downTrainData!['coaches'] != null &&
                              hasAccess),
                          onPendingDeletionsChanged: (deletions) =>
                              handlePendingDeletionsChanged(deletions, false),
                          status: downTrainData!['status']
                        ),

                      if (downTrainData != null &&
                          downTrainData!['coaches'] != null)
                        JobChartStatusWidget(trainNumber:  downTrainData!['train_no'].toString(),originDate:  downTrainData!['date'], statusFor:  "ca", defaultStatus: downTrainData!['status'],submitJobChartStatus:submitJobChartStatus),

                      if (hasAccess && !(upTrainData != null &&
                          upTrainData!['coaches'] != null &&
                          downTrainData != null &&
                          downTrainData!['coaches'] != null &&
                          hasAccess))
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: ElevatedButton(
                            onPressed: () {
                              if (downTrainData != null) {
                                addTrainsCoachWise(
                                  _transformData(selectedUsers2, downTrainData!, cashCarry2),
                                  selectedEhk2.asMap().map((_, ehk) => MapEntry(ehk, List<String>.from(downTrainData!['coaches']))),
                                  downTrainData!['train_no'].toString(),
                                  downTrainData!['date']
                                );
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              foregroundColor: Colors.black87,
                              backgroundColor: Colors.white,
                              side: const BorderSide(color: Colors.black87, width: 0.5),
                            ),
                            child: const Text(
                              'Update',
                              style: TextStyle(fontSize: 16.0),
                            ),
                          ),
                        ),

                      if (upTrainData != null &&
                          upTrainData!['coaches'] != null &&
                          downTrainData != null &&
                          downTrainData!['coaches'] != null &&
                          hasAccess)
                        Center(
                          child: Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: ElevatedButton(
                              onPressed: () async {
                                try {
                                  await submitTwoForms();
                                } catch (e) {
                                  print("Error submitting forms: $e");
                                } finally {}
                              },
                              style: ElevatedButton.styleFrom(
                                foregroundColor: Colors.black,
                                backgroundColor: Colors.white,
                                side: BorderSide(color: Colors.black87, width: 0.5),
                              ),
                              child: const Text(
                                'Update Both Trains',
                                style: TextStyle(
                                  fontSize: 16.0,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      if (showLoader)
        Positioned.fill(
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            color: Colors.black.withOpacity(0.5),
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 24),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children:  [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 16),
                    Text(
                      loaderText,
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],)
    );
  }

  // Map<String, double> _extractAmountData(Map<String, dynamic> trainData) {
  //       final amountData = <String, double>{};

  //       // Add EHK users amount
  //       trainData['ehk_dict'].entries.forEach((entry) {
  //         if (entry.value['origin_date'] == trainData['date']) {
  //           amountData[entry.key] = entry.value['cash_carry'] ?? 0.0;
  //         }
  //       });

  //       // Add CA users amount
  //       trainData['coaches'].forEach((coach) {
  //         if (trainData['coach_wise_dict'].containsKey(coach)) {
  //           trainData['coach_wise_dict'][coach]!.forEach((user) {
  //             print('-----------------------');
  //             print(user);
  //             if (user['origin_date'] == trainData['date']) {
  //               amountData[user['user']] = user['cash_carry'] ?? 0.0;
  //             }
  //           });
  //         }
  //       });

  //       return amountData;
  //     }


  Map<String, dynamic> _transformData(
    Map<String, List<String>> data, 
    Map<String, dynamic> trainData, 
    Map<String, String> amountData
  ) {
    final userToCoaches = <String, dynamic>{};

    data.forEach((coach, users) {
      for (var user in users) {
        if (!userToCoaches.containsKey(user)) {
          userToCoaches[user] = {
            "values": [coach],
            "cash_carry": amountData[user] ?? "0"
          };
        } else {
          // If user already exists, append the coach to their values array
          userToCoaches[user]["values"].add(coach);
        }
      }
    });
    
    return userToCoaches;
  }

  Map<String, List<String>> transformData(Map<String, List<String>> data) {
    final userToCoaches = <String, List<String>>{};
    data.forEach((coach, users) {
      for (var user in users) {
        if (!userToCoaches.containsKey(user)) {
          userToCoaches[user] = [];
        }
        userToCoaches[user]!.add(coach);
      }
    });
    return userToCoaches;
  }

   Future<void> submitTwoForms() async {
    setState(() {
      showLoader = true;
      loaderText = 'Update Job Chart...';
    });
    String combinedMessage = '';

    try {
      print("Selected Users: $selectedUsers1");
      for (var entry in upPendingDeletions.entries) {
        final keyParts = entry.key.split('-');
        final username = keyParts[0];
        final userType = keyParts[1];

        await removeTrainDetailsOriginDate(
          username,
          entry.value,
          upTrainData!['train_no'],
          upTrainData!['date'],
          userType,
        );
      }

      for (var entry in downPendingDeletions.entries) {
        final keyParts = entry.key.split('-');
        final username = keyParts[0];
        final userType = keyParts[1];

        await removeTrainDetailsOriginDate(
          username,
          entry.value,
          downTrainData!['train_no'].toString(),
          downTrainData!['date'],
          userType,
        );
      }

      setState(() {
        upPendingDeletions = {};
        downPendingDeletions = {};
      });

      Map<String, dynamic>? responseData;

      if ((upTrainData != null && upTrainData!['coaches'] != null) &&
          (downTrainData != null && downTrainData!['coaches'] != null)) {
        final upAmountData =
            (upTableKey.currentState as AssignEhkCaTableState).amountInHand;
        final data1 = _transformData(selectedUsers1, upTrainData!, cashCarry1);
        final Map<String, List<String>> dataEhk1 = {};

        for (final ehk in selectedEhk1) {
          dataEhk1[ehk] = List<String>.from(upTrainData!['coaches']);
        }

        final downAmountData =
            (downTableKey.currentState as AssignEhkCaTableState).amountInHand;
        downAmountData.forEach((key, value) {
          // if(value.toString() == "0"){
          //   downAmountData[key]=upAmountData[key] ?? "0";
          // }
          downAmountData[key] = upAmountData[key] ?? "0";
        });
        final data2 =
            _transformData(selectedUsers2, downTrainData!, cashCarry2);
        final Map<String, List<String>> dataEhk2 = {};

        for (final ehk in selectedEhk2) {
          dataEhk2[ehk] = List<String>.from(downTrainData!['coaches']);
        }
        responseData = await AdminAssignService.addBothTrainsCoachWise(
          data1,
          dataEhk1,
          upTrainData!['train_no'].toString(),
          upTrainData!['date'],
          data2,
          dataEhk2,
          downTrainData!['train_no'].toString(),
          downTrainData!['date'],
          "coach attendent",
          Provider.of<UserModel>(context, listen: false).token,
        );

        combinedMessage += "${responseData!["message"]}";
      } else {
        if (upTrainData != null && upTrainData!['coaches'] != null) {
          final upAmountData =
              (upTableKey.currentState as AssignEhkCaTableState).amountInHand;
          final data1 =
              _transformData(selectedUsers1, upTrainData!, cashCarry1);
          final Map<String, List<String>> dataEhk1 = {};

          for (final ehk in selectedEhk1) {
            dataEhk1[ehk] = List<String>.from(upTrainData!['coaches']);
          }

          responseData = await AdminAssignService.addTrainsCoachWise(
            data1,
            dataEhk1,
            upTrainData!['train_no'],
            upTrainData!['date'],
            "coach attendent",
            Provider.of<UserModel>(context, listen: false).token,
          );

          combinedMessage += "${responseData!["message"]}";
        }

        if (downTrainData != null && downTrainData!['coaches'] != null) {
          final downAmountData =
              (downTableKey.currentState as AssignEhkCaTableState).amountInHand;
          final data2 =
              _transformData(selectedUsers2, downTrainData!, cashCarry2);
          final Map<String, List<String>> dataEhk2 = {};

          for (final ehk in selectedEhk2) {
            dataEhk2[ehk] = List<String>.from(downTrainData!['coaches']);
          }

          final response2 = await AdminAssignService.addTrainsCoachWise(
            data2,
            dataEhk2,
            downTrainData!['train_no'].toString(),
            downTrainData!['date'],
            "coach attendent",
            Provider.of<UserModel>(context, listen: false).token,
          );

          if (combinedMessage.isNotEmpty) {
            combinedMessage += '\n';
          }
          combinedMessage += "${response2["message"]}";

          // Use the last response data for skipped users if available
          if (responseData == null) {
            responseData = response2;
          }
        }
      }

      // Show modal with enhanced functionality
      setState(() {
        message = combinedMessage;
        msgModalFlag = true;
      });

      showModalWithSkippedUsers(combinedMessage, responseData);
      clearSeletections();
      if (train != null && date != null) {
        onSubmit(train!, date!);
      }
    } catch (e) {
      setState(() {
        message = 'An error occurred while updating the trains: $e';
        msgModalFlag = true;
      });
      showModal(message);
    } finally {
      setState(() {
        showLoader = false;
        loaderText = '';
      });
    }
  }

  void showModal(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Message'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  void showModalWithSkippedUsers(
      String message, Map<String, dynamic>? responseData) {
    // Check if there are skipped users
    List<dynamic>? skippedUsers = responseData?['skipped_users']?['users'];

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          insetPadding: const EdgeInsets.all(16.0), // Add padding around dialog
          child: Container(
            width: double.maxFinite,
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
              maxWidth: MediaQuery.of(context).size.width * 0.9,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header
                Container(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      const Expanded(
                        child: Text(
                          'Message',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        icon: const Icon(Icons.close),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                ),
                // Content
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          message,
                          style: const TextStyle(fontSize: 16),
                        ),
                        if (skippedUsers != null &&
                            skippedUsers.isNotEmpty) ...[
                          const SizedBox(height: 16),
                          const Text(
                            'Skipped Users:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          const SizedBox(height: 8),
                          // Use a scrollable container for the table
                          Container(
                            width: double.maxFinite,
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: ConstrainedBox(
                                constraints: BoxConstraints(
                                  minWidth:
                                      MediaQuery.of(context).size.width * 0.7,
                                ),
                                child: DataTable(
                                  columnSpacing: 6,
                                  horizontalMargin: 6,
                                  dataRowHeight: 56,
                                  headingRowHeight: 40,
                                  columns: const [
                                    DataColumn(
                                      label: Expanded(
                                        child: Text(
                                          'Username',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Expanded(
                                        child: Text(
                                          'Train No',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Expanded(
                                        child: Text(
                                          'Date',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                                    ),
                                    DataColumn(
                                      label: Expanded(
                                        child: Text(
                                          'Coaches',
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                  rows: skippedUsers.map<DataRow>((user) {
                                    return DataRow(
                                      cells: [
                                        DataCell(
                                          Container(
                                            width: 80,
                                            child: Text(
                                              user['username'] ?? '',
                                              style:
                                                  const TextStyle(fontSize: 12),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                        DataCell(
                                          Container(
                                            width: 50,
                                            child: Text(
                                              user['train_no']?.toString() ??
                                                  '',
                                              style:
                                                  const TextStyle(fontSize: 12),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                        DataCell(
                                          Container(
                                            width: 70,
                                            child: Text(
                                              user['date'] ?? '',
                                              style:
                                                  const TextStyle(fontSize: 12),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                        DataCell(
                                          Container(
                                            width: 100,
                                            child: Text(
                                              (user['coaches']
                                                          as List<dynamic>?)
                                                      ?.join(', ') ??
                                                  '',
                                              style:
                                                  const TextStyle(fontSize: 12),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                      ],
                                    );
                                  }).toList(),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                // Footer
                Container(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: const Text('OK'),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  void _ImageUpload(BuildContext context, Widget page) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => page),
    );

    if (train != null && date != null) {
      onSubmit(train!, date!);
    }
  }
}