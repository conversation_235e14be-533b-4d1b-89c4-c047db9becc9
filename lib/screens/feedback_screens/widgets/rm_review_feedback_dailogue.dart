import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/routes.dart';
import 'package:railops/services/feedback_services/confirm_email.dart';
import 'package:railops/services/feedback_services/feedback_submission.dart';
import 'package:railops/services/feedback_services/update_feedback.dart';
import 'package:railops/services/feedback_services/verify_email.dart';
import 'package:railops/widgets/loader.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';

class RMReviewFeedbackDialog extends StatefulWidget {
  final Map<String, dynamic> feedback;
  final Function onFeedbackUpdated;

  const RMReviewFeedbackDialog({
    super.key,
    required this.feedback,
    required this.onFeedbackUpdated,
  });

  @override
  State<RMReviewFeedbackDialog> createState() => RMReviewFeedbackDialogState();
}

class RMReviewFeedbackDialogState extends State<RMReviewFeedbackDialog> {
  late TextEditingController trainNumberController;
  late TextEditingController trainNameController;
  late TextEditingController passengerNameController;
  late TextEditingController pnrNoController;
  late TextEditingController crnNoController;
  late TextEditingController mobileNoController;
  late TextEditingController coachNoController;
  late TextEditingController berthNoController;
  late TextEditingController emailIdController;
  late TextEditingController otpController;
  late TextEditingController remarksController;
  late TextEditingController issueController;
  late TextEditingController subIssueController;

  String? selectedCategory;
  String? selectedStatus;
  late bool _isPNRVerified;
  bool isEmailVerified = false;
  bool showOtpInput = false;
  bool isEditable = false;
  String feedbackMessage = '';
  String formattedDate = '';
  VideoPlayerController? videoPlayerController;
  ChewieController? chewieController;
  bool isVideoPlaying = false;
  File? _pnrImage;
  final ImagePicker _picker = ImagePicker();
  String? originalStatus;
  String? _resolvedStatus;
  int marksGiven = 0;
  String? selectedIssue;
  String? selectedSubIssue;
  bool resolved = false;
  Map<String, dynamic> issuesData = {
    'ISSUE_TYPES': <String>[],
    'SUB_ISSUE_TYPES': <List<dynamic>>[],
  };

  @override
  void initState() {
    super.initState();
    _isWidgetActive = true;

    trainNumberController = TextEditingController(
        text: widget.feedback['train_number']?.toString() ?? '');
    trainNameController = TextEditingController(
        text: widget.feedback['train_name']?.toString() ?? '');
    passengerNameController = TextEditingController(
        text: widget.feedback['passenger_name']?.toString() ?? '');
    pnrNoController = TextEditingController(
        text: widget.feedback['pnr_no']?.toString() ?? '');
    crnNoController = TextEditingController(
        text: widget.feedback['crn_no']?.toString() ?? '');
    mobileNoController = TextEditingController(
        text: widget.feedback['mobile_no']?.toString() ?? '');
    coachNoController =
        TextEditingController(text: widget.feedback['coach']?.toString() ?? '');

    var berthNoValue = widget.feedback['berth_no'];
    berthNoController = TextEditingController(
        text: berthNoValue is int
            ? berthNoValue.toString()
            : (berthNoValue?.toString() ?? ''));

    emailIdController =
        TextEditingController(text: widget.feedback['email']?.toString() ?? '');
    otpController = TextEditingController();
    remarksController = TextEditingController(
        text: widget.feedback['comment']?.toString() ?? '');

    selectedStatus = widget.feedback['status']?.toString();
    originalStatus = widget.feedback['status']?.toString();
    resolved = widget.feedback['resolved'] == true;
    formattedDate = widget.feedback['date']?.toString() ?? '';

    isEmailVerified = widget.feedback['verified'] == true;

    isEditable = selectedStatus == 'P';

    selectedCategory = widget.feedback['category']?.toString() ?? '';

    // Initialize issue types data from feedback
    if (widget.feedback['issue_type'] != null &&
        widget.feedback['issue_type'] is List) {
      List<dynamic> issueTypeList = widget.feedback['issue_type'];
      if (issueTypeList.isNotEmpty) {
        selectedIssue = issueTypeList.first.toString();
      }
    }

    // Initialize sub-issue types data from feedback
    if (widget.feedback['sub_issue_type'] != null &&
        widget.feedback['sub_issue_type'] is List) {
      List<dynamic> subIssueTypeList = widget.feedback['sub_issue_type'];
      if (subIssueTypeList.isNotEmpty) {
        selectedSubIssue = subIssueTypeList.first.toString();
      }
    }

    // Initialize the issuesData as empty, will be populated by _fetchIssues()
    issuesData = {
      'ISSUE_TYPES': <String>[],
      'SUB_ISSUE_TYPES': <List<dynamic>>[],
    };

    marksGiven = widget.feedback['rating_out_of_10'] is int
        ? widget.feedback['rating_out_of_10']
        : 0;

    _resolvedStatus = resolved ? 'Yes' : 'No';

    issueController = TextEditingController(text: selectedIssue);
    subIssueController = TextEditingController(text: selectedSubIssue);

    // Fetch the issue types data
    _fetchIssues();
  }

  @override
  void dispose() {
    _isWidgetActive = false;
    trainNumberController.dispose();
    trainNameController.dispose();
    passengerNameController.dispose();
    pnrNoController.dispose();
    crnNoController.dispose();
    mobileNoController.dispose();
    coachNoController.dispose();
    berthNoController.dispose();
    emailIdController.dispose();
    otpController.dispose();
    remarksController.dispose();
    issueController.dispose();
    subIssueController.dispose();
    videoPlayerController?.dispose();
    chewieController?.dispose();
    super.dispose();
  }

// Add this at class level if not already present
  bool _isWidgetActive = true;

  Future<void> _fetchIssues() async {
    try {
      final data = await SubmitPassengerFeedback.fetchIssues();
      if (!_isWidgetActive || !mounted) return;
      setState(() {
        if (data.containsKey('ISSUE_TYPES')) {
          if (data['ISSUE_TYPES'] is List) {
            issuesData['ISSUE_TYPES'] = data['ISSUE_TYPES'];
          } else {
            issuesData['ISSUE_TYPES'] = <String>[];
          }
        }
        issuesData['SUB_ISSUE_TYPES'] = _buildSubIssueList(data);
      });
    } catch (e) {
      if (!_isWidgetActive || !mounted) return;
      setState(() {
        issuesData = {
          'ISSUE_TYPES': <String>[],
          'SUB_ISSUE_TYPES': <List<dynamic>>[],
        };
      });
    }
  }

  List<List<dynamic>> _buildSubIssueList(Map<String, dynamic> data) {
    List<List<dynamic>> result = [];
    if (data.containsKey('SUB_ISSUE_TYPES')) {
      final subIssueTypes = data['SUB_ISSUE_TYPES'];
      if (subIssueTypes is Map<String, dynamic>) {
        subIssueTypes.forEach((issueType, subIssues) {
          if (subIssues is List) {
            for (var subIssue in subIssues) {
              result.add([issueType, subIssue]);
            }
          }
        });
      } else if (subIssueTypes is List) {
        for (var item in subIssueTypes) {
          if (item is List && item.length >= 2) {
            result.add([item[0], item[1]]);
          }
        }
      }
    }
    return result;
  }

  void showWarning(BuildContext context, String message, Color color) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.0),
          ),
          backgroundColor: color,
          content: Text(
            message,
            style: const TextStyle(color: Colors.white),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                AppLocalizations.of(context).btn_ok,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        );
      },
    );
  }

  void _initializeVideo(String url) async {
    setState(() {
      isVideoPlaying = false;
    });
    videoPlayerController = VideoPlayerController.network(url);
    try {
      await videoPlayerController!.initialize();
      if (mounted) {
        chewieController = ChewieController(
          videoPlayerController: videoPlayerController!,
          autoPlay: true,
          looping: false,
          aspectRatio: videoPlayerController!.value.aspectRatio,
          errorBuilder: (context, errorMessage) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, color: Colors.red),
                  const SizedBox(height: 8),
                  Text(
                    AppLocalizations.of(context)
                        .msg_error_with_details(errorMessage),
                    style: const TextStyle(color: Colors.white),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          },
        );
        setState(() {
          isVideoPlaying = true;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          isVideoPlaying = false;
        });
        showWarning(
            context,
            AppLocalizations.of(context).msg_failed_to_load_video(e.toString()),
            Colors.red);
      }
    }
  }

  Widget _buildMediaPreview(dynamic media) {
    String? mediaUrl;
    if (media is String) {
      mediaUrl = media;
    } else if (media is Map<String, dynamic> && media.containsKey('file_url')) {
      var url = media['file_url'];
      if (url is String) {
        mediaUrl = url;
      } else if (url is List && url.isNotEmpty && url.first is String) {
        mediaUrl = url.first;
      }
    }
    if (mediaUrl == null || mediaUrl.isEmpty) {
      return const SizedBox.shrink();
    }
    final bool isImage = ['jpg', 'jpeg', 'png', 'gif', 'webp']
        .any((ext) => mediaUrl!.toLowerCase().endsWith(ext));
    final bool isVideo = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv']
        .any((ext) => mediaUrl!.toLowerCase().endsWith(ext));
    if (isImage) {
      return Container(
        width: double.infinity,
        constraints: const BoxConstraints(maxHeight: 300),
        margin: const EdgeInsets.symmetric(vertical: 15),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            mediaUrl,
            fit: BoxFit.contain,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return Container(
                height: 200,
                color: Colors.grey[300],
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.broken_image,
                          size: 48, color: Colors.grey),
                      const SizedBox(height: 8),
                      Text(AppLocalizations.of(context)
                          .text_failed_to_load_image),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      );
    } else if (isVideo) {
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 15),
        child: isVideoPlaying && mediaUrl == videoPlayerController?.dataSource
            ? AspectRatio(
                aspectRatio: 16 / 9,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Chewie(controller: chewieController!),
                ),
              )
            : GestureDetector(
                onTap: () {
                  _initializeVideo(mediaUrl!);
                },
                child: Container(
                  height: 200,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      const Icon(
                        Icons.play_circle_fill,
                        color: Colors.white,
                        size: 64,
                      ),
                      Positioned(
                        bottom: 10,
                        child: Text(
                          AppLocalizations.of(context).text_tap_to_play_video,
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildMediaSection(Map<String, dynamic> feedback) {
    List<dynamic> pnrImages = [];
    List<dynamic> feedbackMedia = [];
    bool modernMediaExists = false;
    if (feedback['media_files'] != null && feedback['media_files'] is List) {
      modernMediaExists = true;
      for (var media in feedback['media_files']) {
        if (media is Map<String, dynamic> && media['is_pnr_image'] == true) {
          pnrImages.add(media);
        } else if (media is Map<String, dynamic>) {
          feedbackMedia.add(media);
        }
      }
    }
    if (!modernMediaExists || pnrImages.isEmpty) {
      if (feedback['pnr_image'] != null &&
          feedback['pnr_image'] is String &&
          feedback['pnr_image'].isNotEmpty) {
        pnrImages.add({'file_url': feedback['pnr_image']});
      }
      if (feedback['pnr_images'] != null && feedback['pnr_images'] is List) {
        for (var url in feedback['pnr_images']) {
          if (url is String && url.isNotEmpty) {
            pnrImages.add({'file_url': url});
          }
        }
      }
    }
    if (feedbackMedia.isEmpty) {
      if (feedback['media_url'] != null &&
          feedback['media_url'] is String &&
          feedback['media_url'].isNotEmpty) {
        feedbackMedia.add({'file_url': feedback['media_url']});
      } else if (feedback['media_url'] != null &&
          feedback['media_url'] is List) {
        for (var url in feedback['media_url']) {
          if (url is String &&
              url.isNotEmpty &&
              !pnrImages.any((item) => item['file_url'] == url)) {
            feedbackMedia.add({'file_url': url});
          }
        }
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        if (pnrImages.isNotEmpty)
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                AppLocalizations.of(context).text_pnr_documents,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...pnrImages
                  .map((media) => _buildMediaPreview(media['file_url'])),
              const Divider(thickness: 1),
            ],
          ),
        if (feedbackMedia.isNotEmpty)
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                AppLocalizations.of(context).text_feedback_media,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...feedbackMedia
                  .map((media) => _buildMediaPreview(media['file_url'])),
            ],
          ),
      ],
    );
  }

  void _showSpamInfoDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.warning_amber_rounded, color: Colors.amber),
              const SizedBox(width: 5),
              Text(
                AppLocalizations.of(context)
                    .dialog_email_verification_info_title,
                style: const TextStyle(fontSize: 18, color: Colors.red),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                AppLocalizations.of(context)
                    .dialog_email_verification_spam_notice,
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 12),
              Text(
                AppLocalizations.of(context).dialog_after_requesting_otp,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(AppLocalizations.of(context).dialog_check_inbox_first),
              Text(AppLocalizations.of(context).dialog_check_spam_folder),
              Text(AppLocalizations.of(context).dialog_add_safe_sender),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(AppLocalizations.of(context).btn_i_understand),
            ),
          ],
        );
      },
    );
  }

  void handleVerifyEmail() async {
    if (!isEditable) {
      showWarning(
          context,
          AppLocalizations.of(context).msg_cannot_verify_email_completed,
          Colors.red);
      return;
    }
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;
    if (emailIdController.text.isEmpty) {
      showWarning(
          context,
          AppLocalizations.of(context).validation_enter_valid_email,
          Colors.red);
      return;
    }
    loader(context, AppLocalizations.of(context).loading_sending_otp);
    try {
      await VerifyPassengerEmailService.verifyEmail(
        emailIdController.text,
        userToken,
      );
      if (mounted) {
        setState(() {
          showOtpInput = true;
        });
      }
      Navigator.of(context, rootNavigator: true).pop();
      showWarning(
          context,
          AppLocalizations.of(context).msg_email_verification_initiated,
          Colors.blue);
    } catch (e) {
      Navigator.of(context, rootNavigator: true).pop();
      showWarning(
          context,
          AppLocalizations.of(context).msg_error_with_details(e.toString()),
          Colors.red);
    }
  }

  void handleVerifyOtp(String formattedDate) async {
    if (!isEditable) {
      showWarning(
          context,
          AppLocalizations.of(context).msg_cannot_verify_otp_completed,
          Colors.red);
      return;
    }
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;
    if (otpController.text.isEmpty) {
      showWarning(context, AppLocalizations.of(context).validation_enter_otp,
          Colors.red);
      return;
    }
    loader(context, AppLocalizations.of(context).loading_verifying_otp);
    try {
      await ConfirmPassengerEmailService.confirmEmail(
        token: userToken,
        date: formattedDate,
        email: emailIdController.text,
        otp: otpController.text,
      );
      if (mounted) {
        setState(() {
          showOtpInput = false;
          isEmailVerified = true;
          widget.feedback['verified'] = true;
        });
        Navigator.of(context, rootNavigator: true).pop();
        showWarning(
            context,
            AppLocalizations.of(context).msg_otp_verified_successfully,
            Colors.blue);
      }
    } catch (e) {
      Navigator.of(context, rootNavigator: true).pop();
      if (mounted) {
        String errorMessage = e.toString();
        showWarning(
          context,
          AppLocalizations.of(context).msg_error_with_details(
              errorMessage.isEmpty
                  ? AppLocalizations.of(context).msg_something_went_wrong
                  : errorMessage),
          Colors.red,
        );
      }
    }
  }

  Widget _buildEmailVerificationSection() {
    bool shouldShowVerificationControls = widget.feedback['verified'] == false;
    bool shouldShowEmailControls = widget.feedback['verified'] == false;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            if (!shouldShowEmailControls)
              Expanded(
                child: TextFormField(
                  controller: emailIdController,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context).form_email_id,
                    border: const OutlineInputBorder(),
                  ),
                  readOnly: !isEditable || widget.feedback['verified'] == true,
                  enabled: isEditable && widget.feedback['verified'] != true,
                ),
              ),
            if (!shouldShowVerificationControls &&
                isEmailVerified &&
                isEditable &&
                !showOtpInput)
              IconButton(
                icon: const Icon(
                  Icons.info_outline,
                  color: Colors.blue,
                ),
                onPressed: () {
                  _showSpamInfoDialog();
                },
              ),
          ],
        ),
        if (!shouldShowVerificationControls &&
            isEmailVerified &&
            isEditable) ...[
          const SizedBox(height: 10),
          ElevatedButton(
            onPressed: handleVerifyEmail,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: Text(AppLocalizations.of(context).btn_verify_email),
          ),
        ],
        if (showOtpInput && shouldShowVerificationControls) ...[
          const SizedBox(height: 10),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.mark_email_unread, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                Flexible(
                  child: Text(
                    AppLocalizations.of(context).text_otp_sent_check_folders,
                    style: TextStyle(color: Colors.blue.shade700),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),
          TextFormField(
            controller: otpController,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context).form_enter_otp,
              border: const OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            enabled: isEditable,
          ),
          const SizedBox(height: 10),
          ElevatedButton(
            onPressed: () => handleVerifyOtp(formattedDate),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: Text(AppLocalizations.of(context).btn_verify_otp),
          ),
        ],
        if (isEmailVerified)
          Row(
            children: [
              const Icon(Icons.verified, color: Colors.green),
              const SizedBox(width: 5.0),
              Text(
                AppLocalizations.of(context).status_verified,
                style: const TextStyle(color: Colors.green),
              ),
            ],
          ),
      ],
    );
  }

  int _countCharacters(String text) {
    return text.length;
  }

  void updateFeedbackStatus() async {
    if (!isEditable && selectedStatus == widget.feedback['status']) {
      Navigator.of(context).pop();
      return;
    }
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;
    final feedbackId = widget.feedback['id'];
    final data = widget.feedback;
    widget.feedback['comment'] = remarksController.text;
    widget.feedback['rating_out_of_10'] = marksGiven;
    widget.feedback['status'] = selectedStatus;
    widget.feedback['issue_type'] = selectedIssue;
    widget.feedback['sub_issue_type'] = selectedSubIssue;
    widget.feedback['resolved'] = resolved;
    loader(context, AppLocalizations.of(context).loading_updating_feedback);
    final response = await UpdateFeedback.updateFeedback(
      feedbackId: feedbackId,
      data: data,
      token: token,
    );
    if (response['success']) {
      if (selectedStatus == 'C' && widget.feedback['status'] == 'P') {
        if (mounted) {
          setState(() {
            isEditable = false;
          });
        }
      }
      Navigator.pushReplacementNamed(context, Routes.passengerFeedbackScreen);
      showWarning(
          context,
          AppLocalizations.of(context).msg_feedback_updated_successfully,
          Colors.blue);
    } else {
      if (mounted) {
        setState(() {
          selectedStatus = widget.feedback['status'];
        });
      }
      Navigator.of(context, rootNavigator: true).pop();
      showWarning(
          context,
          AppLocalizations.of(context).msg_failed_to_update_feedback,
          Colors.red);
    }
  }

  Widget _buildIssueDropdowns() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Issue Type Dropdown - Conditional Editing
        isEditable
            ? DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context).form_issue_type_label,
                  border: const OutlineInputBorder(),
                ),
                value: selectedIssue,
                items: issuesData.containsKey('ISSUE_TYPES') &&
                        issuesData['ISSUE_TYPES'] is List
                    ? (issuesData['ISSUE_TYPES'] as List)
                        .map<DropdownMenuItem<String>>((dynamic issue) {
                        return DropdownMenuItem<String>(
                          value: issue.toString(),
                          child: Text(issue.toString()),
                        );
                      }).toList()
                    : <DropdownMenuItem<String>>[],
                onChanged: (value) {
                  setState(() {
                    selectedIssue = value;
                    selectedSubIssue = null;
                    issueController.text = value ?? '';
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppLocalizations.of(context)
                        .validation_select_issue_type;
                  }
                  return null;
                },
              )
            : InputDecorator(
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context).form_issue_type_label,
                  border: const OutlineInputBorder(),
                ),
                child: Text(
                  selectedIssue ?? 'None selected',
                  style: TextStyle(
                      color: selectedIssue == null ? Colors.grey : null),
                ),
              ),
        const SizedBox(height: 20),
        if (selectedIssue != null)
          isEditable
              ? DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText:
                        AppLocalizations.of(context).form_sub_issue_type_label,
                    border: const OutlineInputBorder(),
                  ),
                  value: selectedSubIssue,
                  items: issuesData.containsKey('SUB_ISSUE_TYPES') &&
                          issuesData['SUB_ISSUE_TYPES'] is List
                      ? (issuesData['SUB_ISSUE_TYPES'] as List)
                          .where((dynamic issue) =>
                              issue is List &&
                              issue.isNotEmpty &&
                              issue[0] == selectedIssue)
                          .map<DropdownMenuItem<String>>((dynamic issue) {
                          if (issue is List && issue.length > 1) {
                            return DropdownMenuItem<String>(
                              value: issue[1].toString(),
                              child: Text(issue[1].toString()),
                            );
                          } else {
                            return const DropdownMenuItem<String>(
                              value: '',
                              child: Text(''),
                            );
                          }
                        }).toList()
                      : <DropdownMenuItem<String>>[],
                  onChanged: (value) {
                    setState(() {
                      selectedSubIssue = value;
                      subIssueController.text = value ?? '';
                    });
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return AppLocalizations.of(context)
                          .validation_select_sub_issue_type;
                    }
                    return null;
                  },
                )
              : InputDecorator(
                  decoration: InputDecoration(
                    labelText:
                        AppLocalizations.of(context).form_sub_issue_type_label,
                    border: const OutlineInputBorder(),
                  ),
                  child: Text(
                    selectedSubIssue ?? 'None selected',
                    style: TextStyle(
                        color: selectedSubIssue == null ? Colors.grey : null),
                  ),
                ),
      ],
    );
  }

  Widget _buildResolvedStatusDropdown() {
    return isEditable
        ? DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: 'Resolved (Yes/No) *',
              border: OutlineInputBorder(),
            ),
            value: _resolvedStatus!.isEmpty ? null : _resolvedStatus,
            hint: const Text('Select'),
            items: ['Yes', 'No']
                .map((status) => DropdownMenuItem(
                      value: status,
                      child: Text(status),
                    ))
                .toList(),
            onChanged: (value) {
              setState(() {
                _resolvedStatus = value ?? '';
                resolved = value == 'Yes';
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please select resolved status';
              }
              return null;
            },
          )
        : InputDecorator(
            decoration: const InputDecoration(
              labelText: 'Resolved (Yes/No) *',
              border: OutlineInputBorder(),
            ),
            child: Text(
              _resolvedStatus?.isEmpty == true
                  ? 'None selected'
                  : (_resolvedStatus ?? 'None selected'),
              style: TextStyle(
                  color: _resolvedStatus?.isEmpty == true ? Colors.grey : null),
            ),
          );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(AppLocalizations.of(context).dialog_title_review_feedback),
      insetPadding: const EdgeInsets.all(10),
      contentPadding: const EdgeInsets.all(10),
      titlePadding: const EdgeInsets.all(10.0),
      content: SizedBox(
        width: MediaQuery.of(context).size.width,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                margin: const EdgeInsets.only(bottom: 15),
                decoration: BoxDecoration(
                  color: isEditable
                      ? Colors.blue.withOpacity(0.1)
                      : Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isEditable ? Colors.blue : Colors.red,
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      isEditable ? Icons.edit : Icons.lock,
                      color: isEditable ? Colors.blue : Colors.red,
                      size: 18,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      isEditable
                          ? AppLocalizations.of(context)
                              .text_status_pending_editable
                          : AppLocalizations.of(context)
                              .text_status_completed_not_editable,
                      style: TextStyle(
                        color: isEditable ? Colors.blue : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              if (feedbackMessage.isNotEmpty)
                Container(
                  padding: const EdgeInsets.all(10),
                  color: Colors.blue[100],
                  child: Text(
                    feedbackMessage,
                    style: const TextStyle(color: Colors.blue),
                  ),
                ),
              const SizedBox(height: 10),
              _buildMediaSection(widget.feedback),
              const SizedBox(height: 10),
              TextFormField(
                controller: passengerNameController,
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context).form_passenger_name,
                  border: const OutlineInputBorder(),
                ),
                onChanged: (value) {
                  if (isEditable) {
                    setState(() {
                      widget.feedback['passenger_name'] = value;
                    });
                  }
                },
                readOnly: !isEditable,
                enabled: isEditable,
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: pnrNoController,
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context).form_pnr_number,
                  border: const OutlineInputBorder(),
                ),
                onChanged: (value) {
                  if (isEditable) {
                    setState(() {
                      widget.feedback['pnr_no'] = value;
                    });
                  }
                },
                readOnly: !isEditable,
                enabled: isEditable,
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: crnNoController,
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context).form_crn_number,
                  border: const OutlineInputBorder(),
                ),
                onChanged: (value) {
                  if (isEditable) {
                    setState(() {
                      widget.feedback['crn_no'] = value;
                    });
                  }
                },
                readOnly: !isEditable,
                enabled: isEditable,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: coachNoController,
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context).form_coach_no,
                        border: const OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        if (isEditable) {
                          setState(() {
                            widget.feedback['coach'] = value;
                          });
                        }
                      },
                      readOnly: !isEditable,
                      enabled: isEditable,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: TextFormField(
                      controller: berthNoController,
                      decoration: InputDecoration(
                        labelText: AppLocalizations.of(context).form_berth_no,
                        border: const OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        if (isEditable) {
                          setState(() {
                            int? parsedValue = int.tryParse(value);
                            widget.feedback['berth_no'] = parsedValue ?? 0;
                          });
                        }
                      },
                      keyboardType: TextInputType.number,
                      readOnly: !isEditable,
                      enabled: isEditable,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              TextFormField(
                controller: mobileNoController,
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context).form_mobile_number,
                  border: const OutlineInputBorder(),
                ),
                onChanged: (value) {
                  if (isEditable) {
                    setState(() {
                      widget.feedback['mobile_no'] = value;
                    });
                  }
                },
                keyboardType: TextInputType.number,
                readOnly: !isEditable,
                enabled: isEditable,
              ),
              const SizedBox(height: 15),
              _buildResolvedStatusDropdown(),
              const SizedBox(height: 20),
              DropdownButtonFormField<int>(
                decoration: const InputDecoration(
                  labelText: 'Marks (1 to 10) *',
                  border: OutlineInputBorder(),
                ),
                value: marksGiven == 0 ? null : marksGiven,
                hint: const Text('Select'),
                items: List.generate(10, (index) => index + 1)
                    .map((mark) => DropdownMenuItem(
                          value: mark,
                          child: Text(mark.toString()),
                        ))
                    .toList(),
                onChanged: isEditable
                    ? (value) {
                        if (value == null) return;
                        setState(() {
                          marksGiven = value;
                        });
                      }
                    : null,
                validator: (value) {
                  if (value == null || value == 0) {
                    return 'Please select marks';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),
              _buildIssueDropdowns(),
              const SizedBox(height: 10),
              Card(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 5.0, vertical: 5.0),
                  child: Stack(
                    children: [
                      TextField(
                        controller: remarksController,
                        maxLines: 2,
                        style: const TextStyle(fontSize: 14),
                        decoration: InputDecoration(
                          labelText: AppLocalizations.of(context).form_remarks,
                          hintText:
                              AppLocalizations.of(context).form_remarks_hint,
                          border: const OutlineInputBorder(),
                        ),
                        inputFormatters: [
                          FilteringTextInputFormatter.deny(RegExp(r'[0-9]')),
                          LengthLimitingTextInputFormatter(100),
                        ],
                        onChanged: (value) {
                          if (_countCharacters(remarksController.text) > 100) {
                            showWarning(
                              context,
                              AppLocalizations.of(context)
                                  .validation_remarks_max_100,
                              Colors.red,
                            );
                          }
                          setState(() {});
                        },
                        readOnly: !isEditable,
                        enabled: isEditable,
                      ),
                      Positioned(
                        right: 8,
                        bottom: 4,
                        child: Text(
                          '${_countCharacters(remarksController.text)}/100 characters',
                          style: TextStyle(
                            fontSize: 10,
                            color:
                                _countCharacters(remarksController.text) > 100
                                    ? Colors.red
                                    : Colors.grey[600],
                            fontWeight:
                                _countCharacters(remarksController.text) > 100
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 12),
              _buildEmailVerificationSection(),
              const SizedBox(height: 12),
              DropdownButtonFormField<String>(
                value: selectedStatus,
                decoration: InputDecoration(
                  labelText:
                      AppLocalizations.of(context).form_task_status_label,
                  border: const OutlineInputBorder(),
                ),
                items: [
                  DropdownMenuItem(
                    value: 'P',
                    child: Text(
                        AppLocalizations.of(context).status_pending_option),
                  ),
                  DropdownMenuItem(
                    value: 'C',
                    child: Text(
                        AppLocalizations.of(context).status_completed_option),
                  ),
                ],
                onChanged: isEditable
                    ? (value) {
                        if (value == null) return;
                        setState(() {
                          selectedStatus = value;
                        });
                      }
                    : null,
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(AppLocalizations.of(context).btn_cancel),
        ),
        ElevatedButton(
          onPressed: isEditable
              ? () {
                  updateFeedbackStatus();
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: isEditable ? Colors.blue : Colors.grey,
          ),
          child: Text(AppLocalizations.of(context).btn_update),
        ),
      ],
    );
  }
}
