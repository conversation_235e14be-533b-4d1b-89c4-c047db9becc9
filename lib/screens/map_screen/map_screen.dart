import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:latlong2/latlong.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/services/map_services/map_services.dart';
import 'package:railops/services/train_services/index.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:railops/types/train_types/train_details_response.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:railops/widgets/index.dart';
import 'package:geolocator/geolocator.dart';
import 'package:flutter_map_marker_popup/flutter_map_marker_popup.dart';

class MapScreen extends StatelessWidget {
  const MapScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      appBar: CustomAppBar(title: 'Map Screen'),
      drawer: CustomDrawer(),
      body: MapAndFormWidget(),
    );
  }
}

class MapAndFormWidget extends StatefulWidget {
  const MapAndFormWidget({super.key});

  @override
  _MapAndFormWidgetState createState() => _MapAndFormWidgetState();
}

class _MapAndFormWidgetState extends State<MapAndFormWidget> {
  final _formKey = GlobalKey<FormState>();

  String? _selectedTrainNumber;
  List<String> _trainNumbers = [];
  DateTime? _selectedDate = DateTime.now();
  String? _trainName;

  bool _showMap = false;
  LatLng? _currentLocation;
  List<Marker> _stationMarkers = [];

  final PopupController _popupLayerController = PopupController();
  MapController? _mapController;
  final TextEditingController _dateController = TextEditingController();


  @override
  void initState() {
    super.initState();
    _fetchTrainNumbers();
    _mapController = MapController();
    _setInitialTrainNoFromUserModel();
    _updateDateController();
  }

  void _setInitialTrainNoFromUserModel() {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final initialTrainNo = userModel.trainNo;
    final initialSelectedDate = userModel.selectedDate;
    if(initialSelectedDate.isNotEmpty){
      setState(() {
        _selectedDate = DateTime.parse(initialSelectedDate);
      });
      _updateDateController();
    }
    if (initialTrainNo.isNotEmpty) {
      _onTrainNumberChanged(initialTrainNo);
    }
  }

  // Custom date formatter function
  String _formatDateToCustomFormat(DateTime date) {
    final day = date.day.toString().padLeft(2, '0');
    final month = DateFormat('MMM').format(date); // Short month name (Jan, Feb, etc.)
    final year = date.year.toString();
    return '$day-$month-$year';
  }

  void _updateDateController() {
    if (_selectedDate != null) {
      _dateController.text = _formatDateToCustomFormat(_selectedDate!);
    }
  }

  Future<void> _selectDate() async {
    final DateTime today = DateTime.now();
    DateTime tempSelectedDate = _selectedDate ?? today;
    bool isEditingDate = false;
    TextEditingController _dateController = TextEditingController();

    DateTime? selectedDate = await showDialog<DateTime>(
      context: context,
      builder: (BuildContext context) {
        _dateController.text = DateFormat('MMM d').format(tempSelectedDate);
        return StatefulBuilder(
          builder: (context, setStateDialog) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: const ColorScheme.light(
                  primary: Colors.blue,
                  onPrimary: Colors.white,
                  onSurface: Colors.black,
                ),
              ),
              child: AlertDialog(
                contentPadding: EdgeInsets.zero,
                content: Container(
                  width: 320,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header with selected date
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.fromLTRB(24, 24, 24, 0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Select date',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            SizedBox(height: 8),
                            Row(
                            children: [
                              isEditingDate
                                  ? Expanded(
                                      child: TextField(
                                        controller: _dateController,
                                        autofocus: true,
                                        style: const TextStyle(fontSize: 28),
                                        decoration: const InputDecoration(
                                          hintText: 'e.g. Jun 27',
                                          border: InputBorder.none,
                                        ),
                                        onChanged: (value) {
                                          try {
                                            final input = value.trim().toLowerCase();
                                            final normalizedInput = input.split(' ').map((word) {
                                              if (word.isEmpty) return '';
                                              return word[0].toUpperCase() + word.substring(1);
                                            }).join(' ');

                                            final parsed = DateFormat('MMM d').parseStrict(normalizedInput);

                                            final updated = DateTime(
                                              tempSelectedDate.year, parsed.month, parsed.day);
                                            setStateDialog(() {
                                              tempSelectedDate = updated;
                                            });
                                          } catch (e) {
                                            // Invalid input; you can handle this if you want
                                          }
                                        },
                                        onSubmitted: (_) {
                                          setStateDialog(() {
                                            isEditingDate = false;
                                          });
                                        },
                                      ),
                                    )
                                  : Text(
                                      DateFormat('EEE, MMM d').format(tempSelectedDate),
                                      style: const TextStyle(
                                        color: Colors.black,
                                        fontSize: 32,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                              const Spacer(),
                              IconButton(
                                onPressed: () {
                                  setStateDialog(() {
                                    isEditingDate = !isEditingDate;
                                  });
                                },
                                icon: Icon(isEditingDate ? Icons.check : Icons.edit, size: 20),
                              ),
                            ],
                          ),
                          ],
                        ),
                      ),
                      Divider(height: 1),
                      // Calendar
                      Container(
                        height: 280,
                        child: CalendarDatePicker(
                          initialDate: tempSelectedDate,

                          key: ValueKey(tempSelectedDate),
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2100),
                          currentDate: today,

                          onDateChanged: (DateTime date) {
                            setStateDialog(() {
                              tempSelectedDate = date;
                            });
                          },
                        ),
                      ),
                      // Action buttons - Today, Cancel, OK
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 8),
                        child: Row(
                          children: [
                            // Today button on the left
                            TextButton(
                              onPressed: () {
                                setStateDialog(() {
                                  tempSelectedDate = today;
                                });
                              },
                              child: const Text(
                                'Today',
                                style: TextStyle(
                                  color: Colors.blue,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            Spacer(),
                            // Cancel and OK buttons on the right
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              child: const Text(
                                'Cancel',
                                style: TextStyle(color: Colors.grey),
                              ),
                            ),
                            SizedBox(width: 8),
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop(tempSelectedDate);
                              },
                              child: const Text(
                                'OK',
                                style: TextStyle(
                                  color: Colors.blue,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );

    if (selectedDate != null) {
      setState(() {
        _selectedDate = selectedDate;
        _updateDateController();
      });

      // Save to UserModel in yyyy-mm-dd format
      Provider.of<UserModel>(context, listen: false).setSelectedDate(
          "${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}");
    }
  }

  @override
  void dispose() {
    _dateController.dispose();
    super.dispose();
  }

  Future<void> _fetchTrainNumbers() async {
    try {
      _trainNumbers = await TrainService.getTrainNumbers();
      setState(() {});
    } catch (e) {
      print('Error fetching train numbers: $e');
    }
  }

  void _onTrainNumberChanged(String? trainNumber) async {
    final trainName = await fetchTrainName(trainNumber);
    setState(() {
      _selectedTrainNumber = trainNumber;
      _trainName = trainName;
    });
    Provider.of<UserModel>(context, listen: false).setTrainNo(trainNumber!);
  }

  Future<String?> fetchTrainName(String? trainNumber) async {
    return await TrainService.getTrainName(trainNumber!);
  }

  Future<void> _submitForm() async {
    if (_formKey.currentState?.validate() ?? false) {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;

      // Show a loading indicator while processing the form
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              width: 100,
              height: 100,
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 6.0,
                ),
              ),
            ),
          );
        },
      );

      try {
        // Fetch current location when form is submitted
        await _getCurrentLocation();

        // Fetch stations near the current location
        await _fetchStations();

        // Close the loading indicator
        Navigator.pop(context);

        if (_currentLocation == null) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
              content: Text('Please turn on location services'),
              duration: Duration(seconds: 5)));
        } else {
          setState(() {
            _showMap = true;
          });
        }
      } catch (e) {
        // Close the loading indicator in case of error
        Navigator.pop(context);
        print('Error submitting form: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('An error occurred: $e'),
            duration: const Duration(seconds: 5),
          ),
        );
      }
    }
  }

  // Method to get current location using geolocator
  Future<void> _getCurrentLocation() async {
    bool serviceEnabled;
    LocationPermission permission;

    // Check if location services are enabled
    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      print('Location services are disabled.');
      return;
    }

    // Check location permissions
    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        print('Location permissions are denied.');
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      print('Location permissions are permanently denied.');
      return;
    }

    // Get current position
    Position position = await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );

    setState(() {
      _currentLocation = LatLng(position.latitude, position.longitude);
    });
  }

  Future<void> _fetchStations() async {
    if (_currentLocation == null) return;

    try {
      // Fetch the stations using MapServices
      List<dynamic> stations = await MapServices.fetchRailwayStations(
        latitude: _currentLocation!.latitude,
        longitude: _currentLocation!.longitude,
      );

      // Fetch station names and codes for comparison
      List<String> trainStations = await fetchRoute(_selectedTrainNumber);

      // Create markers for each station
      List<StationMarker> markers = [];
      for (var station in stations) {
        LatLng stationPosition = LatLng(
          station['lat'],
          station['lon'],
        );

        // Determine marker color based on station code
        Color markerColor = Colors.black; // Default color
        if (trainStations.contains(station['tags']['ref'])) {
          markerColor = Colors.red; // Color for matching station code

          // Fetch onboarding and offboarding details for red-colored markers
          final stationObj = Station(
            name: station['tags']['ref'] ?? 'Unknown',
            lat: station['lat'],
            lon: station['lon'],
          );

          final details = await fetchStationOnboardingDetails(
            _selectedTrainNumber!,
            station['tags']['ref'] ?? 'Unknown',
            _selectedDate!,
          );

          markers.add(
            StationMarker(
              station: Station(
                name: stationObj.name,
                lat: stationObj.lat,
                lon: stationObj.lon,
                onboardingCount: details.onboardingCount,
                offboardingCount: details.offboardingCount,
              ),
              color: markerColor,
            ),
          );
        } else {
          markers.add(
            StationMarker(
              station: Station(
                name: station['tags']['ref'] ?? 'Unknown',
                lat: station['lat'],
                lon: station['lon'],
                onboardingCount: -1,
                offboardingCount: -1,
              ),
              color: markerColor,
            ),
          );
        }
      }

      setState(() {
        _stationMarkers = markers;
      });
    } catch (e) {
      print('Error fetching stations: $e');
    }
  }

  Future<List<String>> fetchRoute(String? trainNumber) async {
    final result = await TrainService.getTrainStations(trainNumber!);
    List<String> stationNames = result['stationList'];
    return stationNames;
  }

  Future<Station> fetchStationOnboardingDetails(
      String trainNumber, String selectedStation, DateTime selectedDate) async {
    DateFormat formatter = DateFormat('yyyy-MM-dd');
    String formattedDate = formatter.format(selectedDate);

    try {
      TrainDataResponse trainDataResponse = await TrainService.fetchTrainData(
        trainNumber: trainNumber,
        date: formattedDate,
        stationCode: selectedStation,
      );

      int totalOnboarding = 0;
      int totalOffboarding = 0;

      for (TrainDetail trainDetail in trainDataResponse.trainsData) {
        for (BerthDetail berthDetail in trainDetail.details) {
          totalOnboarding += berthDetail.countOfBirth;
          totalOffboarding += berthDetail.offBoardingCountOfBirth;
        }
      }

      return Station(
        name: selectedStation,
        lat: 0.0,
        lon: 0.0,
        onboardingCount: totalOnboarding,
        offboardingCount: totalOffboarding,
      );
    } catch (e) {
      print('Error fetching train data: $e');
      return Station(
        name: selectedStation,
        lat: 0.0,
        lon: 0.0,
      );
    }
  }

  Future<void> _refreshMap() async {
    // Show a loading indicator while refreshing the map
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            width: 100,
            height: 100,
            child: const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                strokeWidth: 6.0,
              ),
            ),
          ),
        );
      },
    );

    // Fetch current location when refreshing the map
    await _getCurrentLocation();

    // Fetch stations near the current location
    await _fetchStations();

    _mapController?.move(_currentLocation!, 13);

    // Close the loading indicator
    Navigator.pop(context);

    // Center the map on the current location
    if (_currentLocation != null) {
      setState(() {
        _showMap = true;
      });
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please turn on location services'),
          duration: Duration(seconds: 5),
        ),
      );
    }
  }

  Future<void> _reloadPage() async {
    await Future.delayed(const Duration(seconds: 3));
    reloadCurrentScreen(context, const MapScreen());
  }

  void reloadCurrentScreen(BuildContext context, Widget currentScreen) {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => currentScreen,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _reloadPage,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Container(
          constraints: BoxConstraints(
            minHeight: MediaQuery.of(context).size.height,
          ),
          child: Stack(
            children: [
              // This is the main column that was causing issues
              Column(
                // Fix: Set mainAxisSize to min instead of max (the default)
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (!_showMap)
                    Form(
                      key: _formKey,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Train Number Dropdown
                            DropdownSearch<String>(
                              items: _trainNumbers,
                              selectedItem: _selectedTrainNumber,
                              onChanged: _onTrainNumberChanged,
                              dropdownDecoratorProps:
                                  const DropDownDecoratorProps(
                                dropdownSearchDecoration: InputDecoration(
                                  labelText: 'Select Train Number',
                                  border: OutlineInputBorder(),
                                ),
                              ),
                              popupProps: PopupProps.menu(
                                showSearchBox: true,
                                searchFieldProps: TextFieldProps(
                                  keyboardType: TextInputType.number,
                                  inputFormatters: <TextInputFormatter>[
                                    FilteringTextInputFormatter.digitsOnly,
                                    LengthLimitingTextInputFormatter(6),
                                  ],
                                ),
                                itemBuilder: (context, item, isSelected) =>
                                    ListTile(
                                  title: Text(item),
                                  selected: isSelected,
                                ),
                              ),
                              validator: (value) =>
                                  value == null || value.isEmpty
                                      ? 'Please select a train number'
                                      : null,
                            ),
                            const SizedBox(height: 16),
                            TextFormField(
                              decoration: const InputDecoration(
                                labelText: 'Train Name',
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: 8, vertical: 8),
                              ),
                              readOnly: true,
                              controller:
                                  TextEditingController(text: _trainName),
                            ),
                                const SizedBox(height: 16),
                            // Custom Date Picker
                            TextFormField(
                              decoration: const InputDecoration(
                                labelText: 'Select Date (DD-MMM-YYYY)',
                                border: OutlineInputBorder(),
                                suffixIcon: Icon(Icons.calendar_today),
                                contentPadding:
                                    EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                              ),
                              readOnly: true,
                              onTap: _selectDate,
                              controller: _dateController,
                              validator: (value) {
                                if (_selectedDate == null) {
                                  return 'Please select a date';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            // Submit Button
                            Center(
                              child: ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.white,
                                  foregroundColor: Colors.black,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  side: BorderSide(color: Colors.black87, width: 0.5)
                                ),
                                onPressed: _submitForm,
                                child: const Text('Submit'),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  if (_showMap)
                    // Fix: Replace Expanded with SizedBox with a fixed height
                    SizedBox(
                      height: MediaQuery.of(context).size.height - 10, // Adjust height as needed
                      child: FlutterMap(
                        mapController: _mapController,
                        options: MapOptions(
                          initialCenter: _currentLocation ??
                              const LatLng(1.2878, 103.8666),
                          initialZoom: 13,
                          interactionOptions: const InteractionOptions(
                            flags: ~InteractiveFlag.doubleTapZoom,
                          ),
                        ),
                        children: [
                          openStreetMapTileLayer,
                          if (_currentLocation != null)
                            PopupMarkerLayer(
                              options: PopupMarkerLayerOptions(
                                markers: [
                                  Marker(
                                    point: _currentLocation!,
                                    width: 60,
                                    height: 60,
                                    alignment: Alignment.centerLeft,
                                    child: const Icon(
                                      Icons.location_pin,
                                      size: 60,
                                      color: Colors.red,
                                    ),
                                  ),
                                ],
                                popupController: _popupLayerController,
                                popupDisplayOptions: PopupDisplayOptions(
                                  builder: (_, Marker marker) {
                                    return const Card(
                                        child: Padding(
                                      padding: EdgeInsets.all(8.0),
                                      child: Text('Your current location'),
                                    ));
                                  },
                                ),
                              ),
                            ),
                          // Add station markers to the map
                          PopupMarkerLayer(
                            options: PopupMarkerLayerOptions(
                              markers: _stationMarkers,
                              popupDisplayOptions: PopupDisplayOptions(
                                builder: (_, Marker marker) {
                                  if (marker is StationMarker) {
                                    return StationMarkerPopup(
                                      station: marker.station,
                                      trainNumber: _selectedTrainNumber ?? '',
                                      selectedDate:
                                          _selectedDate ?? DateTime.now(),
                                    );
                                  }
                                  return const SizedBox.shrink();
                                },
                              ),
                            ),
                          ),
                          RichAttributionWidget(
                            animationConfig: const ScaleRAWA(),
                            attributions: [
                              TextSourceAttribution(
                                'OpenStreetMap contributors',
                                onTap: () => launchUrl(Uri.parse(
                                    'https://openstreetmap.org/copyright')),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              if (_showMap)
                Positioned(
                  top: 12.0,
                  left: 12.0,
                  child: FloatingActionButton(
                    mini: true,
                    onPressed: () {
                      setState(() {
                        _showMap = false;
                      });
                    },
                    child: const Icon(Icons.arrow_back),
                    backgroundColor: Colors.white,
                  ),
                ),
              if (_showMap)
                Positioned(
                  top: 12.0,
                  right: 12.0,
                  child: FloatingActionButton(
                    mini: true,
                    onPressed: _refreshMap,
                    child: const Icon(Icons.refresh),
                    backgroundColor: Colors.white,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

TileLayer get openStreetMapTileLayer => TileLayer(
      urlTemplate:
          'https://api.maptiler.com/maps/basic-v2/256/{z}/{x}/{y}.png?key=8bvX5lCd2Ldla13L1zOU',
      userAgentPackageName: 'dev.fleaflet.flutter_map.example',
    );

class Station {
  static const double size = 30;

  Station({
    required this.name,
    required this.lat,
    required this.lon,
    this.onboardingCount = 0,
    this.offboardingCount = 0,
  });

  final String name;
  final double lat;
  final double lon;
  final int onboardingCount;
  final int offboardingCount;
}

class StationMarker extends Marker {
  StationMarker({
    required this.station,
    required this.color,
  }) : super(
          alignment: Alignment.topCenter,
          height: color == Colors.red ? 60 : Station.size,
          width: color == Colors.red ? 60 : Station.size,
          point: LatLng(station.lat, station.lon),
          child: Icon(
            Icons.directions_railway,
            color: color,
            size: color == Colors.red ? 60 : Station.size,
          ),
        );

  final Station station;
  final Color color;
}

class StationMarkerPopup extends StatelessWidget {
  const StationMarkerPopup({
    super.key,
    required this.station,
    required this.trainNumber,
    required this.selectedDate,
  });

  final Station station;
  final String trainNumber;
  final DateTime selectedDate;

  @override
  Widget build(BuildContext context) {
    // Format the date for display
    final formattedDate = DateFormat('dd-MM-yyyy').format(selectedDate);

    return SizedBox(
      width: 150, // Adjust width as needed
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text(
                station.name,
                style: Theme.of(context).textTheme.titleSmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              if (station.onboardingCount >= 0) ...[
                Text(
                  'Train Number: $trainNumber',
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
                Text(
                  'Date: $formattedDate',
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'Onboarding: ${station.onboardingCount}',
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
                Text(
                  'Offboarding: ${station.offboardingCount}',
                  style: Theme.of(context).textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

String _formatDateToCustomFormat(DateTime date) {
  final day = date.day.toString().padLeft(2, '0');
  final month = DateFormat('MMM').format(date);
  final year = date.year.toString();
  return '$day-$month-$year';

}

