import 'dart:async';
import 'dart:io';
import 'dart:isolate';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/screens/attendance/background_upload_serice.dart';
import 'package:railops/screens/attendance/background_upload_task.dart';
import 'package:railops/screens/attendance/upload_manager.dart';
import 'package:railops/screens/attendance/uploaded_image.dart';
import 'package:railops/services/attendance_services/attendance_sevices.dart';
import 'package:railops/types/attendance_types/attendance_data.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';
import 'package:flutter/foundation.dart';
import 'package:railops/widgets/error_modal.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';

class ImageUploadPage extends StatefulWidget {
  final String selectedOption;
  final String trainNumber;
  final String journeyDate;
  final String stationCode;
  final String stationName;
  final String forUserType;
  final bool isMediaUploadEnabled;

  const ImageUploadPage({
    Key? key,
    required this.selectedOption,
    required this.trainNumber,
    required this.journeyDate,
    required this.stationCode,
    required this.stationName,
    required this.forUserType,
    required this.isMediaUploadEnabled,
  }) : super(key: key);

  @override
  _ImageUploadPageState createState() => _ImageUploadPageState();
}

class _ImageUploadPageState extends State<ImageUploadPage> {
  XFile? _selectedImage;
  XFile? _selectedImageForUpload;
  final ImagePicker _imagePicker = ImagePicker();
  bool _isImageLoading = false;
  bool _isUploading = false;

  String? _selectedUser;
  String? username;
  String? token;
  String? _userType;

  List<AttendanceData> uploadedImages = [];
  List<String> _users = [];
  Map<String, String> userImages = {};
  Map<String, Map<String, String>> userDetails = {};

  List<AttendanceData> _selectedUserImages = [];
  bool _isLoadingSelectedUserImages = false;

  Map<String, BackgroundUploadTask> _backgroundTasks = {};
  bool _isCompressing = false;
  bool _isPositioning = false;

  @override
  void initState() {
    super.initState();
    final userModel = Provider.of<UserModel>(context, listen: false);
    username = userModel.userName;
    token = userModel.token;
    _userType = userModel.userType;
    if (widget.selectedOption == "self") {
      _fetchUploadedImages();
    } else {
      fetchSpecificUsers().then((_) => _fetchAllUsersImages());
    }
  }

  @override
  void dispose() {
    _backgroundTasks.forEach((_, task) {
      task.dispose();
    });
    _backgroundTasks.clear();
    super.dispose();
  }

  Future<void> fetchSpecificUsers() async {
    try {
      final userModel = Provider.of<UserModel>(context, listen: false);
      final token = userModel.token;

      if (token.isNotEmpty) {
        final response = await AttendanceService.fetchAllUsers(
          token,
          widget.trainNumber,
          widget.journeyDate,
        );

        setState(() {
          // Update users list based on type
          if (widget.forUserType.toLowerCase() == "coach attendent") {
            _users = List<String>.from(response['users_ca_ehk'] ?? []);
          } else if (widget.forUserType.toUpperCase() == "OBHS") {
            _users = List<String>.from(response['users_obhs'] ?? []);
          }

          // Parse all user details
          userDetails = response['user_details'];
        });
      }
    } catch (e) {
      showErrorModal(
        context,
        'Failed to fetch users: $e',
        "Error",
        () {},
      );
    }
  }

  Future<void> _fetchAllUsersImages() async {
    if (widget.selectedOption != "self") {
      setState(() {
        _isImageLoading = true;
      });

      for (String user in _users) {
        try {
          final response = await AttendanceService.fetchAttendance(
            trainNumber: widget.trainNumber,
            stationCode: widget.stationCode,
            date: widget.journeyDate,
            userName: user,
            token: token!,
          );

          if (response != null && response.isNotEmpty) {
            setState(() {
              userImages[user] = response.first.imageUrl;
            });
          }
        } catch (e) {
          print('Error fetching image for user $user: $e');
        }
      }

      setState(() {
        _isImageLoading = false;
      });
    }
  }

  Future<Position?> _getCurrentPosition() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        await Geolocator.requestPermission();
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        permission = await Geolocator.requestPermission();
      }

      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
    } catch (e) {
      showErrorModal(
        context,
        'Please Enable the location service!',
        "Error",
        () {},
      );
      await Geolocator.requestPermission();
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
    }
  }

  Future<void> _pickImage() async {
    final ImageSource? source = await showDialog<ImageSource>(
      context: context,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Camera'),
              onTap: () => Navigator.pop(context, ImageSource.camera),
            ),
            if ((_userType == "railway admin" ||
                    _userType == "railway officer" ||
                    _userType == "s2 admin" ||
                    _userType == "war room user") ||
                  widget.isMediaUploadEnabled)
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Gallery'),
                onTap: () => Navigator.pop(context, ImageSource.gallery),
              ),
          ],
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );

    if (source != null) {
      final pickedImage = await _imagePicker.pickImage(source: source);
      if (pickedImage != null) {
        setState(() {
          _selectedImage = pickedImage;
          _selectedImageForUpload = pickedImage;
        });
      }
    }
  }

  Future<XFile> resizeImage(XFile imageFile, {int maxWidth = 200}) async {
    return await compute(_resizeImageInBackground,
        {'path': imageFile.path, 'maxWidth': maxWidth});
  }

// Separate method to run image resizing in a background isolate
  static XFile _resizeImageInBackground(Map<String, dynamic> params) {
    final String imagePath = params['path'];
    final int maxWidth = params['maxWidth'];

    try {
      final Uint8List imageBytes = File(imagePath).readAsBytesSync();
      img.Image? image = img.decodeImage(imageBytes);

      if (image == null) {
        throw Exception("Failed to decode image");
      }

      img.Image resized = img.copyResize(image, width: maxWidth);
      Uint8List resizedBytes;
      try {
        resizedBytes = Uint8List.fromList(img.encodeJpg(resized, quality: 90));
      } catch (e) {
        resizedBytes = Uint8List.fromList(img.encodePng(resized));
      }

      final tempDir = Directory.systemTemp;
      String uniqueFileName =
          'resized_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final tempPath = '${tempDir.path}/$uniqueFileName';
      File resizedFile = File(tempPath);
      resizedFile.writeAsBytesSync(resizedBytes);

      return XFile(tempPath, name: uniqueFileName);
    } catch (e) {
      print('Image resizing error: $e');
      return XFile(imagePath);
    }
  }

  Future<void> _uploadImageAndCreateAttendance() async {
    if (_selectedImage == null) return;

    final userModel = Provider.of<UserModel>(context, listen: false);
    final uploadManager = Provider.of<UploadManager>(context, listen: false);
    final token = userModel.token;

    // ScaffoldMessenger.of(context).showSnackBar(
    //   const SnackBar(
    //     content: Text('Getting location...'),
    //     duration: Duration(seconds: 1),
    //   ),
    // );

    Position? position;

    try {
      setState(() {
        _isPositioning = true;
        _isUploading = true;
      });

      position = await _getCurrentPosition();

      setState(() {
        _isPositioning = false;
        _isUploading = false;
        _selectedImage = null;
      });

      _showUploadNotificationModal();

      // Call the service to handle the upload
      final uploadService = BackgroundUploadService();
      await uploadService.uploadImageAndCreateAttendance(
        imagePath: _selectedImageForUpload!.path,
        username: widget.selectedOption == "other" ? _selectedUser! : username!,
        trainNumber: widget.trainNumber,
        stationCode: widget.stationCode,
        date: widget.journeyDate,
        token: token!,
        latitude: position?.latitude,
        longitude: position?.longitude,
        uploadManager: uploadManager,
        onSuccess: () {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Image uploaded! Background verification in progress. Status update soon.'),
                backgroundColor: Colors.green,
              ),
            );
            _handleRefresh();
          }
        },
        onError: (message) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Error: $message'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to get location: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isPositioning = false;
        });
      }
    }
  }

  Widget _buildStatusIndicator() {
    if (_isCompressing || _backgroundTasks.isNotEmpty) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        color: Colors.blue.shade50,
        child: Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _isCompressing
                    ? 'Compressing image...'
                    : 'Processing ${_backgroundTasks.length} tasks in background...',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.blue.shade800,
                ),
              ),
            ),
          ],
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Future<void> _fetchUploadedImages() async {
    if (widget.selectedOption == "self") {
      try {
        setState(() {
          _isImageLoading = true;
        });

        final response = await AttendanceService.fetchAttendance(
          trainNumber: widget.trainNumber,
          stationCode: widget.stationCode,
          date: widget.journeyDate,
          userName: username,
          token: token!,
        );

        if (response != null) {
          setState(() {
            uploadedImages = response;
          });
        }
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error fetching images: $e')),
        );
      } finally {
        setState(() {
          _isImageLoading = false;
        });
      }
    }
  }

  Future<void> _fetchSelectedUserImages() async {
    if (_selectedUser == null) return;

    setState(() {
      _isLoadingSelectedUserImages = true;
    });

    try {
      final response = await AttendanceService.fetchAttendance(
        trainNumber: widget.trainNumber,
        stationCode: widget.stationCode,
        date: widget.journeyDate,
        userName: _selectedUser,
        token: token!,
      );

      setState(() {
        _selectedUserImages = response ?? [];
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error fetching images: $e')),
      );
    } finally {
      setState(() {
        _isLoadingSelectedUserImages = false;
      });
    }
  }

  void _handleUserCardTap(String user) {
    setState(() {
      _selectedUser = _selectedUser == user ? null : user;
      _selectedUserImages = [];
    });
    if (_selectedUser != null) {
      _fetchSelectedUserImages();
    }
  }

  Future<void> _handleRefresh() async {
    if (widget.selectedOption == "self") {
      await _fetchUploadedImages();
    } else {
      await fetchSpecificUsers();
      await _fetchAllUsersImages();
      await _fetchSelectedUserImages();
    }
  }

  void _showAlreadySubmittedPopup() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Attendance Already Submitted'),
          content: const Text(
            'You have already submitted attendance for this station. You cannot submit another one.',
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDetailRow(String title, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 16),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
      ),
      itemCount: _users.length,
      itemBuilder: (context, index) {
        final user = _users[index];
        final isSelected = _selectedUser == user;

        return Card(
          elevation: isSelected ? 8 : 4,
          color: isSelected ? Colors.blue.shade50 : null,
          child: InkWell(
            onTap: () => _handleUserCardTap(user),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    shape: BoxShape.circle,
                  ),
                  child: userImages[user] != null
                      ? ClipOval(
                          child: Image.network(
                            userImages[user]!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                const Icon(Icons.person,
                                    size: 40, color: Colors.blue),
                          ),
                        )
                      : const Icon(Icons.person, size: 40, color: Colors.blue),
                ),
                const SizedBox(height: 8),
                Text(
                  'Id: ${userDetails[user]!["emp_number"]}',
                  textAlign: TextAlign.center,
                  style: TextStyle(),
                ),
                Text(
                  user,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                // Text(
                //   userDetails[user]!["user_type"]!,
                //   textAlign: TextAlign.center,
                //   style: TextStyle(),
                // ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSelectedUserContent() {
    if (_selectedUser == null) {
      return _buildUserGrid();
    }

    return Column(
      children: [
        // Back to all users button
        Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Align(
            alignment: Alignment.centerLeft, // Align to the left
            child: ElevatedButton.icon(
              onPressed: () => setState(() {
                _selectedUser = null;
                _selectedImage = null;
              }),
              icon: const Icon(Icons.arrow_back),
              label: const Text('Back to all users'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[300],
                foregroundColor: Colors.black87,
              ),
            ),
          ),
        ),

        // Selected user card
        Card(
          elevation: 4,
          child: Padding(
            padding:
                const EdgeInsets.all(8), // Reduced padding for a smaller card
            child: Row(
              children: [
                Container(
                  width: 50, // Reduced width for the image
                  height: 50, // Reduced height for the image
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    shape: BoxShape.circle,
                  ),
                  child: userImages[_selectedUser] != null
                      ? ClipOval(
                          child: Image.network(
                            userImages[_selectedUser]!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                const Icon(Icons.person,
                                    size: 40, color: Colors.grey),
                          ),
                        )
                      : const Icon(Icons.person, size: 40, color: Colors.grey),
                ),
                const SizedBox(width: 8), // Spacing between image and text
                Expanded(
                  child: Text(
                    _selectedUser!,
                    style: const TextStyle(
                      fontSize: 16, // Smaller font size
                      fontWeight: FontWeight.bold,
                    ),
                    overflow:
                        TextOverflow.ellipsis, // Ensures text doesn't overflow
                    softWrap:
                        false, // Prevents text wrapping into multiple lines
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 20),

        if (_isUploading)
          const Center(
            child: CircularProgressIndicator(color: Colors.blueAccent),
          ),

        // Image preview
        if (_selectedImage != null)
          Container(
            height: 200,
            width: double.infinity,
            margin: const EdgeInsets.only(bottom: 20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: kIsWeb
                  ? Image.network(_selectedImage!.path, fit: BoxFit.contain)
                  : Image.file(File(_selectedImage!.path), fit: BoxFit.contain),
            ),
          ),

        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _selectedUserImages.isNotEmpty
                ? () => _showAlreadySubmittedPopup()
                : _pickImage,
            icon: const Icon(Icons.photo_camera),
            label: Text(_selectedImage == null ? 'Pick Photo' : 'Retake Photo'),
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor:
                  _selectedUserImages.isNotEmpty ? Colors.grey : Colors.white,
              foregroundColor: _selectedUserImages.isNotEmpty
                  ? Colors.white
                  : Colors.black87,
              side: BorderSide(color: Colors.black87, width: 0.5),
            ),
          ),
        ),

        if (_selectedImage != null) ...[
          const SizedBox(height: 10),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _uploadImageAndCreateAttendance,
              style: ElevatedButton.styleFrom(
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor: Colors.white,
                foregroundColor: Colors.green,
                side: BorderSide(color: Colors.black87, width: 0.5),
              ),
              child: const Text('Submit'),
            ),
          ),
        ],

        const SizedBox(height: 20),

        Container(
          margin: const EdgeInsets.symmetric(vertical: 20),
          child: Card(
            margin: EdgeInsets.zero,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Uploaded Images',
                    style: TextStyle(
                      decoration: TextDecoration.underline,
                      fontSize: 18,
                    ),
                  ),
                  const SizedBox(height: 16),
                  if (_selectedUserImages.isEmpty)
                    const Text(
                      'No images uploaded',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    )
                  else
                    GridView.builder(
                      shrinkWrap:
                          true, // Ensures it doesn't take unnecessary space
                      physics:
                          const NeverScrollableScrollPhysics(), // Prevent scrolling inside the card
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 1, // Two images per row
                        crossAxisSpacing: 8, // Spacing between columns
                        mainAxisSpacing: 8,
                        childAspectRatio: 2,
                      ),
                      itemCount: _selectedUserImages.length,
                      itemBuilder: (context, index) {
                        final imageData = _selectedUserImages[index];
                        if (imageData.imageUrl.isEmpty)
                          return const SizedBox.shrink();

                        return UploadedImage(
                          imageResponse: imageData,
                          trainNumber: widget.trainNumber,
                          onImageDelete: _fetchSelectedUserImages,
                        );
                      },
                    ),
                ],
              ),
            ),
          ),
        )
      ],
    );
  }

  // Add this method to show upload details
  void _showUploadStatusDetails() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Upload Status'),
        content: Container(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (_isCompressing)
                ListTile(
                  leading: SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  title: Text('Compressing image'),
                  dense: true,
                ),
              if (_backgroundTasks.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0, bottom: 4.0),
                  child: Text(
                    'Active Uploads:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ..._backgroundTasks.entries
                  .map((entry) => ListTile(
                        leading: SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            value: entry.value.progress,
                          ),
                        ),
                        title: Text('Upload ${entry.key.substring(0, 6)}...'),
                        subtitle: Text(
                            '${(entry.value.progress * 100).toStringAsFixed(0)}% complete'),
                        dense: true,
                      ))
                  .toList(),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showUploadNotificationModal() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Image Uploading'),
          content: const Text(
            'Your image is being uploaded in the background. Please refresh the page after some time to see the updated status.',
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: "Upload Image",
        onViewUploads: _showUploadStatusDetails,
      ),
      drawer: const CustomDrawer(),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        child: LayoutBuilder(
          builder: (context, constraints) {
            return Column(
              children: [
                Expanded(
                    child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(), // Add this
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: constraints
                          .maxHeight, // Ensures minimum content height
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                Card(
                                  elevation: 5,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(16.0),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        _buildDetailRow('Train Number:',
                                            widget.trainNumber),
                                        _buildDetailRow('Journey Date:',
                                            widget.journeyDate),
                                        _buildDetailRow('Station Code:',
                                            widget.stationCode),
                                        _buildDetailRow('Station Name:',
                                            widget.stationName),
                                      ],
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 20),

                                // Rest of your existing content...
                                if (_isImageLoading)
                                  const Center(
                                    child: CircularProgressIndicator(
                                        color: Colors.blueAccent),
                                  )
                                else if (widget.selectedOption != "self")
                                  _buildSelectedUserContent()
                                else ...[
                                  // Self upload UI
                                  if (_isUploading)
                                    const Center(
                                      child: CircularProgressIndicator(
                                          color: Colors.blueAccent),
                                    )
                                  else ...[
                                    // Image preview area
                                    if (_selectedImage != null)
                                      Container(
                                        height: 200,
                                        width: double.infinity,
                                        margin:
                                            const EdgeInsets.only(bottom: 20),
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          border: Border.all(
                                              color: Colors.grey.shade300),
                                        ),
                                        child: ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                          child: kIsWeb
                                              ? Image.network(
                                                  _selectedImage!.path,
                                                  fit: BoxFit.contain,
                                                )
                                              : Image.file(
                                                  File(_selectedImage!.path),
                                                  fit: BoxFit.contain,
                                                ),
                                        ),
                                      ),

                                    // Take Photo button
                                    ElevatedButton.icon(
                                      onPressed: uploadedImages.isNotEmpty
                                          ? () => _showAlreadySubmittedPopup()
                                          : _pickImage,
                                      icon: const Icon(Icons.photo_camera),
                                      label: Text(_selectedImage == null
                                          ? 'Pick Photo'
                                          : 'Retake Photo'),
                                      style: ElevatedButton.styleFrom(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 16),
                                        shape: RoundedRectangleBorder(
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        backgroundColor:
                                            uploadedImages.isNotEmpty
                                                ? Colors.grey
                                                : Colors.blueAccent,
                                        foregroundColor: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(height: 20),

                                    // Submit button
                                    if (_selectedImage != null)
                                      ElevatedButton(
                                        onPressed:
                                            _uploadImageAndCreateAttendance,
                                        style: ElevatedButton.styleFrom(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 16),
                                          shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          backgroundColor: Colors.green,
                                          foregroundColor: Colors.white,
                                        ),
                                        child: const Text('Submit'),
                                      ),

                                    const SizedBox(height: 20),

                                    Container(
                                      margin: const EdgeInsets.symmetric(
                                          vertical: 20),
                                      child: Card(
                                        margin: EdgeInsets.zero,
                                        child: Padding(
                                          padding: const EdgeInsets.all(16.0),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              const Text(
                                                'Uploaded Images',
                                                style: TextStyle(
                                                  decoration:
                                                      TextDecoration.underline,
                                                  fontSize: 18,
                                                ),
                                              ),
                                              const SizedBox(height: 16),
                                              if (uploadedImages.isEmpty)
                                                const Text(
                                                  'No images uploaded',
                                                  style: TextStyle(
                                                    fontSize: 16,
                                                    color: Colors.grey,
                                                  ),
                                                )
                                              else
                                                GridView.builder(
                                                  shrinkWrap:
                                                      true, // Ensures it doesn't take unnecessary space
                                                  physics:
                                                      const NeverScrollableScrollPhysics(), // Prevent scrolling inside the card
                                                  gridDelegate:
                                                      const SliverGridDelegateWithFixedCrossAxisCount(
                                                    crossAxisCount:
                                                        1, // Two images per row
                                                    crossAxisSpacing:
                                                        8, // Spacing between columns
                                                    mainAxisSpacing: 8,
                                                    childAspectRatio: 2,
                                                  ),
                                                  itemCount:
                                                      uploadedImages.length,
                                                  itemBuilder:
                                                      (context, index) {
                                                    final imageData =
                                                        uploadedImages[index];
                                                    if (imageData
                                                        .imageUrl.isEmpty)
                                                      return const SizedBox
                                                          .shrink();

                                                    return UploadedImage(
                                                      imageResponse: imageData,
                                                      trainNumber:
                                                          widget.trainNumber,
                                                      onImageDelete:
                                                          _fetchUploadedImages,
                                                    );
                                                  },
                                                ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    )
                                  ],
                                ]
                              ]),
                        ),
                      ],
                    ),
                  ),
                ))
              ],
            );
          },
        ),
      ),
    );
  }
}
