// import 'package:flutter/material.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:railops/main.dart';
// import 'package:railops/services/location_services/location_services.dart';
// import 'package:railops/types/edit_train_types/edit_train_types.dart';

// class AttendanceLocationProvider extends ChangeNotifier {
//   final BuildContext context;
//   final String userToken;
//   final EditTrainsData? trainData;

//   AttendanceLocationProvider({
//     required this.context,
//     required this.userToken,
//     required this.trainData,
//   });

//   String? _lastAlertedStation;
// Future<void> saveTrainLocation({bool isTest = false}) async {
//   try {
//     double currentLat;
//     double currentLng;

//     if (isTest) {
//       // 🔧 Use test coordinates
//       currentLat = 22.5828709;
//       currentLng = 88.3428112;
//     } else {
//       // 📍 Use actual GPS location
//       final position = await Geolocator.getCurrentPosition(
//         desiredAccuracy: LocationAccuracy.high,
//       );
//       currentLat = position.latitude;
//       currentLng = position.longitude;
//     }

//     await LocationService.saveTrainLocation(
//       userToken,
//       currentLat.toString(),
//       currentLng.toString(),
//     );

//     List<String> nearbyStations = [];

//     for (var stoppage in trainData?.stoppagesWithLatLng ?? []) {
//       final lat = stoppage.latitude;
//       final lng = stoppage.longitude;

//       if (lat != null && lng != null) {
//         final distance = Geolocator.distanceBetween(
//           currentLat,
//           currentLng,
//           lat,
//           lng,
//         );
//         if (distance <= 50000) {
//           nearbyStations.add(stoppage.stationName ?? stoppage.stationCode);
//         }
//       }
//     }

//     if (nearbyStations.isNotEmpty) {
//       String currentNearbyStation = nearbyStations.first;

//       if (currentNearbyStation != _lastAlertedStation) {
//         _lastAlertedStation = currentNearbyStation;

//         await showLocalNotification(
//           "You're near: $currentNearbyStation",
//           "Tap to view train or station updates.",
//         );

//         if (context.mounted) {
//           await showDialog(
//             context: context,
//             builder: (ctx) => AlertDialog(
//               title: const Text("🛤️ Nearby Station Alert"),
//               content: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   const Text("You're near the following station(s):"),
//                   const SizedBox(height: 8),
//                   ...nearbyStations.take(3).map((station) => Text("• $station")),
//                 ],
//               ),
//               actions: [
//                 TextButton(
//                   child: const Text("OK"),
//                   onPressed: () => Navigator.of(ctx).pop(),
//                 ),
//               ],
//             ),
//           );
//         }
//       }
//     }
//   } catch (e) {
//     print("❌ Error saving train location: $e");
//   }
// }



//    Future<void> showLocalNotification(String title, String body) async {
//   const AndroidNotificationDetails androidPlatformChannelSpecifics =
//       AndroidNotificationDetails(
//     'station_alerts_channel',
//     'Station Alerts',
//     channelDescription: 'Alerts when train is near a station',
//     importance: Importance.max,
//     priority: Priority.high,
//     showWhen: true,
//   );

//   const NotificationDetails platformChannelSpecifics =
//       NotificationDetails(android: androidPlatformChannelSpecifics);

//   await flutterLocalNotificationsPlugin.show(
//     DateTime.now().millisecondsSinceEpoch ~/ 1000, // unique ID
//     title,
//     body,
//     platformChannelSpecifics,
//   );
// }
// }
