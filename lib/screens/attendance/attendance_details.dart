import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/screens/attendance/widget/delete_button.dart';
import 'package:railops/screens/pages/image_detail_page.dart';
import 'package:railops/services/attendance_services/attendance_sevices.dart';
import 'package:railops/services/location_services/location_services.dart';
import 'package:railops/types/attendance_types/attendance_entery.dart';
import 'package:railops/types/image_detail_types/image_detail_types.dart';
import 'package:railops/widgets/custom_app_bar.dart';
import 'package:railops/widgets/custom_drawer.dart';

class AttendanceDetailsPage extends StatefulWidget {
  final String trainNumber;
  final String date;
  final String stationCode;

  const AttendanceDetailsPage({
    super.key,
    required this.trainNumber,
    required this.date,
    required this.stationCode,
  });

  @override
  _AttendanceDetailsPageState createState() => _AttendanceDetailsPageState();
}

class _AttendanceDetailsPageState extends State<AttendanceDetailsPage> {
  Future<List<AttendanceEntry>>? _attendanceEntries;

  @override
  void initState() {
    super.initState();
    _attendanceEntries = fetchAttendance();
  }

  Future<List<AttendanceEntry>> fetchAttendance() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final userToken = userModel.token;

    try {
      final attendanceEntries = await AttendanceService.fetchAttendanceDetails(
        trainNumber: widget.trainNumber,
        date: DateFormat('yyyy-MM-dd').format(DateTime.parse(widget.date)),
        stationCode: widget.stationCode,
        token: userToken,
      );

      return Future.wait(attendanceEntries!.map((data) async {
        final stationInfo = await LocationService.getNearestStation(
          data.latitude!,
          data.longitude!,
        );
        return AttendanceEntry(
          id: data.id,
          username: data.username,
          trainNumber: widget.trainNumber,
          originDate: data.originDate,
          imageUrl: data.imageUrl,
          stationCode: stationInfo["station_code"],
          coachNo: data.coachNo,
          latitude: data.latitude ?? '',
          longitude: data.longitude ?? '',
          updatedAt: data.updatedAt ?? '',
          updatedBy: data.updatedBy ?? '',
          nearestStationCode: stationInfo["station_name"],
          distance: stationInfo["distance_km"].toString(),
        );
      }).toList());
    } catch (e) {
      print('Error fetching attendance: $e');
      return [];
    }
  }

  String _formatDate(String? date) {
    if (date == null) return "N/A";
    try {
      final parsedDate = DateTime.parse(date).toLocal();
      return DateFormat.yMMMd().add_jm().format(parsedDate);
    } catch (e) {
      return date;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
          title: AppLocalizations.of(context).attendance_details_title),
      drawer: const CustomDrawer(),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Card(
              elevation: 5,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDetailRow(
                        AppLocalizations.of(context)
                            .attendance_details_train_number,
                        widget.trainNumber),
                    _buildDetailRow(
                        AppLocalizations.of(context)
                            .attendance_details_journey_date,
                        widget.date),
                    _buildDetailRow(
                        AppLocalizations.of(context)
                            .attendance_details_station_code,
                        widget.stationCode),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: FutureBuilder<List<AttendanceEntry>>(
                future: _attendanceEntries,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  if (!snapshot.hasData || snapshot.data!.isEmpty) {
                    return const Center(child: Text('No attendance found.'));
                  }
                  if (snapshot.hasError) {
                    return Center(child: Text('Error: ${snapshot.error}'));
                  }
                  if (snapshot.hasData) {
                    final entries = snapshot.data!;
                    return ListView.builder(
                      itemCount: entries.length,
                      itemBuilder: (context, index) {
                        final entry = entries[index];
                        return InkWell(
                          onTap: () {
                            AttendanceEntry detailPageEntry = entry;
                            ImageResponse detailPageImageResponse =
                                ImageResponse.fromAttendenceEntery(
                                    detailPageEntry);
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) {
                                  return ImageDetailPage(
                                      imageResponse: detailPageImageResponse);
                                },
                              ),
                            );
                          },
                          child: Card(
                            margin: const EdgeInsets.only(bottom: 16.0),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            elevation: 3,
                            child: Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Center(
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Image.network(
                                        entry.imageUrl,
                                        width: 120,
                                        height: 130,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                            "Username: ${entry.username.length > 22 ? entry.username.substring(0, entry.username.length - 22) : entry.username}",
                                            style: const TextStyle(
                                                fontWeight: FontWeight.bold)),
                                        Text("latitude: ${entry.latitude}",
                                            style:
                                                const TextStyle(fontSize: 12)),
                                        Text("longitude: ${entry.longitude}",
                                            style:
                                                const TextStyle(fontSize: 12)),
                                        Text(
                                            "nearest Station: ${entry.stationCode}",
                                            style:
                                                const TextStyle(fontSize: 12)),
                                        Text("Distance: ${entry.distance} km",
                                            style:
                                                const TextStyle(fontSize: 12)),
                                        Text("Updated By: ${entry.updatedBy}",
                                            style:
                                                const TextStyle(fontSize: 12)),
                                        Text(
                                            "Updated At: ${_formatDate(entry.updatedAt)}",
                                            style:
                                                const TextStyle(fontSize: 12)),
                                        Text(
                                            "Match Percentage: ${entry.matchPercentage ?? 'No human detected'}",
                                            style:
                                                const TextStyle(fontSize: 12)),
                                        Text(
                                          "Status: ${entry.is_real_human == true ? 'Marked' : 'Pending'}",
                                          style: const TextStyle(fontSize: 12),
                                        ),
                                        const SizedBox(height: 10),
                                        DeleteButton(
                                          date: entry.originDate,
                                          stationCode: widget.stationCode,
                                          trainNumber: widget.trainNumber,
                                          username: entry.username,
                                          onDeleteSuccess: () {
                                            setState(() {
                                              _attendanceEntries =
                                                  fetchAttendance();
                                            });
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    );
                  }
                  return const Center(child: Text('No data available.'));
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String title, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 16),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
