
// import 'dart:async';
// import 'dart:convert';
// import 'package:http/http.dart' as http;
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:geolocator/geolocator.dart';
// import 'package:intl/intl.dart';
// import 'package:dropdown_search/dropdown_search.dart';
// import 'package:latlong2/latlong.dart';
// import 'package:provider/provider.dart';
// import 'package:railops/main.dart';
// import 'package:railops/models/user_model.dart';
// import 'package:railops/screens/attendance/attendance_details.dart';
// import 'package:railops/screens/attendance/image_upload.dart';
// import 'package:railops/screens/attendance/widget/station_details_popup.dart';
// import 'package:railops/screens/profile_screen/widgets/change_email_modal.dart';
// import 'package:railops/services/assign_ehk_ca_services/return_gap_services.dart';
// import 'package:railops/services/attendance_services/attendance_sevices.dart';
// import 'package:railops/services/attendance_services/location_service.dart';
// import 'package:railops/services/edit_train_services/edit_train_services.dart';
// import 'package:railops/services/location_services/location_services.dart';
// import 'package:railops/services/profile_services/profile_services.dart';
// import 'package:railops/services/profile_services/profile_train_services.dart';
// import 'package:railops/types/assign_ehk_ca_types/return_gap_types.dart';
// import 'package:railops/types/attendance_types/onboarding_response.dart';
// import 'package:railops/types/edit_train_types/edit_train_types.dart'
//     hide EditTrainServices;
// import 'package:railops/types/train_types/train_charting_response.dart';
// import 'package:railops/utils/permission_handler_service.dart';
// import 'package:railops/widgets/index.dart';
// import 'package:railops/services/train_services/index.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:in_app_update/in_app_update.dart';
// import 'package:path_provider/path_provider.dart';



// class StationItem extends StatelessWidget {
//   final String stationCode;
//   final String stationName;
//   final int attendanceCount;
//   final bool showStartTime;
//   final bool showEndTime;
//   final String startTime;
//   final String endTime;
//   final String selectedDate;
//   final String selectedTrain;
//   final bool isOnboardingStation;
//   final bool isInTwoHourBuffer;
//   final Map<String, List<int>>? onboardingDetails;
//   final Map<String, List<int>>? offboardingDetails;
//   final Map<String, List<int>>? vacantDetails;
//   final bool isAttendanceStation;
//   final bool isBeforeOnboarding;
//   final bool isAfterOnboarding;
//   final bool isInSixHourBuffer;
//   final bool isInThirySixHourBuffer;
//   final bool isFirstAndLastStation;
//   final bool isLastStaion;
//   final bool isFirstStaion;
//   final List<String> coachNumbers;
//   final String? arrivalTime;
//   final Map<String, List<int>>? offboardingDetailsInroute;
//   final Map<String, List<int>>? onboardingDetailsInroute;
//   final List<String>? nearbyStations;
//   final String userType;

//   const StationItem(
//       {Key? key,
//       required this.stationCode,
//       required this.stationName,
//       required this.attendanceCount,
//       this.showStartTime = false,
//       this.showEndTime = false,
//       required this.startTime,
//       required this.isInTwoHourBuffer,
//       required this.isFirstAndLastStation,
//       required this.userType,
//       required this.endTime,
//       required this.selectedDate,
//       required this.selectedTrain,
//       required this.isOnboardingStation,
//       required this.offboardingDetails,
//       required this.onboardingDetails,
//       required this.vacantDetails,
//       required this.isAttendanceStation,
//       required this.isBeforeOnboarding,
//       required this.isAfterOnboarding,
//       required this.coachNumbers,
//       required this.isInSixHourBuffer,
//       required this.isInThirySixHourBuffer,
//       required this.isLastStaion,
//       required this.isFirstStaion,
//       this.arrivalTime,
//       this.nearbyStations,
//       required this.offboardingDetailsInroute,
//       required this.onboardingDetailsInroute})
//       : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     Map<String, Map<String, int>> coachTotals = {};
//     Map<String, Map<String, int>> filteredCoachTotals = {};
//     Set<String> allCoaches = {
//       ...?onboardingDetails?.keys,
//       ...?offboardingDetails?.keys,
//       ...?vacantDetails?.keys,
//     };
//     for (String coach in allCoaches) {
//       coachTotals[coach] = {
//         'onboard': onboardingDetails?[coach]?.length ?? 0,
//         'offboard': offboardingDetails?[coach]?.length ?? 0,
//         'vacant': vacantDetails?[coach]?.length ?? 0,
//       };

//       if (coachNumbers.contains(coach)) {
//         filteredCoachTotals[coach] = coachTotals[coach]!;
//       }
//     }

//     var filteredEntries = coachTotals.entries
//         .where((entry) =>
//             entry.value['onboard']! > 0 ||
//             entry.value['offboard']! > 0 ||
//             entry.value['vacant']! > 0)
//         .toList();

//     final totalOnboard = filteredEntries
//         .map((e) => e.value['onboard']!)
//         .fold<int>(0, (sum, count) => sum + count);
//     final totalOffboard = filteredEntries
//         .map((e) => e.value['offboard']!)
//         .fold<int>(0, (sum, count) => sum + count);
//     final totalVacant = filteredEntries
//         .map((e) => e.value['vacant']!)
//         .fold<int>(0, (sum, count) => sum + count);

//     filteredEntries = coachTotals.entries
//         .where((entry) => coachNumbers.contains(entry.key))
//         .toList();

//     return Row(
//       crossAxisAlignment: CrossAxisAlignment.center,
//       children: [
//         const SizedBox(width: 10),
//         SizedBox(
//           width: 80,
//           child: Column(
//             children: [
//               if (showStartTime) _buildTimeText(startTime),
//               if (!showStartTime)
//                 _buildTimeText(
//                     arrivalTime?.isNotEmpty == true ? arrivalTime : " "),
//               if (showEndTime) _buildTimeText(endTime),
//               if (!showEndTime &&
//                   !showStartTime &&
//                   (arrivalTime == null || arrivalTime!.isEmpty))
//                 _buildTimeText("-"),
//             ],
//           ),
//         ),
//         const SizedBox(width: 10),
//         Column(
//           children: [
//             Stack(
//               alignment: showStartTime
//                   ? Alignment.topCenter
//                   : showEndTime
//                       ? Alignment.bottomCenter
//                       : Alignment.center,
//               children: [
//                 Container(
//                   width: 14,
//                   height: showStartTime || showEndTime ? 90 : 100,
//                   margin: EdgeInsets.only(
//                     top: showStartTime ? 8 : 0,
//                     bottom: showEndTime ? 8 : 0,
//                   ),
//                   decoration: BoxDecoration(
//                     color: const Color.fromARGB(255, 198, 223, 239),
//                     borderRadius: BorderRadius.only(
//                       topLeft: Radius.circular(showStartTime ? 8 : 0),
//                       topRight: Radius.circular(showStartTime ? 8 : 0),
//                       bottomLeft: Radius.circular(showEndTime ? 8 : 0),
//                       bottomRight: Radius.circular(showEndTime ? 8 : 0),
//                     ),
//                   ),
//                 ),
//                 Stack(
//                   alignment: Alignment.center,
//                   children: [
//                     Container(
//                       width: 16,
//                       height: 16,
//                       decoration: BoxDecoration(
//                         color: Colors.transparent,
//                         shape: BoxShape.circle,
//                         border: Border.all(
//                           color: Colors.green,
//                           width: 2,
//                         ),
//                       ),
//                     ),
//                     if (isOnboardingStation)
//                       Container(
//                         width: 16,
//                         height: 16,
//                         decoration: const BoxDecoration(
//                           color: Colors.green,
//                           shape: BoxShape.circle,
//                         ),
//                       ),
//                     if (!isOnboardingStation && isBeforeOnboarding)
//                       const Icon(
//                         Icons.check,
//                         size: 12,
//                         color: Colors.green,
//                       ),
//                   ],
//                 ),
//               ],
//             ),
//           ],
//         ),
//         const SizedBox(width: 30),
//         Expanded(
//           child: InkWell(
//             onTapDown: (details) async {
//               if ((isAttendanceStation &&
//                   nearbyStations!.contains(stationCode))) {
//                 if (!(isFirstAndLastStation)) {
//                   final result = await showMenu<String>(
//                     context: context,
//                     position: RelativeRect.fromLTRB(
//                       details.globalPosition.dx,
//                       details.globalPosition.dy,
//                       details.globalPosition.dx,
//                       details.globalPosition.dy,
//                     ),
//                     items: [
//                       const PopupMenuItem(
//                         value: 'self',
//                         child: Text('Self'),
//                       ),
//                       const PopupMenuItem(
//                         value: 'coach attendent',
//                         child: Text('Other CA'),
//                       ),
//                       const PopupMenuItem(
//                         value: 'OBHS',
//                         child: Text('Other EHK/OBHS'),
//                       ),
//                     ],
//                   );

//                   if (result != null) {
//                     Navigator.push(
//                       context,
//                       MaterialPageRoute(
//                         builder: (context) => ImageUploadPage(
//                             selectedOption: result == "self" ? "self" : "other",
//                             forUserType: result,
//                             trainNumber: selectedTrain,
//                             journeyDate: selectedDate,
//                             stationCode: stationCode,
//                             stationName: stationName),
//                       ),
//                     );
//                   }
//                 } else {
//                   if ((isInTwoHourBuffer ||
//                       isInSixHourBuffer ||
//                       (userType == "war room user" &&
//                           isInThirySixHourBuffer))) {
//                     final result = await showMenu<String>(
//                       context: context,
//                       position: RelativeRect.fromLTRB(
//                         details.globalPosition.dx,
//                         details.globalPosition.dy,
//                         details.globalPosition.dx,
//                         details.globalPosition.dy,
//                       ),
//                       items: [
//                         const PopupMenuItem(
//                           value: 'self',
//                           child: Text('Self'),
//                         ),
//                         const PopupMenuItem(
//                           value: 'coach attendent',
//                           child: Text('Other CA'),
//                         ),
//                         const PopupMenuItem(
//                           value: 'OBHS',
//                           child: Text('Other EHK/OBHS'),
//                         ),
//                       ],
//                     );

//                     if (result != null) {
//                       Navigator.push(
//                         context,
//                         MaterialPageRoute(
//                           builder: (context) => ImageUploadPage(
//                               selectedOption:
//                                   result == "self" ? "self" : "other",
//                               forUserType: result,
//                               trainNumber: selectedTrain,
//                               journeyDate: selectedDate,
//                               stationCode: stationCode,
//                               stationName: stationName, isMediaUploadEnabled: null,),
//                         ),
//                       );
//                     }
//                   } else {
//                     String bufferMessage = "";
//                     if ((!isInSixHourBuffer && isLastStaion) &&
//                         (isInThirySixHourBuffer && isLastStaion)) {
//                       bufferMessage =
//                           "The train, Train Number: $selectedTrain is not within 12 hour buffer after reaching the last station."
//                           "Attendance can only be marked by a war room user.";
//                     } else if (!isInTwoHourBuffer && isFirstStaion) {
//                       bufferMessage =
//                           "The train, Train Number: $selectedTrain is not within 12 hour buffer";
//                     } else if ((!isInSixHourBuffer && isLastStaion) &&
//                         (!isInThirySixHourBuffer && isLastStaion)) {
//                       bufferMessage =
//                           "Train Number: $selectedTrain has exceeded the 36-hour buffer after reaching the last station. Attendance cannot be marked.";
//                     } else {
//                       bufferMessage =
//                           "The time between the current time and the train's arrival time is not within the buffer.\n"
//                           "If you're unable to see the arrival time correctly, please contact the admin or add the arrival time for train.";
//                     }

//                     ScaffoldMessenger.of(context).showSnackBar(SnackBar(
//                       content: Text(bufferMessage),
//                       backgroundColor: const Color.fromARGB(255, 237, 162, 76),
//                       duration: const Duration(seconds: 10),
//                     ));
//                   }
//                 }
//               } else {
//                 if (!isAttendanceStation) {
//                   ScaffoldMessenger.of(context).showSnackBar(
//                     SnackBar(
//                       content: Text(
//                           "Attendance cannot be marked for station $stationCode as it is not an attendance station."),
//                       backgroundColor: const Color.fromARGB(255, 237, 162, 76),
//                       duration: const Duration(seconds: 2),
//                     ),
//                   );
//                 } else if (!nearbyStations!.contains(stationCode)) {
//                   ScaffoldMessenger.of(context).showSnackBar(
//                     SnackBar(
//                       content: Text(
//                           "You're over 50 KM away from the selected station $stationCode. Attendance can only be marked when you're within the allowed range.\nFor Now You can only mark attendance for stations: ${nearbyStations!.join(', ')}"),
//                       backgroundColor: const Color.fromARGB(255, 237, 162, 76),
//                       duration: const Duration(seconds: 2),
//                     ),
//                   );
//                 } else if (!isInTwoHourBuffer && isFirstStaion) {
//                   ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
//                     content: Text(
//                         "The time between the current time and the train's arrival time is not within the 2-hour buffer.\n"
//                         "If you're unable to see the arrival time, please contact the admin or add the arrival time for this train."),
//                     backgroundColor: Color.fromARGB(255, 237, 162, 76),
//                     duration: Duration(seconds: 2),
//                   ));
//                 } else {
//                   ScaffoldMessenger.of(context).showSnackBar(
//                     const SnackBar(
//                       content: Text(
//                           "Train Location is not fetched yet, please try again later"),
//                       backgroundColor: const Color.fromARGB(255, 237, 162, 76),
//                       duration: const Duration(seconds: 2),
//                     ),
//                   );
//                 }
//               }
//             },
//             child: Container(
//               padding: const EdgeInsets.symmetric(vertical: 12),
//               decoration: BoxDecoration(
//                 color: Colors.grey.shade200,
//                 borderRadius: BorderRadius.circular(8),
//               ),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Text(
//                     stationCode,
//                     style: TextStyle(
//                       fontSize: 14,
//                       fontWeight: FontWeight.w500,
//                       color: isOnboardingStation
//                           ? Colors.green.shade700
//                           : Colors.black,
//                     ),
//                   ),
//                   const SizedBox(height: 4),
//                   Text(
//                     stationName.length > 10
//                         ? stationName.substring(0, 11)
//                         : stationName,
//                     style: TextStyle(
//                       fontSize: 12,
//                       color: isOnboardingStation
//                           ? Colors.green.shade600
//                           : Colors.grey.shade700,
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//         GestureDetector(
//           onTap: () {
//             if (totalOnboard == 0 && totalOffboard == 0 && totalVacant == 0) {
//               ScaffoldMessenger.of(context).showSnackBar(
//                 const SnackBar(
//                   content: Text('Chart has not been prepared for this station'),
//                   backgroundColor: Colors.orange,
//                 ),
//               );
//             } else {
//               showDialog(
//                 context: context,
//                 builder: (BuildContext context) => StationDetailsPopup(
//                   stationCode: stationCode,
//                   onboardingDetails: onboardingDetails,
//                   offboardingDetails: offboardingDetails,
//                   vacantDetails: vacantDetails,
//                   coachTotals: coachTotals,
//                   filteredCoachTotals: filteredCoachTotals,
//                   coachNumbers: coachNumbers,
//                   onboardingDetailsInroute: onboardingDetailsInroute,
//                   offboardingDetailsInroute: offboardingDetailsInroute,
//                 ),
//               );
//             }
//           },
//           child: Container(
//               padding: const EdgeInsets.symmetric(horizontal: 8),
//               decoration: BoxDecoration(
//                 border: Border.all(color: Colors.transparent),
//               ),
//               child: Column(children: [
//                 Table(
//                   defaultColumnWidth: const IntrinsicColumnWidth(),
//                   border: TableBorder.all(
//                     color: Colors.white,
//                     width: 2,
//                   ),
//                   children: [
//                     TableRow(
//                       decoration: BoxDecoration(
//                         color: Colors.grey.shade100,
//                       ),
//                       children: [
//                         _buildTableCell('Total',
//                             isBold: isOnboardingStation ? true : false,
//                             backgroundColor: isOnboardingStation
//                                 ? Colors.blue.shade200
//                                 : Colors.blue.shade50,
//                             isOnboardingStation: isOnboardingStation),
//                         _buildTableCell(totalOnboard.toString(),
//                             isBold: isOnboardingStation ? true : false,
//                             backgroundColor: isOnboardingStation
//                                 ? Colors.green.shade200
//                                 : Colors.green.shade50,
//                             isOnboardingStation: isOnboardingStation),
//                         _buildTableCell(totalOffboard.toString(),
//                             isBold: isOnboardingStation ? true : false,
//                             backgroundColor: isOnboardingStation
//                                 ? const Color.fromARGB(255, 237, 162, 76)
//                                 : const Color.fromARGB(255, 252, 231, 174),
//                             isOnboardingStation: isOnboardingStation),
//                         _buildTableCell(totalVacant.toString(),
//                             isBold: isOnboardingStation ? true : false,
//                             backgroundColor: isOnboardingStation
//                                 ? Colors.grey.shade300
//                                 : Colors.grey.shade100,
//                             isOnboardingStation: isOnboardingStation),
//                       ],
//                     ),
//                     ...filteredEntries.take(3).map((entry) {
//                       return TableRow(
//                         children: [
//                           _buildTableCell(entry.key,
//                               backgroundColor: isOnboardingStation
//                                   ? Colors.blue.shade200
//                                   : Colors.blue.shade50,
//                               isOnboardingStation: isOnboardingStation),
//                           _buildTableCell(entry.value['onboard'].toString(),
//                               backgroundColor: isOnboardingStation
//                                   ? Colors.green.shade200
//                                   : Colors.green.shade50,
//                               isOnboardingStation: isOnboardingStation),
//                           _buildTableCell(entry.value['offboard'].toString(),
//                               backgroundColor: isOnboardingStation
//                                   ? const Color.fromARGB(255, 237, 162, 76)
//                                   : const Color.fromARGB(255, 252, 231, 174),
//                               isOnboardingStation: isOnboardingStation),
//                           _buildTableCell(entry.value['vacant'].toString(),
//                               backgroundColor: isOnboardingStation
//                                   ? Colors.grey.shade200
//                                   : Colors.grey.shade100,
//                               isOnboardingStation: isOnboardingStation),
//                         ],
//                       );
//                     }),
//                   ],
//                 ),
//                 if (filteredEntries.length > 3)
//                   Container(
//                     padding: const EdgeInsets.only(top: 2),
//                     child: Text(
//                       'click for more...',
//                       style: TextStyle(
//                         fontSize: 8,
//                         height: 0.5,
//                         color: Colors.grey.shade600,
//                       ),
//                     ),
//                   ),
//               ])),
//         ),
//         if (isAttendanceStation)
//           GestureDetector(
//             onTap: () {
//               Navigator.push(
//                 context,
//                 MaterialPageRoute(
//                   builder: (context) => AttendanceDetailsPage(
//                       trainNumber: selectedTrain,
//                       date: selectedDate,
//                       stationCode: stationCode),
//                 ),
//               );
//             },
//             child: Container(
//               padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
//               decoration: BoxDecoration(
//                 color: Colors.blue.shade100,
//                 borderRadius: BorderRadius.circular(12),
//               ),
//               child: Text(
//                 "A: $attendanceCount",
//                 style: const TextStyle(
//                   fontSize: 12,
//                   fontWeight: FontWeight.bold,
//                   color: Colors.black,
//                 ),
//               ),
//             ),
//           ),
//         const SizedBox(width: 20),
//       ],
//     );
//   }

//   Widget _buildTableCell(String text,
//       {bool isBold = false,
//       Color? backgroundColor,
//       bool isOnboardingStation = false}) {
//     return Container(
//       padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
//       color: backgroundColor,
//       child: Text(
//         text,
//         style: TextStyle(
//             fontSize: 10,
//             fontWeight: isBold ? FontWeight.bold : FontWeight.w500,
//             color: isOnboardingStation ? Colors.black : Colors.grey.shade900),
//         textAlign: TextAlign.center,
//       ),
//     );
//   }

  
// Widget _buildTimeText(String? time) {
//   return Text(
//     time ?? "-",
//     style: const TextStyle(
//       fontSize: 12,
//       fontWeight: FontWeight.bold,
//     ),
//     textAlign: TextAlign.right,
//   );
// }
// }