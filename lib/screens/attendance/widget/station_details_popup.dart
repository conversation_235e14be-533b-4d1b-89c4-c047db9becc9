

import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:latlong2/latlong.dart';
import 'package:provider/provider.dart';
import 'package:railops/main.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/screens/attendance/attendance_details.dart';
import 'package:railops/screens/attendance/image_upload.dart';
import 'package:railops/screens/profile_screen/widgets/change_email_modal.dart';
import 'package:railops/services/assign_ehk_ca_services/return_gap_services.dart';
import 'package:railops/services/attendance_services/attendance_sevices.dart';
import 'package:railops/services/attendance_services/location_service.dart';
import 'package:railops/services/edit_train_services/edit_train_services.dart';
import 'package:railops/services/location_services/location_services.dart';
import 'package:railops/services/profile_services/profile_services.dart';
import 'package:railops/services/profile_services/profile_train_services.dart';
import 'package:railops/types/assign_ehk_ca_types/return_gap_types.dart';
import 'package:railops/types/attendance_types/onboarding_response.dart';
import 'package:railops/types/edit_train_types/edit_train_types.dart'
    hide EditTrainServices;
import 'package:railops/types/train_types/train_charting_response.dart';
import 'package:railops/utils/permission_handler_service.dart';
import 'package:railops/widgets/index.dart';
import 'package:railops/services/train_services/index.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:path_provider/path_provider.dart';

class StationDetailsPopup extends StatefulWidget {
  final String stationCode;
  final Map<String, List<int>>? onboardingDetails;
  final Map<String, List<int>>? offboardingDetails;
  final Map<String, List<int>>? vacantDetails;
  final Map<String, Map<String, int>>? coachTotals;
  final Map<String, Map<String, int>>? filteredCoachTotals;
  final List<String> coachNumbers;
  final Map<String, List<int>>? offboardingDetailsInroute;
  final Map<String, List<int>>? onboardingDetailsInroute;

  const StationDetailsPopup(
      {Key? key,
      required this.stationCode,
      this.onboardingDetails,
      this.offboardingDetails,
      this.vacantDetails,
      this.coachTotals,
      this.filteredCoachTotals,
      required this.coachNumbers,
      this.offboardingDetailsInroute,
      this.onboardingDetailsInroute})
      : super(key: key);

  @override
  State<StationDetailsPopup> createState() => _StationDetailsPopupState();
}

class _StationDetailsPopupState extends State<StationDetailsPopup> {
  bool showSleeperCoaches = false;

  bool isSleeperCoach(String coach) {
    return coach.toLowerCase().startsWith('s');
  }

  Map<String, List<int>> filterSleeperCoaches(
      Map<String, List<int>> details, bool wantSleeper) {
    return Map.fromEntries(
      details.entries
          .where((entry) => isSleeperCoach(entry.key) == wantSleeper),
    );
  }

  Map<String, List<int>> _filterDetailsByCoaches(Map<String, List<int>> details,
      List<String> coachNumbers, bool includeSleeper) {
    return Map.fromEntries(
      details.entries.where((entry) =>
          coachNumbers.contains(entry.key) &&
          isSleeperCoach(entry.key) == includeSleeper),
    );
  }

  Map<String, List<int>> _excludeDetailsByCoaches(
      Map<String, List<int>> details,
      List<String> coachNumbers,
      bool includeSleeper) {
    return Map.fromEntries(
      details.entries.where((entry) =>
          !coachNumbers.contains(entry.key) &&
          isSleeperCoach(entry.key) == includeSleeper),
    );
  }

  bool isInrouteBerth(String coach, int berth) {
    final onboardingInroute = widget.onboardingDetailsInroute ?? {};
    final offboardingInroute = widget.offboardingDetailsInroute ?? {};

    return (onboardingInroute.containsKey(coach) &&
            onboardingInroute[coach]!.contains(berth)) ||
        (offboardingInroute.containsKey(coach) &&
            offboardingInroute[coach]!.contains(berth));
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                      vertical: 0.0, horizontal: 0.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const SizedBox(width: 40),
                      Expanded(
                        child: Text(
                          'Station ${widget.stationCode} Details',
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                ),
                const Divider(),
                TextButton(
                  onPressed: () {
                    setState(() {
                      showSleeperCoaches = !showSleeperCoaches;
                    });
                  },
                  child: Text(
                    showSleeperCoaches
                        ? 'Back to AC Coaches'
                        : 'Show Sleeper Coaches',
                    style: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.w600),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 0.0),
                  child: Text(
                    showSleeperCoaches ? 'Sleeper Coaches' : 'AC coaches',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Flexible(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSection(
                      'Onboarding Details (Your Coaches)',
                      Colors.green,
                      _filterDetailsByCoaches(widget.onboardingDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.filteredCoachTotals,
                    ),
                    _buildSection(
                      'Onboarding Details (All Coaches)',
                      Colors.green,
                      _excludeDetailsByCoaches(widget.onboardingDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.coachTotals,
                    ),
                    _buildSection(
                      'Offboarding Details (Your Coaches)',
                      const Color.fromARGB(255, 237, 162, 76),
                      _filterDetailsByCoaches(widget.offboardingDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.filteredCoachTotals,
                    ),
                    _buildSection(
                      'Offboarding Details (All Coaches)',
                      const Color.fromARGB(255, 237, 162, 76),
                      _excludeDetailsByCoaches(widget.offboardingDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.coachTotals,
                    ),
                    _buildSection(
                      'Vacant Details (Your Coaches)',
                      Colors.grey.shade600,
                      _filterDetailsByCoaches(widget.vacantDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.filteredCoachTotals,
                    ),
                    _buildSection(
                      'Vacant Details (All Coaches)',
                      Colors.blue,
                      _excludeDetailsByCoaches(widget.vacantDetails ?? {},
                          widget.coachNumbers, showSleeperCoaches),
                      widget.coachTotals,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, Color color,
      Map<String, List<int>> details, Map<String, Map<String, int>>? totals) {
    if (details.isEmpty) return const SizedBox.shrink();

    Color backgroundColor =
        HSLColor.fromColor(color).withLightness(0.95).toColor();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: color,
          ),
        ),
        const SizedBox(height: 8),
        _buildDetailsTable(details, totals, backgroundColor),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildDetailsTable(
    Map<String, List<int>> details,
    Map<String, Map<String, int>>? coachTotals,
    Color backgroundColor,
  ) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: backgroundColor,
      ),
      child: Table(
        columnWidths: const {
          0: IntrinsicColumnWidth(),
          1: FlexColumnWidth(),
        },
        border: TableBorder.all(
          color: Colors.grey.shade300,
          borderRadius: BorderRadius.circular(8),
        ),
        children: [
          TableRow(
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
            ),
            children: [
              _buildTableCell('Coach', backgroundColor, isHeader: true),
              _buildTableCell('Berths', backgroundColor, isHeader: true),
            ],
          ),
          ...details.entries
              .where((entry) => entry.value.isNotEmpty)
              .map((entry) => _buildDataRow(entry, coachTotals))
              .toList(),
        ],
      ),
    );
  }
  
  
    TableRow _buildDataRow(MapEntry<String, List<int>> entry,
      Map<String, Map<String, int>>? coachTotals) {
    final berthCount = entry.value.length;

    if (berthCount > 18) {
      return TableRow(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(entry.key,
                    style: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.bold)),
                const SizedBox(height: 4),
                Text('${coachTotals?[entry.key]?['onboard'] ?? 0}',
                    style: const TextStyle(color: Colors.green)),
                Text('${coachTotals?[entry.key]?['offboard'] ?? 0}',
                    style: const TextStyle(
                        color: Color.fromARGB(255, 237, 162, 76))),
                Text('${coachTotals?[entry.key]?['vacant'] ?? 0}',
                    style: TextStyle(color: Colors.grey.shade600)),
              ],
            ),
          ),
          _buildBerthsList(entry.key, entry.value),
        ],
      );
    }

    return TableRow(
      children: [
        _buildCoachCell(entry.key, coachTotals),
        _buildBerthsList(entry.key, entry.value),
      ],
    );

    
  }
  
  

  Widget _buildTableCell(String text, backgroundColor,
      {bool isHeader = false}) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
      ),
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
      alignment: Alignment.center,
      child: Text(
        text,
        style: TextStyle(
          fontWeight: isHeader ? FontWeight.bold : FontWeight.normal,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildCoachCell(
      String coach, Map<String, Map<String, int>>? coachTotals) {
    return Container(
      padding: const EdgeInsets.all(8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(coach,
              style:
                  const TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
          Row(
            children: [
              Text('${coachTotals?[coach]?['onboard'] ?? 0} - ',
                  style: const TextStyle(color: Colors.green, fontSize: 13)),
              Text('${coachTotals?[coach]?['offboard'] ?? 0} - ',
                  style: const TextStyle(
                      color: Color.fromARGB(255, 237, 162, 76), fontSize: 13)),
              Text('${coachTotals?[coach]?['vacant'] ?? 0}',
                  style: const TextStyle(color: Colors.grey, fontSize: 13)),
            ],
          )
        ],
      ),
    );
  }

  Widget _buildBerthsList(String coach, List<int> berths) {
    return Container(
      padding: const EdgeInsets.all(8),
      alignment: Alignment.center,
      child: Wrap(
        alignment: WrapAlignment.center,
        spacing: 4,
        runSpacing: 4,
        children: berths.map((berth) {
          final isInroute = isInrouteBerth(coach, berth);
          return Text(
            isInroute ? "(${berth})," : "${berth},",
            style: TextStyle(
              fontSize: 13,
              fontWeight: isInroute ? FontWeight.bold : FontWeight.normal,
            ),
          );
        }).toList(),
      ),
    );
  }}