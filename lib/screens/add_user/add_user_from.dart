import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/routes.dart';
import 'package:railops/screens/add_user/widget/new_user_email_field.dart';
import 'package:railops/screens/add_user/widget/new_user_mobile_field.dart';
import 'package:railops/screens/user_screen/widgets/index.dart';
import 'package:railops/screens/user_screen/widgets/signup_page/zone_dropdown.dart';
import 'package:railops/services/authentication_services/auth_service.dart';
import 'package:railops/services/train_services/train_service_signup.dart';
import 'package:railops/types/train_types/zone_division_type.dart';
import 'package:railops/widgets/index.dart';
import '../user_screen/widgets/signup_page/signup_error.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class AddUserFrom extends StatefulWidget {
  const AddUserFrom({super.key});

  @override
  _AddUserFromState createState() => _AddUserFromState();
}

class _AddUserFromState extends State<AddUserFrom> {
  final _formKey = GlobalKey<FormState>();
  String? usertype;
  String? userName;
  bool hasAdminPermissions = false;

  // Form field values
  late String _mobileNumber = '',
      _firstName = '',
      _middleName = '',
      _lastName = '',
      _email = '',
      _password = '',
      _empNumber = '';
  late String _whatsappNumber = '';
  late String _secondaryPhoneNumber = '';
  late String _division = '',
      _depot = '',
      _role = 'coach attendent',
      _selectedZones = '';

  List<String> depots = [];
  List<String> trainList = [];
  List<String> empNumberList = [];
  List<String> coachList = [];
  final List<String> _trainNumber = [];
  final List<String> _coachNumber = [];
  List<String> divisionCodes = [];

  // Form completion flags
  bool _isLoading = false;
  bool _isEmailVerified = false;
  bool _isPhoneVerified = false;
  bool _isWhatsappVerified = false;
  bool _wasClicked = false;
  bool _sameAsPhone = false;

  // Progressive field enabling flags
  bool _isFirstNameValid = false;
  bool _isLastNameValid = false;
  bool _isEmailEditable = false;
  bool _isPhoneEditable = false;
  bool _isWhatsappEditable = false;
  bool _isPasswordEditable = false;
  bool _isRoleEditable = false;
  bool _isStaffFieldsEditable = false;
  bool _isSubmitButtonEnabled = false;
  bool _isEmailFieldVisible = true;

  // Field controllers
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _middleNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _whatsappController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _empNumberController = TextEditingController();

  bool get _isPassenger => _role.toLowerCase() == 'passenger';

  @override
  void initState() {
    super.initState();
    final userModel = Provider.of<UserModel>(context, listen: false);
    List<String> adminUsers = ['s2 admin', 'railway admin', 'contractor admin'];

    setState(() {
      usertype = userModel.userType;
      userName = userModel.userName;
      hasAdminPermissions = adminUsers.contains(usertype);
    });

    // Add listeners to controllers for progressive validation
    _firstNameController.addListener(_validateFirstName);
    _lastNameController.addListener(_validateLastName);

    getDepot();
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _middleNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _whatsappController.dispose();
    _passwordController.dispose();
    _empNumberController.dispose();
    super.dispose();
  }

  // Progressive validation methods
  void _validateFirstName() {
    final value = _firstNameController.text;
    setState(() {
      _isFirstNameValid = value.isNotEmpty;
      _firstName = value;
    });
  }

  void _validateLastName() {
    final value = _lastNameController.text;
    setState(() {
      _isLastNameValid = value.isNotEmpty;
      _lastName = value;
      // Enable email when both first and last name are valid
      _isEmailEditable = _isFirstNameValid && _isLastNameValid;
    });
  }

  void _validateEmail(bool isVerified) {
    setState(() {
      if (isVerified) {
        _isPhoneEditable = true; // Enable phone field when email is verified
      }
    });
  }

  void _validatePhone(bool isVerified) {
    setState(() {
      _isPhoneVerified = isVerified;
      if (isVerified) {
        _isWhatsappEditable = true;
      }
    });
  }

  void _validateWhatsapp() {
    setState(() {
      if (_sameAsPhone) {
        _whatsappNumber = _mobileNumber;
        _isWhatsappVerified = true;
      } else if (_whatsappNumber.length == 10 &&
          RegExp(r'^[0-9]+$').hasMatch(_whatsappNumber)) {
        _isWhatsappVerified = true;
      } else {
        _isWhatsappVerified = false;
      }

      if (_isWhatsappVerified) {
        _isPasswordEditable = true;
      }
    });
  }

  void _validatePassword(String value) {
    setState(() {
      _password = value;
      if (_password.isNotEmpty && _password.length >= 6) {
        _isRoleEditable = true;
      } else {
        _isRoleEditable = false;
      }
    });
  }

  void _handleZoneSelection(String selectedZones) async {
    if (mounted) {
      setState(() {
        _selectedZones = selectedZones;
      });

      if (_selectedZones.isNotEmpty) {
        try {
          List<ZoneDivision> divisions =
              await TrainServiceSignup.getDivisions(_selectedZones);

          List<String> divisionCodesList =
              divisions.map((division) => division.code).toList();

          if (mounted) {
            setState(() {
              divisionCodes = divisionCodesList;
            });
          }
        } catch (e) {
          debugPrint("Error fetching divisions: $e");
        }
      }
    }
  }

  Future getDepot() async {
    try {
      final getData = await TrainServiceSignup.getDepot(_division);
      if (mounted) {
        setState(() {
          depots = getData;
        });
      }
    } catch (e) {
      if (e is StateError && e.toString().contains('mounted')) {
        debugPrint('Widget disposed before operation completes');
      } else {
        debugPrint('Get Depot Failed : $e');
      }
    }
  }

  Future getTrainList() async {
    try {
      final getData = await TrainServiceSignup.getTrainList(_depot);
      if (mounted) {
        setState(() {
          trainList = getData['trains'];
          empNumberList = getData['emp_numbers'];
        });
      }
    } catch (e) {
      if (e is StateError && e.toString().contains('mounted')) {
        debugPrint('Widget disposed before operation completes');
      } else {
        debugPrint('Get Train List Failed : $e');
      }
    }
  }

  Future getCoachList() async {
    try {
      final getData =
          await TrainServiceSignup.getCoaches(_trainNumber.join(','));
      if (mounted) {
        setState(() {
          coachList = getData;
        });
      }
    } catch (e) {
      if (e is StateError && e.toString().contains('mounted')) {
        debugPrint('Widget disposed before operation completes');
      } else {
        debugPrint('Get Coach List Failed : $e');
      }
    }
  }

  void _submitForm() async {
    if (_formKey.currentState?.validate() ?? false) {
      _formKey.currentState?.save();

      if (_mobileNumber.trim() == _secondaryPhoneNumber.trim()) {
        showErrorModal(
          context,
          AppLocalizations.of(context)
              .text_phone_and_secondary_phone_must_be_different,
          AppLocalizations.of(context).text_validation_error,
          () {},
        );
        return;
      }
      setState(() {
        _isLoading = true;
      });
      loader(context,
          AppLocalizations.of(context).text_submitting_data_please_wait);

      try {
        String response;
        final submitZones = _isPassenger ? '' : _selectedZones;
        final submitDivision = _isPassenger ? '' : _division;
        final submitDepot = _isPassenger ? '' : _depot;
        final submitEmpNumber = _isPassenger ? '' : _empNumberController.text;

        bool isAdminType = usertype == "railway admin" ||
            usertype == "s2 admin" ||
            usertype == "contract admin";

        if (!isAdminType) {
          response = await AuthService.signup(
            _mobileNumber,
            _firstName,
            _middleName,
            _lastName,
            _email,
            _password,
            _password, // Using same password for both fields
            _role,
            submitDivision,
            _trainNumber.join(','),
            _coachNumber.join(','),
            submitDepot,
            submitEmpNumber,
            submitZones,
            _whatsappNumber,
            _secondaryPhoneNumber,
          );
        } else {
          response = await AuthService.addNewUser(
            _mobileNumber,
            _firstName,
            _middleName,
            _lastName,
            _email,
            _password,
            _password, // Using same password for both fields
            _role,
            submitDivision,
            _trainNumber.join(','),
            _coachNumber.join(','),
            submitDepot,
            submitEmpNumber,
            submitZones,
            _whatsappNumber,
            _secondaryPhoneNumber,
          );
        }

        if (mounted) {
          Navigator.of(context).pop();
          showSuccessModal(
              context, response, AppLocalizations.of(context).text_success, () {
            Navigator.pushNamed(context, Routes.addUser);
          });
        }
      } catch (e) {
        if (mounted) {
          Navigator.of(context).pop();
          if (e is SignupDetailsException) {
            showErrorDialog(context, e.details);
          } else {
            showErrorModal(context, "Error: $e",
                "${AppLocalizations.of(context).text_error}", () {});
          }
        }
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    } else {
      showErrorModal(
          context,
          AppLocalizations.of(context).text_please_complete_all_required_fields,
          AppLocalizations.of(context).text_form_incomplete,
          () {});
    }
  }

  Widget _buildNameFields() {
    return Column(
      children: [
        TextFormField(
          controller: _firstNameController,
          decoration: InputDecoration(
            labelText: AppLocalizations.of(context).text_first_name,
            hintText: AppLocalizations.of(context).text_enter_first_name,
            border: const OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return AppLocalizations.of(context).text_please_enter_first_name;
            }
            return null;
          },
        ),
        const SizedBox(height: 20),
        TextField(
          controller: _middleNameController,
          decoration: InputDecoration(
            labelText: AppLocalizations.of(context).text_middle_name_optional,
            hintText: AppLocalizations.of(context).text_enter_middle_name,
            border: const OutlineInputBorder(),
          ),
          onChanged: (value) {
            _middleName = value;
          },
        ),
        const SizedBox(height: 20),
        TextFormField(
          controller: _lastNameController,
          decoration: InputDecoration(
            labelText: AppLocalizations.of(context).text_last_name,
            hintText: AppLocalizations.of(context).text_enter_last_name,
            border: const OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return AppLocalizations.of(context).text_please_enter_last_name;
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildEmailField() {
    if (_email == '<EMAIL>') {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        const SizedBox(height: 20),
        Opacity(
          opacity: _isEmailEditable ? 1.0 : 0.6,
          child: AbsorbPointer(
            absorbing: !_isEmailEditable,
            child: NewEmailField(
              onSaved: (value) {
                _email = value;
                _validateEmail(true); // For admin form, auto-verify email
              },
            ),
          ),
        ),
        const SizedBox(height: 10),
        OutlinedButton(
          onPressed: _isEmailEditable
              ? () {
                  setState(() {
                    _email = '<EMAIL>';
                    _isEmailVerified = true;
                    _isPhoneEditable = true;
                    _isEmailFieldVisible = false;
                  });
                }
              : null,
          child: Text(AppLocalizations.of(context).text_i_dont_have),
        ),
      ],
    );
  }

  Widget _buildPhoneField() {
    return Column(
      children: [
        const SizedBox(height: 20),
        Opacity(
          opacity: _isPhoneEditable ? 1.0 : 0.6,
          child: AbsorbPointer(
            absorbing: !_isPhoneEditable,
            child: NewMobileNumberField(
              labelText: AppLocalizations.of(context).text_phone_number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(10),
              ],
              onSaved: (value) {
                _mobileNumber = value;
                _validatePhone(true);
                if (_sameAsPhone) {
                  _whatsappNumber = value;
                }
              },
              onChanged: (value) {
                _mobileNumber = value;
                _validatePhone(true);
                if (_sameAsPhone) {
                  _whatsappNumber = value;
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSecondaryPhone() {
    return Column(
      children: [
        const SizedBox(height: 20),
        Opacity(
          opacity: _isPhoneEditable ? 1.0 : 0.6,
          child: AbsorbPointer(
            absorbing: !_isPhoneEditable,
            child: NewMobileNumberField(
              labelText: AppLocalizations.of(context)
                  .text_secondary_phone_number_optional,
              isOptional: true,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(10),
              ],
              onSaved: (value) {
                _secondaryPhoneNumber = value;
              },
              onChanged: (value) {
                _secondaryPhoneNumber = value;
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildWhatsAppFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        const SizedBox(height: 20),
        OutlinedButton(
          onPressed: _isPhoneVerified
              ? () {
                  setState(() {
                    _sameAsPhone = !_sameAsPhone;
                    if (_sameAsPhone) {
                      _whatsappNumber = _mobileNumber;
                      _isWhatsappVerified = true;
                      _isPasswordEditable = true;
                    } else {
                      _whatsappNumber = '';
                      _isWhatsappVerified = false;
                    }
                    _wasClicked = true;
                  });
                }
              : null,
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 15),
            side:
                BorderSide(color: _isPhoneVerified ? Colors.blue : Colors.grey),
          ),
          child: Text(
            _sameAsPhone
                ? AppLocalizations.of(context)
                    .text_whatsapp_number_same_as_phone
                : AppLocalizations.of(context)
                    .text_use_same_number_for_whatsapp,
            style: TextStyle(
              fontSize: 14.0,
              color: _isPhoneVerified ? Colors.blue : Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        if (_sameAsPhone)
          Padding(
            padding: const EdgeInsets.only(top: 15),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _mobileNumber,
                    style: const TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
          ),
        if (!_sameAsPhone && _wasClicked)
          Padding(
            padding: const EdgeInsets.only(top: 15),
            child: TextFormField(
              keyboardType: TextInputType.number,
              maxLength: 10,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context).text_whatsapp_number,
                hintText: AppLocalizations.of(context)
                    .text_enter_10_digit_whatsapp_number,
                border: const OutlineInputBorder(),
                counterText: '',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return AppLocalizations.of(context)
                      .text_please_enter_whatsapp_number;
                }
                if (value.length != 10) {
                  return AppLocalizations.of(context)
                      .text_whatsapp_number_must_be_10_digits;
                }
                if (!RegExp(r'^[0-9]+$').hasMatch(value)) {
                  return AppLocalizations.of(context)
                      .text_please_enter_only_numbers;
                }
                return null;
              },
              onSaved: (value) {
                _whatsappNumber = value ?? '';
              },
              onChanged: (value) {
                setState(() {
                  _whatsappNumber = value;
                  _isWhatsappVerified =
                      value.length == 10 && RegExp(r'^[0-9]+$').hasMatch(value);
                  if (_isWhatsappVerified) {
                    _isPasswordEditable = true;
                  }
                });
              },
            ),
          ),
      ],
    );
  }

  Widget _buildPasswordField() {
    return Column(
      children: [
        const SizedBox(height: 20),
        Opacity(
          opacity: _isPasswordEditable ? 1.0 : 0.6,
          child: AbsorbPointer(
            absorbing: !_isPasswordEditable,
            child: PasswordField(
              onSaved: (value) {
                _password = value;
                _validatePassword(value);
              },
              onChanged: (value) {
                _validatePassword(value);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRoleField() {
    return Column(
      children: [
        const SizedBox(height: 20),
        Opacity(
          opacity: _isRoleEditable ? 1.0 : 0.6,
          child: AbsorbPointer(
            absorbing: !_isRoleEditable,
            child: RolesDropdown(
              onSaved: (value) {
                setState(() {
                  _role = value;
                  if (!_isPassenger) {
                    _isStaffFieldsEditable = true;
                  } else {
                    _isStaffFieldsEditable = false;
                    _isSubmitButtonEnabled = true;
                  }
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStaffFields() {
    return Column(
      children: [
        const SizedBox(height: 20),
        Opacity(
          opacity: _isStaffFieldsEditable ? 1.0 : 0.6,
          child: AbsorbPointer(
            absorbing: !_isStaffFieldsEditable,
            child: Column(
              children: [
                // First row with Zone dropdown
                Row(
                  children: [
                    Expanded(
                      child: ZoneDropdown(
                        onSaved: (value) {
                          _handleZoneSelection(value);
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // Second row with Division and Depot
                Row(
                  children: [
                    Expanded(
                      child: DivisionDropdown(
                        divisions: divisionCodes
                          ..sort((a, b) =>
                              a.toLowerCase().compareTo(b.toLowerCase())),
                        onSaved: (value) {
                          if (mounted) {
                            setState(() {
                              _division = value;
                              _depot = '';
                            });
                            getDepot();
                          }
                        },
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: DepotDropdown(
                        depots: depots
                          ..sort((a, b) =>
                              a.toLowerCase().compareTo(b.toLowerCase())),
                        onSaved: (value) {
                          if (mounted) {
                            setState(() {
                              _depot = value;
                              _isSubmitButtonEnabled = true;
                            });
                            getTrainList();
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 20),
        Opacity(
          opacity: _isStaffFieldsEditable ? 1.0 : 0.6,
          child: AbsorbPointer(
            absorbing: !_isStaffFieldsEditable,
            child: EmpNumberField(
              onSaved: (value) {
                if (mounted) {
                  setState(() {
                    _isSubmitButtonEnabled = true;
                  });
                }
              },
              userType: _role,
              empNumberList: empNumberList,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    double screenWidth = MediaQuery.of(context).size.width;
    double buttonWidth = screenWidth * 0.9;

    return Column(
      children: [
        const SizedBox(height: 20),
        // Separate rows to prevent overflow
        Opacity(
          opacity: _isSubmitButtonEnabled ? 1.0 : 0.6,
          child: SizedBox(
            width: buttonWidth,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 50),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              onPressed: _isSubmitButtonEnabled ? _submitForm : null,
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : Text(
                      hasAdminPermissions
                          ? AppLocalizations.of(context).text_submit
                          : AppLocalizations.of(context)
                              .text_request_for_add_user,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        // Info button in separate row
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              icon: const Icon(Icons.info_outline, color: Colors.blue),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: Text(AppLocalizations.of(context)
                        .text_information_dialog_title),
                    content: Text(
                      AppLocalizations.of(context)
                          .text_please_complete_fields_in_order,
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(AppLocalizations.of(context).text_ok,
                            style:
                                const TextStyle(fontWeight: FontWeight.bold)),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Card(
          color: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Text(
                  AppLocalizations.of(context).text_add_new_user,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 4, bottom: 12),
                        child: Text(
                          AppLocalizations.of(context)
                              .text_personal_information,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ),
                      _buildNameFields(),
                      const SizedBox(height: 24),
                      const Divider(),
                      const SizedBox(height: 24),
                      Padding(
                        padding: const EdgeInsets.only(left: 4, bottom: 12),
                        child: Text(
                          AppLocalizations.of(context).text_contact_details,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ),
                      _buildEmailField(),
                      _buildPhoneField(),
                      _buildWhatsAppFields(),
                      _buildSecondaryPhone(),
                      const SizedBox(height: 24),
                      const Divider(),
                      const SizedBox(height: 24),
                      Padding(
                        padding: const EdgeInsets.only(left: 4, bottom: 12),
                        child: Text(
                          AppLocalizations.of(context).text_account_settings,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                      ),
                      _buildPasswordField(),
                      _buildRoleField(),
                      if (!_isPassenger || !_isRoleEditable)
                        _buildStaffFields(),
                      _buildSubmitButton(),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
