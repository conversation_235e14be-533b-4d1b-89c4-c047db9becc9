import 'package:flutter/material.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:railops/services/train_services/train_details_services.dart';
import 'package:intl/intl.dart';

class AssignObhsFilters extends StatefulWidget {
  final void Function(String? trainNumber, String? date) onSubmit;

  const AssignObhsFilters({Key? key, required this.onSubmit}) : super(key: key);

  @override
  _AssignObhsFiltersState createState() => _AssignObhsFiltersState();
}

class _AssignObhsFiltersState extends State<AssignObhsFilters> {
  List<String> _trainNumbers = [];
  String? _selectedTrainNumber;
  String? _trainName;
  bool loading = false;
  DateTime? _selectedDate = DateTime.now();
  final TextEditingController _trainNameController = TextEditingController();
  final TextEditingController _dateController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fetchTrainNumbers();
    _setInitialTrainNoFromUserModel();
    _updateDateController();
  }

  void _setInitialTrainNoFromUserModel() {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final initialTrainNo = userModel.trainNo;
    final initialSelectedDate = userModel.selectedDate;
    if (initialSelectedDate.isNotEmpty) {
      setState(() {
        _selectedDate = DateTime.parse(initialSelectedDate);
      });
      _updateDateController();
    }
    if (initialTrainNo.isNotEmpty) {
      _onTrainNumberChanged(initialTrainNo);
    }
  }

  @override
  void dispose() {
    _trainNameController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  Future<void> _fetchTrainNumbers() async {
    try {
      setState(() {
        loading = true;
      });
      _trainNumbers = await TrainService.getTrainNumbers();
      setState(() {
        loading = false;
      });
    } catch (e) {
      print('Error fetching train numbers: $e');
      setState(() {
        loading = false;
      });
    }
  }

  void _onTrainNumberChanged(String? trainNumber) async {
    if (trainNumber != null) {
      final trainName = await fetchTrainName(trainNumber);
      setState(() {
        _selectedTrainNumber = trainNumber;
        _trainName = trainName;
        _trainNameController.text = trainName ?? '';
      });
      Provider.of<UserModel>(context, listen: false).setTrainNo(trainNumber);
    }
  }

  Future<String?> fetchTrainName(String trainNumber) async {
    return await TrainService.getTrainName(trainNumber);
  }

  // Custom date formatter function
  String _formatDateToCustomFormat(DateTime date) {
    final day = date.day.toString().padLeft(2, '0');
    final month =
        DateFormat('MMM').format(date); // Short month name (Jan, Feb, etc.)
    final year = date.year.toString();
    return '$day-$month-$year';
  }

  void _updateDateController() {
    if (_selectedDate != null) {
      _dateController.text = _formatDateToCustomFormat(_selectedDate!);
    }
  }

  void _submitForm() {
    if (_selectedTrainNumber != null && _selectedDate != null) {
      final formattedDate =
          "${_selectedDate!.year}-${_selectedDate!.month.toString().padLeft(2, '0')}-${_selectedDate!.day.toString().padLeft(2, '0')}";
      widget.onSubmit(_selectedTrainNumber, formattedDate);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
            content: Text('Please select all fields before submitting.')),
      );
    }
  }

  Future<void> _refreshPage() async {
    await _fetchTrainNumbers();
    setState(() {
      _selectedTrainNumber = null;
      _trainName = null;
      _selectedDate = DateTime.now();
      _trainNameController.clear();
      _updateDateController();
    });
  }

  Future<void> _selectDate() async {
    final DateTime today = DateTime.now();
    DateTime tempSelectedDate = _selectedDate ?? today;

    bool isEditingDate = false;
    TextEditingController _dateController = TextEditingController();

    DateTime? selectedDate = await showDialog<DateTime>(
      context: context,
      builder: (BuildContext context) {
        _dateController.text = DateFormat('MMM d').format(tempSelectedDate);
        return StatefulBuilder(
          builder: (context, setStateDialog) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme:const ColorScheme.light(
                  primary: Colors.blue,
                  onPrimary: Colors.white,
                  onSurface: Colors.black,
                ),
              ),
              child: AlertDialog(
                contentPadding: EdgeInsets.zero,
                content: Container(
                  width: 320,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header with selected date
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Select date',
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                            children: [
                              isEditingDate
                                  ? Expanded(
                                      child: TextField(
                                        controller: _dateController,
                                        autofocus: true,
                                        style: const TextStyle(fontSize: 28),
                                        decoration: const InputDecoration(
                                          hintText: 'e.g. Jun 27',
                                          border: InputBorder.none,
                                        ),
                                        onChanged: (value) {
                                          try {
                                            final input = value.trim().toLowerCase();
                                            final normalizedInput = input.split(' ').map((word) {
                                              if (word.isEmpty) return '';
                                              return word[0].toUpperCase() + word.substring(1);
                                            }).join(' ');

                                            final parsed = DateFormat('MMM d').parseStrict(normalizedInput);

                                            final updated = DateTime(
                                              tempSelectedDate.year, parsed.month, parsed.day);
                                            setStateDialog(() {
                                              tempSelectedDate = updated;
                                            });
                                          } catch (e) {
                                            // Invalid input; you can handle this if you want
                                          }
                                        },
                                        onSubmitted: (_) {
                                          setStateDialog(() {
                                            isEditingDate = false;
                                          });
                                        },
                                      ),
                                    )
                                  : Text(
                                      DateFormat('EEE, MMM d').format(tempSelectedDate),
                                      style: const TextStyle(
                                        color: Colors.black,
                                        fontSize: 32,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                              const Spacer(),
                              IconButton(
                                onPressed: () {
                                  setStateDialog(() {
                                    isEditingDate = !isEditingDate;
                                  });
                                },
                                icon: Icon(isEditingDate ? Icons.check : Icons.edit, size: 20),
                              ),
                            ],
                          ),
                          ],
                        ),
                      ),
                      const Divider(height: 1),
                      // Calendar
                      Container(
                        height: 280,
                        child: CalendarDatePicker(
                          initialDate: tempSelectedDate,
                          key: ValueKey(tempSelectedDate),
                          firstDate: DateTime(2000),
                          lastDate: DateTime(2100),
                          currentDate: today,
                          onDateChanged: (DateTime date) {
                            setStateDialog(() {
                              tempSelectedDate = date;
                            });
                          },
                        ),
                      ),
                      // Action buttons - Today, Cancel, OK
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 8),
                        child: Row(
                          children: [
                            // Today button on the left
                            TextButton(
                              onPressed: () {
                                setStateDialog(() {
                                  tempSelectedDate = today;
                                });
                              },
                              child: const Text(
                                'Today',
                                style: TextStyle(
                                  color: Colors.blue,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const Spacer(),
                            // Cancel and OK buttons on the right
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                              child: const Text(
                                'Cancel',
                                style: TextStyle(color: Colors.grey),
                              ),
                            ),
                            const SizedBox(width: 8),
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop(tempSelectedDate);
                              },
                              child: const Text(
                                'OK',
                                style: TextStyle(
                                  color: Colors.blue,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );

    if (selectedDate != null) {
      setState(() {
        _selectedDate = selectedDate;
        _updateDateController();
      });

      // Save to UserModel in yyyy-mm-dd format
      Provider.of<UserModel>(context, listen: false).setSelectedDate(
          "${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}");
    }
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: _refreshPage,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Flexible(
                  flex: 2,
                  child: DropdownSearch<String>(
                    items: _trainNumbers,
                    selectedItem: _selectedTrainNumber,
                    onChanged: _onTrainNumberChanged,
                    dropdownDecoratorProps: const DropDownDecoratorProps(
                      dropdownSearchDecoration: InputDecoration(
                        labelText: 'Train Number',
                        border: OutlineInputBorder(),
                        contentPadding:
                            EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                      ),
                    ),
                    popupProps: PopupProps.menu(
                      showSearchBox: true,
                      searchFieldProps: TextFieldProps(
                          keyboardType: TextInputType.number,
                          inputFormatters: <TextInputFormatter>[
                            FilteringTextInputFormatter.digitsOnly,
                            LengthLimitingTextInputFormatter(6),
                          ]),
                      itemBuilder: (context, item, isSelected) {
                        return ListTile(
                          title:
                              Text(item, style: const TextStyle(fontSize: 14)),
                          selected: isSelected,
                        );
                      },
                    ),
                    dropdownButtonProps: DropdownButtonProps(
                      icon: loading
                          ? const Padding(
                              padding: EdgeInsets.all(8.0),
                              child: SizedBox(
                                width: 20,
                                height: 20,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              ),
                            )
                          : const Icon(Icons.arrow_drop_down),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please select a train number';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Flexible(
                  flex: 3,
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'Train Name',
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                    ),
                    readOnly: true,
                    controller: _trainNameController,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'Select Date (DD-MMM-YYYY)',
                border: OutlineInputBorder(),
                suffixIcon: Icon(Icons.calendar_today),
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 12, vertical: 16),
              ),
              readOnly: true,
              onTap: _selectDate,
              controller: _dateController,
              validator: (value) {
                if (_selectedDate == null) {
                  return 'Please select a date';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            Center(
              child: Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: ElevatedButton(
                  onPressed: _submitForm,
                  style: ElevatedButton.styleFrom(
                    foregroundColor: Colors.black87,
                    backgroundColor: Colors.white,
                    side: const BorderSide(color: Colors.black87, width: 0.5),
                    padding: const EdgeInsets.symmetric(
                        horizontal: 32, vertical: 12),
                  ),
                  child: const Text(
                    'Submit',
                    style: TextStyle(
                      fontSize: 16.0,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
