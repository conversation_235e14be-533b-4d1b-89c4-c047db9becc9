class OnboardingResponse {
  final String message;
  final List<String>? stations;
  final DateTime? lastLocationFetched;
  final String ? lastLocationFetchedFromUser;
  final String? date;
  final String? trainNumber;
  final List<String>? coachNumbers;
  final Map<String, Map<String, List<int>>>? details;
  final Map<String, Map<String, List<int>>>? detailsOffBoarding;
  final Map<String, Map<String, List<int>>>? detailsVacant;
  final Map<String, Map<String, List<int>>>? detailsOffBoardingInroute;
  final Map<String, Map<String, List<int>>>? detailsOffBoardingCancelled;
  final Map<String, Map<String, List<int>>>? detailsOnBoardingCancelled;
  final Map<String, Map<String, List<int>>>? detailsInroute;

  OnboardingResponse({
    required this.message,
    this.stations,
    this.lastLocationFetched,
    this.lastLocationFetchedFromUser,
    this.date,
    this.trainNumber,
    this.coachNumbers,
    this.details,
    this.detailsOffBoarding,
    this.detailsVacant,
    this.detailsOffBoardingInroute,
    this.detailsInroute,
    this.detailsOffBoardingCancelled,
    this.detailsOnBoardingCancelled,
  });

  factory OnboardingResponse.fromJson(Map<String, dynamic> json) {
    return OnboardingResponse(
      message: json['message'],
      stations: (json['stations'] as List<dynamic>?)?.cast<String>(),
      lastLocationFetched: json['last_location_fetched'] != null
          ? DateTime.parse(json['last_location_fetched'])
          : null,
      lastLocationFetchedFromUser:json["last_location_fetched_from_user"],
      date: json['date'],
      trainNumber: json['train_number'],
      coachNumbers: (json['coach_numbers'] as List<dynamic>?)?.cast<String>(),
      details: (json['details'] as Map<String, dynamic>?)?.map((key, value) => MapEntry(
            key,
            (value as Map<String, dynamic>).map((k, v) => MapEntry(k, (v as List<dynamic>).cast<int>())),
          )),
      detailsOffBoarding: (json['details_off_boarding'] as Map<String, dynamic>?)?.map((key, value) => MapEntry(
            key,
            (value as Map<String, dynamic>).map((k, v) => MapEntry(k, (v as List<dynamic>).cast<int>())),
          )),
      detailsVacant: (json['details_vacant'] as Map<String, dynamic>?)?.map((key, value) => MapEntry(
          key,
          (value as Map<String, dynamic>).map((k, v) => MapEntry(k, (v as List<dynamic>).cast<int>())),
        )),

      detailsOffBoardingInroute: (json['off_boarding_inroute_details'] as Map<String, dynamic>?)?.map((key, value) => MapEntry(
          key,
          (value as Map<String, dynamic>).map((k, v) => MapEntry(k, (v as List<dynamic>).cast<int>())),
        )),
      detailsInroute: (json['on_boarding_inroute_details'] as Map<String, dynamic>?)?.map((key, value) => MapEntry(
          key,
          (value as Map<String, dynamic>).map((k, v) => MapEntry(k, (v as List<dynamic>).cast<int>())),
        )),
      detailsOffBoardingCancelled: (json['off_boarding_cancelled_details'] as Map<String, dynamic>?)?.map((key, value) => MapEntry(
          key,
          (value as Map<String, dynamic>).map((k, v) => MapEntry(k, (v as List<dynamic>).cast<int>())),
        )),
      detailsOnBoardingCancelled: (json['on_boarding_cancelled_details'] as Map<String, dynamic>?)?.map((key, value) => MapEntry(
          key,
          (value as Map<String, dynamic>).map((k, v) => MapEntry(k, (v as List<dynamic>).cast<int>())),
        )),
    );
  }
}
