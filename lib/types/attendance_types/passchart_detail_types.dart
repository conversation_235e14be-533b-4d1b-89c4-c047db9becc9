class TrainScheduleResponse {
  final String username;
  final String userType;
  final String queryDate;
  final bool fallbackMode;
  final List<TrainData> trains;

  TrainScheduleResponse({
    required this.username,
    required this.userType,
    required this.queryDate,
    required this.fallbackMode,
    required this.trains,
  });

  factory TrainScheduleResponse.fromJson(Map<String, dynamic> json) {
    return TrainScheduleResponse(
      username: json['username'] ?? '',
      userType: json['user_type'] ?? '',
      queryDate: json['query_date'] ?? '',
      fallbackMode: json['fallback_mode'] ?? false,
      trains: (json['trains'] as List<dynamic>?)
              ?.map((train) => TrainData.fromJson(train))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'user_type': userType,
      'query_date': queryDate,
      'fallback_mode': fallbackMode,
      'trains': trains.map((train) => train.toJson()).toList(),
    };
  }
}

class TrainData {
  final int trainNo;
  final List<String> stoppages;
  final Map<String, CoachData> assignedCoaches;
  final Map<String, CoachData> otherCoaches;

  TrainData({
    required this.trainNo,
    required this.stoppages,
    required this.assignedCoaches,
    required this.otherCoaches,
  });

  factory TrainData.fromJson(Map<String, dynamic> json) {
    return TrainData(
      trainNo: json['train_no'] ?? 0,
      stoppages: List<String>.from(json['stoppages'] ?? []),
      assignedCoaches: _parseCoachData(json['assigned_coaches']),
      otherCoaches: _parseCoachData(json['other_coaches']),
    );
  }

  static Map<String, CoachData> _parseCoachData(dynamic coachesJson) {
    if (coachesJson == null) return {};
    
    final Map<String, CoachData> coaches = {};
    (coachesJson as Map<String, dynamic>).forEach((coachName, coachData) {
      coaches[coachName] = CoachData.fromJson(coachData);
    });
    return coaches;
  }

  Map<String, dynamic> toJson() {
    return {
      'train_no': trainNo,
      'stoppages': stoppages,
      'assigned_coaches': assignedCoaches.map((key, value) => MapEntry(key, value.toJson())),
      'other_coaches': otherCoaches.map((key, value) => MapEntry(key, value.toJson())),
    };
  }
}

class CoachData {
  final Map<String, BerthInfo> onboarding;
  final Map<String, BerthInfo> offboarding;
  final Map<String, String> available;

  CoachData({
    required this.onboarding,
    required this.offboarding,
    required this.available,
  });

  factory CoachData.fromJson(Map<String, dynamic> json) {
    return CoachData(
      onboarding: _parseBerthInfo(json['onboarding']),
      offboarding: _parseBerthInfo(json['offboarding']),
      available: _parseAvailable(json['available']),
    );
  }

  static Map<String, BerthInfo> _parseBerthInfo(dynamic berthJson) {
    if (berthJson == null) return {};
    
    final Map<String, BerthInfo> berths = {};
    (berthJson as Map<String, dynamic>).forEach((berthNo, berthData) {
      berths[berthNo] = BerthInfo.fromJson(berthData);
    });
    return berths;
  }

  static Map<String, String> _parseAvailable(dynamic availableJson) {
    if (availableJson == null) return {};
    
    final Map<String, String> available = {};
    (availableJson as Map<String, dynamic>).forEach((berthNo, route) {
      available[berthNo] = route?.toString() ?? '';
    });
    return available;
  }

  Map<String, dynamic> toJson() {
    return {
      'onboarding': onboarding.map((key, value) => MapEntry(key, value.toJson())),
      'offboarding': offboarding.map((key, value) => MapEntry(key, value.toJson())),
      'available': available,
    };
  }
}

class BerthInfo {
  final String occupied;
  final String vacant;

  BerthInfo({
    required this.occupied,
    required this.vacant,
  });

  factory BerthInfo.fromJson(Map<String, dynamic> json) {
    return BerthInfo(
      occupied: json['occupied']?.toString() ?? '',
      vacant: json['vacant']?.toString() ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'occupied': occupied,
      'vacant': vacant,
    };
  }

  // Helper methods for occupied and vacant routes
  List<String> get occupiedRoutes {
    if (occupied.isEmpty) return [];
    return occupied.split(',').map((route) => route.trim()).toList();
  }

  List<String> get vacantRoutes {
    if (vacant.isEmpty) return [];
    return vacant.split(',').map((route) => route.trim()).toList();
  }

  bool get hasOccupiedRoutes => occupied.isNotEmpty;
  bool get hasVacantRoutes => vacant.isNotEmpty;
}

// Extension methods for easier data access
extension TrainScheduleResponseExtensions on TrainScheduleResponse {
  // Get all coach names across all trains
  List<String> get allCoachNames {
    final Set<String> coachNames = {};
    for (final train in trains) {
      coachNames.addAll(train.assignedCoaches.keys);
      coachNames.addAll(train.otherCoaches.keys);
    }
    return coachNames.toList()..sort();
  }

  // Get total number of berths across all trains
  int get totalBerthCount {
    int count = 0;
    for (final train in trains) {
      for (final coach in [...train.assignedCoaches.values, ...train.otherCoaches.values]) {
        count += coach.onboarding.length + coach.offboarding.length + coach.available.length;
      }
    }
    return count;
  }

  // Check if user is in fallback mode
  bool get isUsingFallback => fallbackMode;

  // Get formatted date string
  String get formattedDate {
    try {
      final date = DateTime.parse(queryDate);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return queryDate;
    }
  }
}

extension TrainDataExtensions on TrainData {
  // Get all berth numbers for a specific coach
  List<String> getBerthNumbers(String coachName) {
    final Set<String> berthNumbers = {};
    
    final assignedCoach = assignedCoaches[coachName];
    final otherCoach = otherCoaches[coachName];
    
    if (assignedCoach != null) {
      berthNumbers.addAll(assignedCoach.onboarding.keys);
      berthNumbers.addAll(assignedCoach.offboarding.keys);
      berthNumbers.addAll(assignedCoach.available.keys);
    }
    
    if (otherCoach != null) {
      berthNumbers.addAll(otherCoach.onboarding.keys);
      berthNumbers.addAll(otherCoach.offboarding.keys);
      berthNumbers.addAll(otherCoach.available.keys);
    }
    
    return berthNumbers.toList()..sort((a, b) => int.tryParse(a)?.compareTo(int.tryParse(b) ?? 0) ?? a.compareTo(b));
  }

  // Get stoppages as a formatted string
  String get stoppagesString => stoppages.join(' → ');

  // Check if train has assigned coaches
  bool get hasAssignedCoaches => assignedCoaches.isNotEmpty;

  // Check if train has other coaches
  bool get hasOtherCoaches => otherCoaches.isNotEmpty;

  // Get all coach names for this train
  List<String> get allCoachNames {
    final Set<String> names = {};
    names.addAll(assignedCoaches.keys);
    names.addAll(otherCoaches.keys);
    return names.toList()..sort();
  }
}

extension CoachDataExtensions on CoachData {
  // Get all berth numbers for this coach
  List<String> get allBerthNumbers {
    final Set<String> berthNumbers = {};
    berthNumbers.addAll(onboarding.keys);
    berthNumbers.addAll(offboarding.keys);
    berthNumbers.addAll(available.keys);
    return berthNumbers.toList()..sort((a, b) => int.tryParse(a)?.compareTo(int.tryParse(b) ?? 0) ?? a.compareTo(b));
  }

  // Check if coach has any data
  bool get hasData => onboarding.isNotEmpty || offboarding.isNotEmpty || available.isNotEmpty;

  // Get total berth count for this coach
  int get totalBerthCount => onboarding.length + offboarding.length + available.length;

  // Check if coach has onboarding data
  bool get hasOnboarding => onboarding.isNotEmpty;

  // Check if coach has offboarding data
  bool get hasOffboarding => offboarding.isNotEmpty;

  // Check if coach has available data
  bool get hasAvailable => available.isNotEmpty;
}

extension BerthInfoExtensions on BerthInfo {
  // Check if berth is completely occupied
  bool get isFullyOccupied => hasOccupiedRoutes && !hasVacantRoutes;

  // Check if berth is completely vacant
  bool get isFullyVacant => !hasOccupiedRoutes && hasVacantRoutes;

  // Check if berth has mixed occupancy
  bool get hasMixedOccupancy => hasOccupiedRoutes && hasVacantRoutes;

  // Get occupancy status as string
  String get occupancyStatus {
    if (isFullyOccupied) return 'Fully Occupied';
    if (isFullyVacant) return 'Fully Vacant';
    if (hasMixedOccupancy) return 'Partially Occupied';
    return 'No Data';
  }

  // Get formatted route strings
  String get formattedOccupied => occupiedRoutes.isNotEmpty ? occupiedRoutes.join(', ') : 'None';
  String get formattedVacant => vacantRoutes.isNotEmpty ? vacantRoutes.join(', ') : 'None';
}