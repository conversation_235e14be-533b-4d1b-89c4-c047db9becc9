import 'dart:convert';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/services/api_services/api_service.dart';
import 'package:railops/types/edit_train_types/coach_update_type.dart';
import 'package:railops/types/edit_train_types/edit_train_types.dart';
import 'package:railops/types/edit_train_types/stoppages_update_type.dart';

class StoppageLatLng {
  final String stationName;
  final String stationCode;
  final double latitude;
  final double longitude;

  StoppageLatLng({
    required this.stationName,
    required this.stationCode,
    required this.latitude,
    required this.longitude,
  });

  factory StoppageLatLng.fromJson(Map<String, dynamic> json) {
    return StoppageLatLng(
      stationName: json['station_name'] ?? '',
      stationCode: json['station_code'] ?? '',
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'station_name': stationName,
      'station_code': stationCode,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

class EditTrainsData {
  final int? id;
  final Map<String, String>? extraInfo;
  final int? trainNo;
  final String? trainName;
  final int? relatedTrain;
  final String? updown; // This can be null as per your JSON
  final String? division;
  final String? depot;
  final List<int>? frequency;
  final String? fromStation;
  final String? toStation;
  final String? chartingDay;
  final String? startTime;
  final String? endTime;
  final String? chartingTime;
  final String? trainType;
  final List<String>? stoppagesInSequence;
  final List<Map<String, String>>? arrivalTime;
  final int? journeyDurationDays; // This can be null as per your JSON
  final List<String>? attendanceStations;
  final List<String>? coachesInSequence;
  final String? coachSequenceUpdatedBy;
  final String? coachSequenceUpdatedAt;
  final String? createdAt;
  final String? createdBy;
  final String? updatedAt;
  final String? updatedBy;
  final bool? isBufferTimeRestriction;
  final bool? isLocationRestriction;
  final bool? isMediaUploadEnabled;
    final List<StoppageLatLng>? stoppagesWithLatLng;


  EditTrainsData({
    this.id,
    this.extraInfo,
    this.trainNo, // Changed from required to optional
    this.trainName, // Changed from required to optional
    this.relatedTrain, // Changed from required to optional
    this.updown, // Changed from required to optional
    this.division, // Changed from required to optional
    this.depot, // Changed from required to optional
    this.frequency, // Changed from required to optional
    this.fromStation,
    this.toStation,
    this.chartingDay,
    this.startTime, // Changed from required to optional
    this.endTime, // Changed from required to optional
    this.chartingTime,
    this.trainType, // Changed from required to optional
    this.stoppagesInSequence, // Changed from required to optional
    this.arrivalTime,
    this.journeyDurationDays,
    this.attendanceStations,
    this.coachesInSequence,
    this.coachSequenceUpdatedBy,
    this.coachSequenceUpdatedAt,
    this.createdAt,
    this.createdBy,
    this.updatedAt,
    this.updatedBy,
    this.isBufferTimeRestriction,
    this.isLocationRestriction,
    this.isMediaUploadEnabled,
       this.stoppagesWithLatLng,
  });

  factory EditTrainsData.fromJson(Map<String, dynamic> json) {
    return EditTrainsData(
      id: json['id'] as int?,
      extraInfo: (json['extra_info'] as Map<String, dynamic>?)?.map(
        (key, value) => MapEntry(key.toString(), value.toString()),
      ),
      trainNo: json['train_no'] as int?,
      trainName: json['train_name'] as String?,
      relatedTrain: json['related_train'] is int
          ? json['related_train'] as int?
          : int.tryParse(json['related_train']?.toString() ?? ''),
      updown: json['up_down'] as String?, // Will handle the null case
      division: json['division'] as String?,
      depot: json['Depot'] as String?,
      frequency: (json['frequency'] as List?)
          ?.map((item) => int.tryParse(item.toString()) ?? 0)
          .toList(),
      fromStation: json['from_station'] as String?,
      toStation: json['to_station'] as String?,
      chartingDay: json['charting_day'] as String?,
      startTime: json['start_time'] as String?,
      endTime: json['end_time'] as String?,
      chartingTime: json['charting_time'] as String?,
      trainType: json['train_type'] as String?,
      stoppagesInSequence: (json['stopages_in_sequence'] as List?)
          ?.map((item) => item.toString())
          .toList(),
      arrivalTime: (json['arrival_time'] as List?)
          ?.map((item) => (item as Map<String, dynamic>).map(
                (key, value) => MapEntry(
                    key.toString(), value.toString()),
              ))
          .toList(),
      journeyDurationDays:
          json['journey_duration_days'] as int?, // Will handle the null case
      attendanceStations: (json['attendance_stations'] as List?)
          ?.map((item) => item.toString())
          .toList(),
      coachesInSequence: (json['coaches_in_sequence'] as List?)
          ?.map((item) => item.toString())
          .toList(),
      coachSequenceUpdatedBy: json['coach_sequence_updated_by'] as String?,
      coachSequenceUpdatedAt: json['coach_sequence_updated_at'] as String?,
      createdAt: json['created_at'] as String?,
      createdBy: json['created_by'] as String?,
      updatedAt: json['updated_at'] as String?,
      updatedBy: json['updated_by'] as String?,
      stoppagesWithLatLng: (json['stoppages_with_lat_lng'] as List?)?.map((item) =>
          StoppageLatLng.fromJson(item as Map<String, dynamic>)).toList(),
      isBufferTimeRestriction: json['is_time_resriction'] as bool?,
      isLocationRestriction: json['is_location_restriction'] as bool?,
      isMediaUploadEnabled: json['media_upload_enabled'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'extra_info': extraInfo,
      'train_no': trainNo,
      'train_name': trainName,
      'related_train': relatedTrain,
      'up_down': updown,
      'division': division,
      'Depot': depot,
      'frequency': frequency,
      'from_station': fromStation,
      'to_station': toStation,
      'charting_day': chartingDay,
      'start_time': startTime,
      'end_time': endTime,
      'charting_time': chartingTime,
      'train_type': trainType,
      'stopages_in_sequence': stoppagesInSequence,
      'arrival_time': arrivalTime,
      'journey_duration_days': journeyDurationDays,
      'attendance_stations': attendanceStations,
      'coaches_in_sequence': coachesInSequence,
      'coach_sequence_updated_by': coachSequenceUpdatedBy,
      'coach_sequence_updated_at': coachSequenceUpdatedAt,
      'created_at': createdAt,
      'created_by': createdBy,
      'updated_at': updatedAt,
      'updated_by': updatedBy,
        'stoppages_with_lat_lng':
          stoppagesWithLatLng?.map((e) => e.toJson()).toList(),
      'is_time_resriction': isBufferTimeRestriction,
      'is_location_restriction': isLocationRestriction,
      'media_upload_enabled': isMediaUploadEnabled,
    };
  }
}

class EditTrainsRequest {
  final String? token;
  final int? trainNo;
  final String? trainName;
  final int? relatedTrain;
  final String? updown;
  final String? division;
  final String? depot;
  final Map<String, String>? extraInfo;
  final List<int>? frequency;
  final String? fromStation;
  final String? toStation;
  final String? chartingDay;
  final String? startTime;
  final String? endTime;
  final String? chartingTime;
  final String? trainType;
  final List<String>? stoppagesInSequence;
  final int? journeyDurationDays;
  final List<String>? attendanceStations;
  final List<String>? coachesInSequence;

  EditTrainsRequest({
    this.token,
    this.trainNo,
    this.trainName,
    this.relatedTrain,
    this.updown,
    this.division,
    this.depot,
    this.extraInfo,
    this.frequency,
    this.fromStation,
    this.toStation,
    this.chartingDay,
    this.startTime,
    this.endTime,
    this.chartingTime,
    this.trainType,
    this.stoppagesInSequence,
    this.journeyDurationDays,
    this.attendanceStations,
    this.coachesInSequence,
  });

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'train_no': trainNo,
      'train_name': trainName,
      'related_train': relatedTrain,
      'up_down': updown,
      'division': division,
      'Depot': depot,
      'extra_info': extraInfo,
      'frequency': frequency,
      'from_station': fromStation,
      'to_station': toStation,
      'charting_day': chartingDay,
      'start_time': startTime,
      'end_time': endTime,
      'charting_time': chartingTime,
      'train_type': trainType,
      'stopages_in_sequence': stoppagesInSequence,
      'journey_duration_days': journeyDurationDays,
      'attendance_stations': attendanceStations,
      'coaches_in_sequence': coachesInSequence,
    };
  }
}
