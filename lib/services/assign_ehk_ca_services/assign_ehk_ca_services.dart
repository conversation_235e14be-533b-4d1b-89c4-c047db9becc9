import 'dart:convert';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/services/api_services/api_service.dart';

class AdminAssignService {
  static Future<List<String>> fetchCoaches(String trainNumber) async {
    try {
      final responseJson = await ApiService.get(
          '/api/coaches-of-train/?trains=$trainNumber', {});
      return List<String>.from(responseJson['coaches']);
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      return [];
    }
  }

  static Future<Map<String, dynamic>> fetchTrainsCoachWise(
      String trainNumber, String date, String forUserType, String token) async {

        
    try {
      final response = await ApiService.post(
        '/api/users/get_Train_access_coach_wise/',
        {
          "train_number": trainNumber,
          "date": date,
          "for_user_type": forUserType,
          "token": token
        },
      );
      return response;
    } on ApiException catch (e) {
      throw e.message;
    }
  }

  static Future<List<dynamic>> fetchSpecificUsers(
      String userType, String token) async {
    try {
      final response = await ApiService.post(
        '/api/users/fetch_specific_users/',
        {"user_type": userType, "token": token},
      );
      return response['users'];
    } on ApiException catch (e) {
      throw e.message;
    }
  }

  static Future<List<dynamic>> fetchSpecificUsersDivision(
      String userType, String token, String trainNumber) async {
    try {
      final response = await ApiService.post(
        '/api/users/fetch_specific_users_division/',
        {"user_type": userType, "token": token, "train_number": trainNumber },
      );
      return response['users'];
    } on ApiException catch (e) {
      throw e.message;
    }
  }

  static Future<dynamic> addTrainsCoachWise(
      Map<String, dynamic> data,
      Map<String, dynamic> dataEhk,
      String trainNumber,
      String date,
      String forUserType,
      String token) async {
    try {
      final response = ApiService.post(
        '/api/users/add_trains_coach_wise/',
        {
          "data": data,
          "dataEhk": dataEhk,
          "train_number": trainNumber,
          "date": date,
          "for_user_type": forUserType,
          "token": token
        },
      );
      return response;
    } on ApiException catch (e) {
      throw e.message;
    }
  }

  static Future<dynamic> addBothTrainsCoachWise(
      Map<String, dynamic> data1,
      Map<String, dynamic> dataEhk1,
      String trainNumber1,
      String date1,

      Map<String, dynamic> data2,
      Map<String, dynamic> dataEhk2,
      String trainNumber2,
      String date2,

      String forUserType,
      String token) async {
    try {
      final response = ApiService.post(
        '/api/users/add_both_trains_coach_wise/',
        {
          "data1": data1,
          "dataEhk1": dataEhk1,
          "train_number1": trainNumber1,
          "date1": date1,

          "data2": data2,
          "dataEhk2": dataEhk2,
          "train_number2": trainNumber2,
          "date2": date2,

          "for_user_type": forUserType,
          "token": token
        },
      );
      return response;
    } on ApiException catch (e) {
      throw e.message;
    }
  }

  static Future<String> fetchLastJourneyDate(
      String username, String token) async {
    try {
      final response = await ApiService.post(
        '/api/users/fetch_last_journey_date/',
        {"user_name": username, "token": token},
      );
      return '${response['last_train_number']} : ${response['last_origin_date']}';
    } on ApiException catch (e) {
      throw e.message;
    }
  }

  static Future<void> removeTrainDetailsOriginDate(
      String username,
      List<String> coaches,
      String trainNumber,
      String originDate,
      String forUserType,
      String token) async {
    try {
      await ApiService.post(
        '/api/users/remove_train_details_by_origin_date/',
        {
          "user_name": username,
          "train_number": trainNumber,
          "origin_date": originDate,
          "coach_numbers": coaches,
          "for_user_type": forUserType,
          "token": token
        },
      );
    } on ApiException catch (e) {
      throw e.message;
    }
  }
}
