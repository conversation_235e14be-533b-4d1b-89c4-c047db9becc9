import 'dart:convert';
import 'dart:typed_data';
import 'package:railops/constants/api_constant.dart';
import 'package:http/http.dart' as http;
import 'package:railops/services/api_services/index.dart';


class DownloadPdfService {

  static Future<Map<String, dynamic>> downloadPdf(
    String date,
    String url,
    bool isMail,
    String trains, {
    bool? showPhoneColumn,
    String? division,
    String? userType,
  }) async {
    try {
      String endpoint = url;
      List<String>? userTypeList;
      if (userType != null && userType.isNotEmpty) {
        userTypeList = userType.split(',').map((e) => e.trim()).toList();
      }
      

      final response = await http.post(
        Uri.parse('${ApiConstant.baseUrl}$endpoint'),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode({
          "date": date,
          "isMail": isMail,
          "trains": trains,
          "showPhoneColumn": showPhoneColumn ?? false,
          "division": division ?? '',
          "userType": userType ?? '',
        }),
      );

      if (response.statusCode == 200) {
        String? filename;
        final contentDisposition = response.headers['content-disposition'];
        if (contentDisposition != null) {
          final regex = RegExp(r'filename="?([^";]+)"?');
          final match = regex.firstMatch(contentDisposition);
          if (match != null) {
            filename = match.group(1);
          }
        }

        return {
          'bytes': response.bodyBytes,
          'filename': filename ?? 'Report_$date.pdf',
        };
      } else {
        throw Exception('Failed to download PDF. Status code: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Download failed: $e');
    }
  }



  

  static Future<String> mailAllTrainPdf(
    String date,
    String url,
    bool isMail,
    String token,
    String trains,
    {
      bool ? showPhoneColumn,
      String? division,
      String? userType, 
    }
  ) async {
    try{

      List<String>? userTypeList;
      if (userType != null && userType.isNotEmpty) {
        userTypeList = userType.split(',').map((e) => e.trim()).toList();
      }

      final request ={
        "date":date,
        "isMail":isMail,
        "token":token,
        "trains":trains,
        "showPhoneColumn":showPhoneColumn ?? false,
        "division":division ?? '',
        "userType": userType ?? '',
      };
      
        final responseJson =await ApiService.post(url, request);
        return responseJson['message'];

    }
     on ApiException catch (e) {
      throw (e.message);
    }
    catch(e){
      return '$e';
    }
}

}

