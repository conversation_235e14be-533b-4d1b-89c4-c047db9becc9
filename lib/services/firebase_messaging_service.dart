import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb, kDebugMode;

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/services/fcm_token_service.dart';
import 'package:railops/services/firestore_token_service.dart';

// Global navigator key for navigation from outside the widget tree
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// Top-level function to handle background messages
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  if (kIsWeb) return; // 🚫 Skip web

  await Firebase.initializeApp();

  // Create a local notifications plugin instance for background use
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // Set up notification channel for Android
  if (Platform.isAndroid) {
    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(const AndroidNotificationChannel(
          'high_importance_channel',
          'High Importance Notifications',
          description: 'This channel is used for important notifications.',
          importance: Importance.high,
        ));
  }

  // Show notification
  final notification = message.notification;
  if (notification != null && !kIsWeb) {
    flutterLocalNotificationsPlugin.show(
      notification.hashCode,
      notification.title,
      notification.body,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'high_importance_channel',
          'High Importance Notifications',
          channelDescription:
              'This channel is used for important notifications.',
          icon: 'ic_notification',
          color: Color(0xFF4CAF50),
        ),
        iOS: DarwinNotificationDetails(),
      ),
      payload: json.encode(message.data),
    );
  }

  // Save to history using the singleton
  await FirebaseMessagingService().saveNotificationToHistory(message);
}

class FirebaseMessagingService {
  static final FirebaseMessagingService _instance =
      FirebaseMessagingService._internal();

  factory FirebaseMessagingService() {
    return _instance;
  }

  FirebaseMessagingService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isFlutterLocalNotificationsInitialized = false;

  // Channel IDs
  static const String _highImportanceChannelId = 'high_importance_channel';
  static const String _highImportanceChannelName =
      'High Importance Notifications';
  static const String _highImportanceChannelDescription =
      'This channel is used for important notifications.';

  // Initialize Firebase Messaging
  Future<void> initialize() async {

      if (kIsWeb) {
    debugPrint("Firebase Messaging is disabled on web.");
    return;
  }

    try {
      // Register background message handler
      FirebaseMessaging.onBackgroundMessage(
          _firebaseMessagingBackgroundHandler);

      // Initialize local notifications
      await setupFlutterNotifications();

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen(showFlutterNotification);

      // Handle notification clicks when app is in background but not terminated
      FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

      // Check for initial message (app opened from terminated state)
      final initialMessage = await _firebaseMessaging.getInitialMessage();
      if (initialMessage != null) {
        _handleMessageOpenedApp(initialMessage);
      }

      // Request permission
      await requestPermission();

      // Get FCM token
      final token = await _firebaseMessaging.getToken();

      // debugPrint the token with clear markers to make it easy to find in logs
      debugPrint('==================== FCM TOKEN BEGIN ====================');
      debugPrint(token);
      debugPrint('==================== FCM TOKEN END ====================');

      // Save token to shared preferences
      if (token != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('fcm_token', token);

        // Also store in Firestore for Cloud Function access
        await FirestoreTokenService.storeTokenInFirestore(fcmToken: token);
      }

      // Listen for token refresh
      _firebaseMessaging.onTokenRefresh.listen((newToken) async {
        // debugPrint the refreshed token with clear markers
        debugPrint(
            '==================== FCM TOKEN REFRESHED BEGIN ====================');
        debugPrint(newToken);
        debugPrint(
            '==================== FCM TOKEN REFRESHED END ====================');

        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('fcm_token', newToken);

        // Store refreshed token in Firestore
        await FirestoreTokenService.updateTokenInFirestore(newToken);

        // Sync refreshed token with server
        await _syncRefreshedTokenWithServer(newToken);
      });
    } catch (e) {
      debugPrint('Error initializing Firebase Messaging: $e');
      // Consider reporting this error to a monitoring service
    }
  }

  // Request notification permission
  Future<void> requestPermission() async {
    try {
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      debugPrint('User granted permission: ${settings.authorizationStatus}');
    } catch (e) {
      debugPrint('Error requesting notification permission: $e');
    }
  }

  // Setup Flutter Local Notifications
  Future<void> setupFlutterNotifications() async {
    if (_isFlutterLocalNotificationsInitialized) {
      return;
    }

    try {
      // Android initialization settings
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('ic_notification');

      // iOS initialization settings
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestAlertPermission: false,
        requestBadgePermission: false,
        requestSoundPermission: false,
      );

      // Initialization settings
      const InitializationSettings initializationSettings =
          InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      // Initialize plugin
      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse:
            (NotificationResponse notificationResponse) {
          // Handle notification tap
          if (notificationResponse.payload != null) {
            try {
              final Map<String, dynamic> data =
                  json.decode(notificationResponse.payload!);
              _handleNotificationTap(data);
            } catch (e) {
              debugPrint('Error parsing notification payload: $e');
            }
          }
        },
      );

      // Create notification channel for Android
      if (Platform.isAndroid) {
        await _flutterLocalNotificationsPlugin
            .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin>()
            ?.createNotificationChannel(const AndroidNotificationChannel(
              _highImportanceChannelId,
              _highImportanceChannelName,
              description: _highImportanceChannelDescription,
              importance: Importance.high,
            ));
      }

      // Set up iOS notification categories
      if (Platform.isIOS) {
        // iOS notification categories are handled automatically by the plugin
        // in newer versions, so we don't need to set them manually
      }

      // Set foreground notification presentation options
      await FirebaseMessaging.instance
          .setForegroundNotificationPresentationOptions(
        alert: true,
        badge: true,
        sound: true,
      );

      _isFlutterLocalNotificationsInitialized = true;
    } catch (e) {
      debugPrint('Error setting up Flutter Local Notifications: $e');
    }
  }

  // Show notification with enhanced content processing
  void showFlutterNotification(RemoteMessage message) {
    try {
      final RemoteNotification? notification = message.notification;
      final AndroidNotification? android = message.notification?.android;

      if (notification != null && !kIsWeb) {
        // Process notification content for enhanced display
        final processedContent =
            _processNotificationContent(notification, message.data);

        if (kDebugMode) {
          debugPrint('🔔 Showing notification:');
          debugPrint('   Title: ${processedContent['title']}');
          debugPrint(
              '   Body length: ${processedContent['body']?.length ?? 0} chars');
          debugPrint('   Data keys: ${message.data.keys.join(', ')}');
        }

        _flutterLocalNotificationsPlugin.show(
          notification.hashCode,
          processedContent['title'],
          processedContent['body'],
          NotificationDetails(
            android: android != null
                ? AndroidNotificationDetails(
                    _highImportanceChannelId,
                    _highImportanceChannelName,
                    channelDescription: _highImportanceChannelDescription,
                    icon: 'ic_notification',
                    color: const Color(0xFF4CAF50),
                    // Enhanced Android notification settings for rich content
                    importance: Importance.high,
                    priority: Priority.high,
                    styleInformation:
                        (processedContent['body']?.length ?? 0) > 100
                            ? BigTextStyleInformation(
                                processedContent['body'] ?? '',
                                contentTitle: processedContent['title'],
                                summaryText:
                                    _extractNotificationSummary(message.data),
                              )
                            : null,
                  )
                : null,
            iOS: const DarwinNotificationDetails(
              presentAlert: true,
              presentBadge: true,
              presentSound: true,
            ),
          ),
          payload: json.encode(message.data),
        );
      }

      // Save notification to history
      saveNotificationToHistory(message);
    } catch (e) {
      debugPrint('❌ Error showing notification: $e');

      // Fallback to basic notification if enhanced processing fails
      try {
        final RemoteNotification? notification = message.notification;
        if (notification != null && !kIsWeb) {
          _flutterLocalNotificationsPlugin.show(
            notification.hashCode,
            notification.title,
            notification.body,
            const NotificationDetails(
              android: AndroidNotificationDetails(
                _highImportanceChannelId,
                _highImportanceChannelName,
                icon: 'ic_notification',
                color: Color(0xFF4CAF50),
              ),
              iOS: DarwinNotificationDetails(),
            ),
            payload: json.encode(message.data),
          );
        }
      } catch (fallbackError) {
        debugPrint('❌ Fallback notification also failed: $fallbackError');
      }
    }
  }

  /// Process notification content for enhanced display
  /// Handles rich content from Firebase Cloud Function data field
  Map<String, String?> _processNotificationContent(
      RemoteNotification notification, Map<String, dynamic> data) {
    try {
      String? enhancedTitle = notification.title;
      String? enhancedBody = notification.body;

      // Check if this is an enhanced train location notification
      if (data.containsKey('type') &&
          data['type'] == 'enhanced_train_location_update') {
        if (kDebugMode) {
          debugPrint('🔍 Processing enhanced train location notification');
        }

        // Try to parse coach_data for better formatting
        if (data.containsKey('coach_data')) {
          try {
            final coachDataStr = data['coach_data'] as String;
            final coachData = json.decode(coachDataStr) as Map<String, dynamic>;

            // Extract summary information
            final stations = coachData['stations'] as List<dynamic>? ?? [];
            final hasCoachAssignments =
                coachData['has_coach_assignments'] ?? false;
            final summary =
                coachData['summary'] as String? ?? notification.body;

            // Enhance title with station information
            if (stations.isNotEmpty) {
              final stationCodes = stations.join(', ');
              enhancedTitle = hasCoachAssignments
                  ? 'Train Update - $stationCodes'
                  : 'General Train Update - $stationCodes';
            }

            // Use the formatted summary from Cloud Function
            if (summary != null && summary.isNotEmpty) {
              enhancedBody = summary;
            }

            if (kDebugMode) {
              debugPrint('✅ Enhanced notification content:');
              debugPrint('   Stations: ${stations.join(', ')}');
              debugPrint('   Has assignments: $hasCoachAssignments');
              debugPrint('   Body length: ${enhancedBody?.length ?? 0}');
            }
          } catch (e) {
            if (kDebugMode) {
              debugPrint(
                  '⚠️ Error parsing coach_data, using original content: $e');
            }
          }
        }
      }

      // Handle other notification types
      else if (data.containsKey('type')) {
        final notificationType = data['type'] as String;

        switch (notificationType) {
          case 'boarding_alert':
            enhancedTitle = '🚂 ${notification.title ?? 'Boarding Alert'}';
            break;
          case 'offboarding_alert':
            enhancedTitle = '🚪 ${notification.title ?? 'Off-boarding Alert'}';
            break;
          case 'station_approaching':
            enhancedTitle = '📍 ${notification.title ?? 'Station Approaching'}';
            break;
          case 'coach_reminder':
            enhancedTitle = '🚃 ${notification.title ?? 'Coach Reminder'}';
            break;
          default:
            // Keep original title for unknown types
            break;
        }
      }

      return {
        'title': enhancedTitle,
        'body': enhancedBody,
      };
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Error processing notification content: $e');
      }

      // Return original content on error
      return {
        'title': notification.title,
        'body': notification.body,
      };
    }
  }

  /// Extract notification summary for Android BigTextStyle
  String? _extractNotificationSummary(Map<String, dynamic> data) {
    try {
      if (data.containsKey('type') &&
          data['type'] == 'enhanced_train_location_update') {
        if (data.containsKey('coach_data')) {
          final coachDataStr = data['coach_data'] as String;
          final coachData = json.decode(coachDataStr) as Map<String, dynamic>;

          final stationsCount = coachData['stations_count'] ?? 0;
          final hasCoachAssignments =
              coachData['has_coach_assignments'] ?? false;

          return hasCoachAssignments
              ? 'Coach assignments for $stationsCount station(s)'
              : 'General train update for $stationsCount station(s)';
        }
      }

      // Extract timestamp for summary
      if (data.containsKey('timestamp')) {
        final timestamp = data['timestamp'] as String;
        try {
          final dateTime = DateTime.parse(timestamp);
          final timeStr =
              '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
          return 'Received at $timeStr';
        } catch (e) {
          // Ignore timestamp parsing errors
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('⚠️ Error extracting notification summary: $e');
      }
      return null;
    }
  }

  // Handle notification tap
  void _handleNotificationTap(Map<String, dynamic> data) {
    // Handle navigation based on notification data
    debugPrint('Notification tapped with data: $data');

    // Navigate to a specific screen based on the route parameter
    if (data.containsKey('route')) {
      final route = data['route'] as String;
      // Use the global navigator key for navigation
      navigatorKey.currentState?.pushNamed(route);
    }
  }

  // Handle message opened app
  void _handleMessageOpenedApp(RemoteMessage message) {
    debugPrint('Message opened app: ${message.data}');
    _handleNotificationTap(message.data);
  }

  // Save notification to history
  Future<void> saveNotificationToHistory(RemoteMessage message) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson =
          prefs.getStringList('notifications_history') ?? [];

      final notification = {
        'id': message.messageId,
        'title': message.notification?.title,
        'body': message.notification?.body,
        'data': message.data,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      notificationsJson.add(json.encode(notification));

      // Limit history to 50 notifications
      if (notificationsJson.length > 50) {
        notificationsJson.removeAt(0);
      }

      await prefs.setStringList('notifications_history', notificationsJson);
    } catch (e) {
      debugPrint('Error saving notification to history: $e');
    }
  }

  // Get notification history
  Future<List<Map<String, dynamic>>> getNotificationHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final notificationsJson =
          prefs.getStringList('notifications_history') ?? [];

      return notificationsJson
          .map((notificationJson) =>
              json.decode(notificationJson) as Map<String, dynamic>)
          .toList();
    } catch (e) {
      debugPrint('Error getting notification history: $e');
      return [];
    }
  }

  // Clear notification history
  Future<void> clearNotificationHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('notifications_history');
    } catch (e) {
      debugPrint('Error clearing notification history: $e');
    }
  }

  // Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await _firebaseMessaging.subscribeToTopic(topic);
    } catch (e) {
      debugPrint('Error subscribing to topic: $e');
    }
  }

  // Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _firebaseMessaging.unsubscribeFromTopic(topic);
    } catch (e) {
      debugPrint('Error unsubscribing from topic: $e');
    }
  }

  // Get the current FCM token
  Future<String?> getFcmToken() async {
    try {
      final token = await _firebaseMessaging.getToken();
      return token;
    } catch (e) {
      debugPrint('Error getting FCM token: $e');
      return null;
    }
  }

  // Sync refreshed token with server
  Future<void> _syncRefreshedTokenWithServer(String newToken) async {
    try {
      // Import the FcmTokenService and UserModel
      final userModel = UserModel();
      await userModel.loadUserData();

      if (userModel.token.isNotEmpty) {
        // Use the FcmTokenService to sync with server
        final success =
            await FcmTokenService.syncTokenWithServer(userModel.token);
        if (success) {
          debugPrint('Refreshed FCM token synced successfully with server');
        } else {
          debugPrint('Failed to sync refreshed FCM token with server');
        }
      }
    } catch (e) {
      debugPrint('Error syncing refreshed token with server: $e');
    }
  }
}
