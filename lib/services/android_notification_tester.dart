import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:railops/services/notification_quality_validator.dart';
import 'package:railops/services/firebase_messaging_service.dart';
import 'package:railops/services/firebase_cloud_function_service.dart';
import 'package:railops/services/fcm_token_service.dart';
import 'dart:convert';

/// Android Notification Tester
///
/// This service provides comprehensive testing for notification display
/// on real Android devices, focusing on train data formatting, table display,
/// and user experience validation.
class AndroidNotificationTester {
  static bool _isRunning = false;

  /// Test notification display on real Android device
  static Future<Map<String, dynamic>> testNotificationDisplay({
    required String testType,
    String? customTitle,
    String? customBody,
    Map<String, dynamic>? customData,
    bool validateQuality = true,
  }) async {
    if (_isRunning) {
      return {
        'status': 'skipped',
        'message': 'Test already running',
      };
    }

    _isRunning = true;
    final testStartTime = DateTime.now();

    try {
      if (kDebugMode) {
        print('📱 Testing notification display on Android device: $testType');
      }

      // Create test notification based on type
      final testNotification = await _createTestNotification(
        testType,
        customTitle: customTitle,
        customBody: customBody,
        customData: customData,
      );

      // Display the notification
      final displayResult = await _displayTestNotification(testNotification);

      // Validate quality if requested
      Map<String, dynamic>? qualityValidation;
      if (validateQuality) {
        qualityValidation =
            NotificationQualityValidator.validateNotificationContent(
                testNotification);
      }

      final testEndTime = DateTime.now();
      final testDuration = testEndTime.difference(testStartTime);

      return {
        'status': 'completed',
        'success': displayResult['success'],
        'test_type': testType,
        'notification_data': {
          'title': testNotification.notification?.title,
          'body': testNotification.notification?.body,
          'data': testNotification.data,
        },
        'display_result': displayResult,
        'quality_validation': qualityValidation,
        'test_duration_ms': testDuration.inMilliseconds,
        'timestamp': testEndTime.toIso8601String(),
        'device_info': await _getDeviceInfo(),
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Android notification test failed: $e');
      }

      return {
        'status': 'failed',
        'error': e.toString(),
        'test_type': testType,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } finally {
      _isRunning = false;
    }
  }

  /// Test coach table format display
  static Future<Map<String, dynamic>> testCoachTableDisplay({
    bool hasCoachAssignments = true,
    List<Map<String, dynamic>>? customStations,
  }) async {
    if (kDebugMode) {
      print(
          '📊 Testing coach table format display (hasAssignments: $hasCoachAssignments)');
    }

    // Create realistic station data
    final stations =
        customStations ?? _createTestStationData(hasCoachAssignments);

    // Build table format
    final tableBody = _buildTestCoachTable(stations, hasCoachAssignments);

    // Create notification with table data
    final testData = {
      'type': 'enhanced_train_location_update',
      'station_code': stations.map((s) => s['station_code']).join(', '),
      'coach_data': json.encode({
        'stations_count': stations.length,
        'stations': stations.map((s) => s['station_code']).toList(),
        'summary': tableBody,
        'has_coach_assignments': hasCoachAssignments,
      }),
      'timestamp': DateTime.now().toIso8601String(),
    };

    return await testNotificationDisplay(
      testType: 'coach_table_format',
      customTitle: hasCoachAssignments
          ? 'Train 12157 - Station Updates'
          : 'Train 12157 - General Update',
      customBody: tableBody,
      customData: testData,
      validateQuality: true,
    );
  }

  /// Test notification for users without coach assignments
  static Future<Map<String, dynamic>> testNoCoachAssignmentDisplay() async {
    if (kDebugMode) {
      print(
          '👤 Testing notification display for users without coach assignments');
    }

    const noAssignmentBody = '''Train update for stations ARA, BTA, DNR.

No coach assignments - contact admin for access.

General train information available.''';

    final testData = {
      'type': 'enhanced_train_location_update',
      'station_code': 'ARA, BTA, DNR',
      'coach_data': json.encode({
        'stations_count': 3,
        'stations': ['ARA', 'BTA', 'DNR'],
        'summary': noAssignmentBody,
        'has_coach_assignments': false,
      }),
      'timestamp': DateTime.now().toIso8601String(),
    };

    return await testNotificationDisplay(
      testType: 'no_coach_assignment',
      customTitle: 'Train 12157 - General Update',
      customBody: noAssignmentBody,
      customData: testData,
      validateQuality: true,
    );
  }

  /// Test comprehensive notification scenarios
  static Future<Map<String, dynamic>> testComprehensiveScenarios() async {
    if (kDebugMode) {
      print('🎯 Running comprehensive notification display scenarios');
    }

    final scenarios = [
      'coach_table_with_assignments',
      'coach_table_without_assignments',
      'single_station_update',
      'multi_station_update',
      'no_passenger_activity',
      'high_passenger_activity',
    ];

    final results = <String, dynamic>{};
    final overallResults = <String, bool>{};

    for (final scenario in scenarios) {
      try {
        final result = await _testScenario(scenario);
        results[scenario] = result;
        overallResults[scenario] = result['success'] ?? false;

        // Add delay between tests to avoid overwhelming the notification system
        await Future.delayed(const Duration(seconds: 2));
      } catch (e) {
        results[scenario] = {
          'status': 'failed',
          'error': e.toString(),
        };
        overallResults[scenario] = false;
      }
    }

    final passedScenarios =
        overallResults.values.where((success) => success).length;
    final totalScenarios = scenarios.length;

    return {
      'status': 'completed',
      'overall_success': passedScenarios == totalScenarios,
      'passed_scenarios': passedScenarios,
      'total_scenarios': totalScenarios,
      'success_rate':
          (passedScenarios / totalScenarios * 100).toStringAsFixed(1),
      'scenario_results': results,
      'summary': overallResults,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Create test notification based on type
  static Future<RemoteMessage> _createTestNotification(
    String testType, {
    String? customTitle,
    String? customBody,
    Map<String, dynamic>? customData,
  }) async {
    String title = customTitle ?? 'Test Notification';
    String body = customBody ?? 'Test notification body';
    Map<String, dynamic> data = customData ?? {'type': 'test'};

    switch (testType) {
      case 'coach_table_format':
        // Already handled by custom parameters
        break;

      case 'boarding_alert':
        title = '🚂 Boarding Alert - Coach A1';
        body = 'Boarding at ARA station in 5 minutes. Coach A1, Berth 23-24.';
        data = {
          'type': 'boarding_alert',
          'station_code': 'ARA',
          'coach': 'A1',
          'berth': '23-24',
          'timestamp': DateTime.now().toIso8601String(),
        };
        break;

      case 'station_approaching':
        title = '📍 Station Approaching - BTA';
        body =
            'BTA station approaching in 10 minutes. Prepare for boarding/deboarding.';
        data = {
          'type': 'station_approaching',
          'station_code': 'BTA',
          'minutes_to_arrival': 10,
          'timestamp': DateTime.now().toIso8601String(),
        };
        break;

      default:
        // Use provided custom parameters or defaults
        break;
    }

    // Create RemoteMessage-like object for testing
    return RemoteMessage(
      messageId: 'test_${DateTime.now().millisecondsSinceEpoch}',
      data: data,
      notification: RemoteNotification(
        title: title,
        body: body,
      ),
      sentTime: DateTime.now(),
    );
  }

  /// Display test notification
  static Future<Map<String, dynamic>> _displayTestNotification(
      RemoteMessage message) async {
    try {
      // Use the actual Firebase messaging service to display the notification
      FirebaseMessagingService().showFlutterNotification(message);

      if (kDebugMode) {
        print('📱 Test notification displayed successfully');
        print('📋 Title: ${message.notification?.title}');
        print('📄 Body: ${message.notification?.body}');
      }

      return {
        'success': true,
        'message': 'Notification displayed successfully',
        'notification_id': message.messageId,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to display test notification: $e');
      }

      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Test specific scenario
  static Future<Map<String, dynamic>> _testScenario(String scenario) async {
    switch (scenario) {
      case 'coach_table_with_assignments':
        return await testCoachTableDisplay(hasCoachAssignments: true);

      case 'coach_table_without_assignments':
        return await testCoachTableDisplay(hasCoachAssignments: false);

      case 'single_station_update':
        return await testCoachTableDisplay(
          hasCoachAssignments: true,
          customStations: [_createSingleStationData()],
        );

      case 'multi_station_update':
        return await testCoachTableDisplay(
          hasCoachAssignments: true,
          customStations: _createMultiStationData(),
        );

      case 'no_passenger_activity':
        return await testNotificationDisplay(
          testType: 'no_activity',
          customTitle: 'Train 12157 - No Activity',
          customBody: 'No passenger onboarding/deboarding at station ARA.',
        );

      case 'high_passenger_activity':
        return await testCoachTableDisplay(
          hasCoachAssignments: true,
          customStations: _createHighActivityStationData(),
        );

      default:
        throw Exception('Unknown scenario: $scenario');
    }
  }

  /// Create test station data
  static List<Map<String, dynamic>> _createTestStationData(
      bool hasCoachAssignments) {
    if (hasCoachAssignments) {
      return [
        {
          'station_code': 'ARA',
          'coaches': [
            {
              'coach_number': 'A1',
              'onboarding_count': 5,
              'off_boarding_count': 3,
              'vacant_count': 2
            },
            {
              'coach_number': 'B3',
              'onboarding_count': 6,
              'off_boarding_count': 3,
              'vacant_count': 6
            },
          ],
        },
        {
          'station_code': 'BTA',
          'coaches': [
            {
              'coach_number': 'A1',
              'onboarding_count': 2,
              'off_boarding_count': 4,
              'vacant_count': 4
            },
            {
              'coach_number': 'B3',
              'onboarding_count': 3,
              'off_boarding_count': 2,
              'vacant_count': 8
            },
          ],
        },
      ];
    } else {
      return [
        {'station_code': 'ARA'},
        {'station_code': 'BTA'},
        {'station_code': 'DNR'},
      ];
    }
  }

  /// Create single station test data
  static Map<String, dynamic> _createSingleStationData() {
    return {
      'station_code': 'PNBE',
      'coaches': [
        {
          'coach_number': 'A1',
          'onboarding_count': 8,
          'off_boarding_count': 5,
          'vacant_count': 3
        },
      ],
    };
  }

  /// Create multi-station test data
  static List<Map<String, dynamic>> _createMultiStationData() {
    return [
      {
        'station_code': 'ARA',
        'coaches': [
          {
            'coach_number': 'A1',
            'onboarding_count': 5,
            'off_boarding_count': 3,
            'vacant_count': 2
          },
        ],
      },
      {
        'station_code': 'BTA',
        'coaches': [
          {
            'coach_number': 'A1',
            'onboarding_count': 2,
            'off_boarding_count': 4,
            'vacant_count': 4
          },
        ],
      },
      {
        'station_code': 'DNR',
        'coaches': [
          {
            'coach_number': 'A1',
            'onboarding_count': 6,
            'off_boarding_count': 2,
            'vacant_count': 1
          },
        ],
      },
    ];
  }

  /// Create high activity station data
  static List<Map<String, dynamic>> _createHighActivityStationData() {
    return [
      {
        'station_code': 'PNBE',
        'coaches': [
          {
            'coach_number': 'A1',
            'onboarding_count': 15,
            'off_boarding_count': 12,
            'vacant_count': 8
          },
          {
            'coach_number': 'B3',
            'onboarding_count': 18,
            'off_boarding_count': 10,
            'vacant_count': 5
          },
          {
            'coach_number': 'C2',
            'onboarding_count': 12,
            'off_boarding_count': 15,
            'vacant_count': 10
          },
        ],
      },
    ];
  }

  /// Build test coach table
  static String _buildTestCoachTable(
      List<Map<String, dynamic>> stations, bool hasCoachAssignments) {
    if (!hasCoachAssignments) {
      final stationCodes = stations.map((s) => s['station_code']).join(', ');
      return 'Train update for stations $stationCodes.\n\nNo coach assignments - contact admin for access.';
    }

    String table = 'Station | Coach | Board | Deboard | Vacant\n';
    table += '--------|-------|-------|---------|-------\n';

    for (final station in stations) {
      final coaches = station['coaches'] as List<dynamic>? ?? [];
      for (final coach in coaches) {
        final stationCode = station['station_code'];
        final coachNumber = coach['coach_number'];
        final onboarding = coach['onboarding_count'];
        final offBoarding = coach['off_boarding_count'];
        final vacant = coach['vacant_count'];

        table +=
            '$stationCode | $coachNumber | $onboarding | $offBoarding | $vacant\n';
      }
    }

    return table;
  }

  /// Get device information for testing context
  static Future<Map<String, dynamic>> _getDeviceInfo() async {
    // This would typically use device_info_plus package
    // For now, return basic information
    return {
      'platform': 'Android',
      'test_environment': kDebugMode ? 'Debug' : 'Release',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
