import 'package:flutter/foundation.dart';
import 'package:railops/models/index.dart';
import 'package:railops/services/api_services/index.dart';
import 'package:railops/services/profile_services/profile_train_services.dart';
import 'package:railops/types/profile_types/profile_response.dart';
import 'package:railops/types/profile_types/request_profile.dart';

/// Custom exception for location service errors
class LocationServiceException implements Exception {
  final String message;
  final String errorCode;

  const LocationServiceException(this.message, this.errorCode);

  @override
  String toString() => 'LocationServiceException: $message (Code: $errorCode)';
}

class LocationService {
  /// Enhanced location upload with validation and error handling
  /// Checks inside train status before uploading GPS coordinates
  static Future<String> addCurrentUserLocation(
    final String token,
    String latitude,
    String longitude,
  ) async {
    try {
      if (kDebugMode) {
        debugPrint('🔍 LocationService: Attempting to upload GPS coordinates');
        debugPrint('🔍 LocationService: Lat: $latitude, Lng: $longitude');
      }

      // First, check if user is inside train
      final insideTrainStatus = await _validateInsideTrainStatus(token);

      if (!insideTrainStatus['is_inside_train']) {
        final errorMessage =
            'User not inside train. Current status: ${insideTrainStatus['status_message']}';
        if (kDebugMode) {
          debugPrint('❌ LocationService: $errorMessage');
        }
        throw LocationServiceException(errorMessage, 'USER_NOT_INSIDE_TRAIN');
      }

      if (kDebugMode) {
        debugPrint(
            '✅ LocationService: User is inside train ${insideTrainStatus['train_number']}');
      }

      final responseJson = await ApiService.post(
        '/location/add_current_user_location/',
        {'token': token, 'latitude': latitude, 'longitude': longitude},
      );

      if (kDebugMode) {
        debugPrint('✅ LocationService: GPS upload successful');
      }

      return responseJson['message'];
    } on LocationServiceException {
      // Re-throw our custom exceptions
      rethrow;
    } on ApiException catch (e) {
      if (kDebugMode) {
        debugPrint('❌ LocationService: API error - ${e.message}');
      }

      // Handle specific error cases
      if (e.message.toLowerCase().contains('not inside train') ||
          e.message.toLowerCase().contains('user not assigned')) {
        throw LocationServiceException(
            'Location upload failed: ${e.message}. Please ensure you are assigned to a train and have toggled "Inside Train" status.',
            'USER_NOT_INSIDE_TRAIN');
      }

      throw LocationServiceException(e.message, 'API_ERROR');
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ LocationService: Unexpected error - $e');
      }
      throw LocationServiceException(
          'Unexpected error during location upload: $e', 'UNKNOWN_ERROR');
    }
  }

  /// Validate if user is inside train before uploading location
  static Future<Map<String, dynamic>> _validateInsideTrainStatus(
      String token) async {
    try {
      final status = await ProfileTrainServices.getInsideTrainStatus(token);

      final isInsideTrain =
          status['inside_train'] == true || status['inside_train'] == 'true';
      final trainNumber = status['inside_train_number'] ?? '';
      final trainDate = status['inside_train_date'] ?? '';

      if (kDebugMode) {
        debugPrint('🔍 LocationService: Inside train status check:');
        debugPrint('   - Inside train: $isInsideTrain');
        debugPrint('   - Train number: $trainNumber');
        debugPrint('   - Train date: $trainDate');
      }

      return {
        'is_inside_train': isInsideTrain,
        'train_number': trainNumber,
        'train_date': trainDate,
        'status_message': isInsideTrain
            ? 'Inside train $trainNumber on $trainDate'
            : 'Not inside any train. Please toggle "Inside Train" status in the app.',
      };
    } catch (e) {
      if (kDebugMode) {
        debugPrint(
            '❌ LocationService: Error checking inside train status - $e');
      }

      return {
        'is_inside_train': false,
        'train_number': '',
        'train_date': '',
        'status_message': 'Could not verify inside train status: $e',
      };
    }
  }

  static Future<String> saveTrainLocation(
    final String token,
    String latitude,
    String longitude,
  ) async {
    try {
      final responseJson = await ApiService.post(
        '/location/save_train_location/',
        {'token': token, 'latitude': latitude, 'longitude': longitude},
        
      );
          print('Location post response: $responseJson');

      
      return responseJson['message'];
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw '$e';
    }
  }

  static Future<Map<String, dynamic>> getNearestStation(
      String latitude, String longitude) async {
    try {
      final responseJson = await ApiService.get(
          '/microservice/station/nearest/?latitude=$latitude&longitude=$longitude',
          {});
      return responseJson;
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw '$e';
    }
  }

  /// Helper method to check inside train status and provide guidance
  /// Returns detailed status information for debugging and user guidance
  static Future<Map<String, dynamic>> checkInsideTrainStatus(
      String token) async {
    try {
      final status = await _validateInsideTrainStatus(token);

      return {
        'success': true,
        'is_inside_train': status['is_inside_train'],
        'train_number': status['train_number'],
        'train_date': status['train_date'],
        'status_message': status['status_message'],
        'can_upload_location': status['is_inside_train'],
        'guidance': status['is_inside_train']
            ? 'Location uploads are enabled. GPS coordinates will be uploaded every 15 minutes.'
            : 'To enable location uploads:\n1. Go to Train Details screen\n2. Select your assigned train\n3. Toggle "Inside Train" status to ON\n4. Ensure you have train assignments for the current date',
      };
    } catch (e) {
      return {
        'success': false,
        'is_inside_train': false,
        'error': e.toString(),
        'guidance':
            'Error checking train status. Please check your network connection and try again.',
      };
    }
  }

  /// Helper method to attempt to fix common location upload issues
  /// Provides step-by-step guidance for resolving "user not inside train" errors
  static Future<Map<String, dynamic>> diagnoseLocationUploadIssues(
      String token) async {
    try {
      final diagnosis = <String, dynamic>{
        'issues_found': <String>[],
        'recommendations': <String>[],
        'can_auto_fix': false,
      };

      // Check inside train status
      final insideTrainStatus = await _validateInsideTrainStatus(token);

      if (!insideTrainStatus['is_inside_train']) {
        diagnosis['issues_found'].add('User not marked as inside train');
        diagnosis['recommendations']
            .add('Toggle "Inside Train" status in Train Details screen');
      }

      // Check if train number is set
      final trainNumber = insideTrainStatus['train_number'] as String;
      if (trainNumber.isEmpty) {
        diagnosis['issues_found'].add('No train number assigned');
        diagnosis['recommendations'].add(
            'Select and assign yourself to a train in Train Details screen');
      }

      // Check if train date is current
      final trainDate = insideTrainStatus['train_date'] as String;
      if (trainDate.isNotEmpty) {
        try {
          final assignedDate = DateTime.parse(trainDate);
          final today = DateTime.now();
          final daysDifference = today.difference(assignedDate).inDays;

          if (daysDifference.abs() > 1) {
            diagnosis['issues_found'].add(
                'Train assignment date mismatch (assigned: $trainDate, today: ${today.toString().split(' ')[0]})');
            diagnosis['recommendations']
                .add('Update train assignment to current date');
          }
        } catch (e) {
          diagnosis['issues_found']
              .add('Invalid train date format: $trainDate');
          diagnosis['recommendations'].add('Re-assign train with correct date');
        }
      }

      // Provide overall assessment
      final issuesFound = diagnosis['issues_found'] as List<String>;
      diagnosis['summary'] = issuesFound.isEmpty
          ? 'No issues found. Location uploads should work normally.'
          : 'Found ${issuesFound.length} issue(s) preventing location uploads.';

      return {
        'success': true,
        'diagnosis': diagnosis,
        'current_status': insideTrainStatus,
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'diagnosis': {
          'issues_found': ['Error during diagnosis: $e'],
          'recommendations': ['Check network connection and try again'],
          'summary': 'Diagnosis failed due to error',
        },
      };
    }
  }
}
