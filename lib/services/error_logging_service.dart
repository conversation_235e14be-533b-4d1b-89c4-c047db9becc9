import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// Log levels for different types of issues
enum LogLevel {
  debug,
  info,
  warning,
  error,
  critical,
}

/// Comprehensive error logging service for the notification pipeline
/// Provides structured logging with different severity levels and context
class ErrorLoggingService {
  static const String _errorLogKey = 'notification_error_logs';
  static const int maxLogEntries = 100; // Keep last 100 error entries

  /// Log an error with context and severity level
  static Future<void> logError({
    required String component,
    required String message,
    required LogLevel level,
    String? stackTrace,
    Map<String, dynamic>? context,
    Exception? exception,
  }) async {
    try {
      final logEntry = {
        'timestamp': DateTime.now().toIso8601String(),
        'level': level.name,
        'component': component,
        'message': message,
        'stack_trace': stackTrace ?? exception?.toString(),
        'context': context ?? {},
        'exception_type': exception?.runtimeType.toString(),
      };

      // Console logging for debug mode
      if (kDebugMode) {
        final emoji = _getLogEmoji(level);
        debugPrint('$emoji [$component] ${level.name.toUpperCase()}: $message');
        if (context != null && context.isNotEmpty) {
          debugPrint('   Context: ${jsonEncode(context)}');
        }
        if (stackTrace != null) {
          debugPrint('   Stack: $stackTrace');
        }
      }

      // Store in SharedPreferences for debugging
      await _storeLogEntry(logEntry);
    } catch (e) {
      // Fallback logging if the logging service itself fails
      if (kDebugMode) {
        debugPrint('❌ ErrorLoggingService failed to log error: $e');
        debugPrint('   Original error: $message');
      }
    }
  }

  /// Get emoji for log level
  static String _getLogEmoji(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return '🔍';
      case LogLevel.info:
        return 'ℹ️';
      case LogLevel.warning:
        return '⚠️';
      case LogLevel.error:
        return '❌';
      case LogLevel.critical:
        return '🚨';
    }
  }

  /// Store log entry in SharedPreferences
  static Future<void> _storeLogEntry(Map<String, dynamic> logEntry) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final existingLogs = prefs.getStringList(_errorLogKey) ?? [];

      // Add new log entry
      existingLogs.add(jsonEncode(logEntry));

      // Keep only the last N entries to prevent storage bloat
      if (existingLogs.length > maxLogEntries) {
        existingLogs.removeRange(0, existingLogs.length - maxLogEntries);
      }

      await prefs.setStringList(_errorLogKey, existingLogs);
    } catch (e) {
      // Silent failure for storage issues
      if (kDebugMode) {
        debugPrint('Failed to store log entry: $e');
      }
    }
  }

  /// Retrieve stored error logs for debugging
  static Future<List<Map<String, dynamic>>> getStoredLogs({
    LogLevel? minLevel,
    String? component,
    DateTime? since,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final logStrings = prefs.getStringList(_errorLogKey) ?? [];

      final logs = logStrings
          .map((logString) {
            try {
              return jsonDecode(logString) as Map<String, dynamic>;
            } catch (e) {
              return null;
            }
          })
          .where((log) => log != null)
          .cast<Map<String, dynamic>>()
          .toList();

      // Apply filters
      return logs.where((log) {
        // Filter by minimum log level
        if (minLevel != null) {
          final logLevel = LogLevel.values.firstWhere(
            (level) => level.name == log['level'],
            orElse: () => LogLevel.debug,
          );
          if (logLevel.index < minLevel.index) return false;
        }

        // Filter by component
        if (component != null && log['component'] != component) return false;

        // Filter by timestamp
        if (since != null) {
          try {
            final logTime = DateTime.parse(log['timestamp']);
            if (logTime.isBefore(since)) return false;
          } catch (e) {
            // Skip logs with invalid timestamps
            return false;
          }
        }

        return true;
      }).toList();
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error retrieving stored logs: $e');
      }
      return [];
    }
  }

  /// Clear all stored logs
  static Future<void> clearLogs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_errorLogKey);

      if (kDebugMode) {
        debugPrint('🗑️ Cleared all stored error logs');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error clearing logs: $e');
      }
    }
  }

  /// Get log summary for debugging
  static Future<Map<String, dynamic>> getLogSummary() async {
    try {
      final logs = await getStoredLogs();
      final summary = <String, dynamic>{
        'total_logs': logs.length,
        'by_level': <String, int>{},
        'by_component': <String, int>{},
        'recent_errors': <Map<String, dynamic>>[],
      };

      // Count by level
      for (final level in LogLevel.values) {
        summary['by_level'][level.name] =
            logs.where((log) => log['level'] == level.name).length;
      }

      // Count by component
      final components = logs.map((log) => log['component'] as String).toSet();
      for (final component in components) {
        summary['by_component'][component] =
            logs.where((log) => log['component'] == component).length;
      }

      // Get recent errors (last 5)
      final recentErrors = logs
          .where((log) =>
              log['level'] == LogLevel.error.name ||
              log['level'] == LogLevel.critical.name)
          .take(5)
          .toList();
      summary['recent_errors'] = recentErrors;

      return summary;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('Error generating log summary: $e');
      }
      return {
        'total_logs': 0,
        'error': e.toString(),
      };
    }
  }

  // Convenience methods for different log levels

  static Future<void> logDebug(String component, String message,
      {Map<String, dynamic>? context}) async {
    await logError(
      component: component,
      message: message,
      level: LogLevel.debug,
      context: context,
    );
  }

  static Future<void> logInfo(String component, String message,
      {Map<String, dynamic>? context}) async {
    await logError(
      component: component,
      message: message,
      level: LogLevel.info,
      context: context,
    );
  }

  static Future<void> logWarning(String component, String message,
      {Map<String, dynamic>? context}) async {
    await logError(
      component: component,
      message: message,
      level: LogLevel.warning,
      context: context,
    );
  }

  static Future<void> logCritical(
    String component,
    String message, {
    Exception? exception,
    String? stackTrace,
    Map<String, dynamic>? context,
  }) async {
    await logError(
      component: component,
      message: message,
      level: LogLevel.critical,
      exception: exception,
      stackTrace: stackTrace,
      context: context,
    );
  }
}
