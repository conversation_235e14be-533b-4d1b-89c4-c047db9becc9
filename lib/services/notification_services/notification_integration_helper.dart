import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:railops/services/train_services/upcoming_station_service.dart';
import 'package:railops/types/train_types/upcoming_station_response.dart';
import 'package:railops/services/authentication_services/jwt_service.dart';
import 'package:railops/services/firebase_cloud_function_service.dart';
import 'package:railops/services/fcm_token_service.dart';

/// Helper class for Firebase Cloud Function notification integration
class NotificationIntegrationHelper {
  // ✅ Local stats storage
  static final Map<String, dynamic> _notificationStats = {
    'processed_notifications': 0,
    'last_sent_title': '',
    'last_sent_body': '',
  };

  /// ✅ Retrieve basic stats for debugging/testing
  static Map<String, dynamic> getNotificationStats() => _notificationStats;

  /// ✅ Used for Phase 1: Configuring time-based and station-sequence triggers
  static void configureNotificationTiming({
    required Duration boardingNotificationDelay,
    required Duration offBoardingNotificationDelay,
    required int stationsBeforeAlert,
  }) {
    if (kDebugMode) {
      print('🔧 Phase 1 Notification Timing Configured:');
      print('- Boarding Delay: $boardingNotificationDelay');
      print('- Offboarding Delay: $offBoardingNotificationDelay');
      print('- Stations Before Alert: $stationsBeforeAlert');
    }
  }

  /// ✅ Simple test notification method for Phase 1 test cases
  static Future<void> sendTestNotification({
    required String title,
    required String body,
  }) async {
    if (kDebugMode) {
      print('📨 Test Notification: "$title" → $body');
    }

    // Simulate processing
    _notificationStats['processed_notifications'] =
        (_notificationStats['processed_notifications'] ?? 0) + 1;
    _notificationStats['last_sent_title'] = title;
    _notificationStats['last_sent_body'] = body;

    await Future.delayed(const Duration(milliseconds: 300));
  }

  /// ✅ Phase 2: Proximity-based configuration
  static void configureProximityNotifications({
    required double proximityThresholdKm,
    required Duration locationUpdateInterval,
    required bool enableLocationMonitoring,
  }) {
    if (kDebugMode) {
      print('🔧 Configured Proximity Notifications:');
      print('- Threshold: $proximityThresholdKm km');
      print('- Update Interval: $locationUpdateInterval');
      print('- Location Monitoring Enabled: $enableLocationMonitoring');
    }
  }

  /// ✅ Phase 2: Passenger count-based notifications
  static void configurePassengerCountNotifications({
    required int passengerCountThreshold,
    required bool enableBoardingCountUpdates,
    required bool enableOffBoardingPreparation,
  }) {
    if (kDebugMode) {
      print('🔧 Configured Passenger Count Notifications:');
      print('- Threshold: $passengerCountThreshold');
      print('- Boarding Count Updates: $enableBoardingCountUpdates');
      print('- Off-Boarding Prep: $enableOffBoardingPreparation');
    }
  }

  /// ✅ Phase 2: Train status monitoring
  static void configureTrainStatusMonitoring({
    required List<String> monitoredStatusTypes,
    required Duration statusCheckInterval,
    required bool enableScheduleChangeAlerts,
  }) {
    if (kDebugMode) {
      print('🔧 Configured Train Status Monitoring:');
      print('- Status Types: $monitoredStatusTypes');
      print('- Interval: $statusCheckInterval');
      print('- Schedule Change Alerts: $enableScheduleChangeAlerts');
    }
  }

  /// ✅ Phase 2: Send mock/test proximity alert
  static Future<void> sendTestProximityNotification({
    required String stationName,
    required double distanceKm,
  }) async {
    if (kDebugMode) {
      print('📣 Test Proximity Alert: $stationName - $distanceKm km');
    }
  }

  /// ✅ Phase 2: Station approach mock/test
  static Future<void> sendTestStationApproachAlert({
    required String stationName,
    required int minutesBeforeArrival,
  }) async {
    if (kDebugMode) {
      print('📣 Test Station Approach: $stationName arriving in $minutesBeforeArrival min');
    }
  }

  /// ✅ Phase 2: Train status update mock/test
  static Future<void> sendTestTrainStatusUpdate({
    required String trainNumber,
    required String statusType,
    required String statusMessage,
  }) async {
    if (kDebugMode) {
      print('📣 Train Status: $trainNumber - $statusType - $statusMessage');
    }
  }

  /// ✅ Phase 2: Boarding count update mock/test
  static Future<void> sendTestBoardingCountUpdate({
    required String stationName,
    required int currentCount,
    required int previousCount,
    required List<String> coaches,
  }) async {
    if (kDebugMode) {
      print('📣 Boarding Count Update: $stationName');
      print('- Count: $previousCount → $currentCount');
      print('- Coaches: ${coaches.join(', ')}');
    }
  }

  /// ✅ Utility: Get context with real GPS, auth, train, etc.
  static Future<Map<String, dynamic>> getRealUserContext() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      String? userId = await JwtService.getStoredUserId();
      userId ??= prefs.getString('user_id');

      final trainNumber = prefs.getString('trainNo') ?? '';
      final authToken = prefs.getString('authToken') ?? '';
      final currentDate = FirebaseCloudFunctionService.getCurrentDateString();

      Position? position;
      try {
        LocationPermission permission = await Geolocator.checkPermission();
        if (permission == LocationPermission.denied) {
          permission = await Geolocator.requestPermission();
        }

        if (permission == LocationPermission.whileInUse ||
            permission == LocationPermission.always) {
          position = await Geolocator.getCurrentPosition(
            locationSettings: const LocationSettings(
              accuracy: LocationAccuracy.medium,
            ),
          );
        }
      } catch (e) {
        if (kDebugMode) print('❌ Location Error: $e');
      }

      return {
        'user_id': userId,
        'train_number': trainNumber,
        'auth_token': authToken,
        'current_date': currentDate,
        'latitude': position?.latitude.toString() ?? '28.6139',
        'longitude': position?.longitude.toString() ?? '77.2090',
        'has_real_location': position != null,
        'has_user_context':
            (userId?.isNotEmpty ?? false) && trainNumber.isNotEmpty,
      };
    } catch (e) {
      if (kDebugMode) print('❌ User Context Error: $e');
      return {
        'user_id': null,
        'train_number': '',
        'auth_token': '',
        'current_date': FirebaseCloudFunctionService.getCurrentDateString(),
        'latitude': '28.6139',
        'longitude': '77.2090',
        'has_real_location': false,
        'has_user_context': false,
        'error': e.toString(),
      };
    }
  }

  /// ✅ Full pipeline call test to Firebase Cloud Function
  static Future<Map<String, dynamic>> testRealNotificationPipeline({
    String? customTrainNumber,
    String? customLat,
    String? customLng,
  }) async {
    try {
      final userContext = await getRealUserContext();

      if (!userContext['has_user_context']) {
        return {
          'success': false,
          'error': 'Missing user context',
          'details':
              'User ID or train number not found. Please ensure you are logged in and have a train assignment.',
          'user_context': userContext,
        };
      }

      final userId = userContext['user_id'] as String;
      final trainNumber =
          customTrainNumber ?? userContext['train_number'] as String;
      final lat = customLat ?? userContext['latitude'] as String;
      final lng = customLng ?? userContext['longitude'] as String;
      final currentDate = userContext['current_date'] as String;

      if (kDebugMode) {
        print('📍 Coordinates: $lat, $lng');
        print('🚆 Train: $trainNumber');
        print('👤 User: $userId');
        print('📅 Date: $currentDate');
      }

      String? fcmToken;
      try {
        fcmToken = await FcmTokenService.getFcmToken();
      } catch (e) {
        if (kDebugMode) print('⚠️ FCM Token Error: $e');
      }

      final result = await FirebaseCloudFunctionService.callNotifyFunction(
        userId: userId,
        trainNumber: trainNumber,
        date: currentDate,
        lat: lat,
        lng: lng,
        fcmToken: fcmToken,
      );

      return {
        'success': result['status'] == 'sent',
        'status': result['status'],
        'message': result['message'],
        'details': result['details'],
        'user_context': userContext,
        'request_data': {
          'user_id': userId,
          'train_number': trainNumber,
          'date': currentDate,
          'coordinates': '$lat, $lng',
          'fcm_token_available': fcmToken != null,
        },
      };
    } catch (e) {
      if (kDebugMode) print('❌ Pipeline Error: $e');
      return {
        'success': false,
        'error': 'Pipeline test failed',
        'details': e.toString(),
      };
    }
  }
}
