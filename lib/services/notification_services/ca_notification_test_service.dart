import 'package:flutter/foundation.dart';
import 'package:railops/services/firebase_cloud_function_service.dart';
import 'package:railops/services/fcm_token_service.dart';
import 'package:railops/services/firestore_token_service.dart';
import 'package:railops/services/notification_services/notification_integration_helper.dart';

/// Specialized service for CA/CS/EHK notification testing
///
/// This service provides comprehensive testing capabilities for:
/// - Multi-station proximity notifications
/// - Multi-coach assignment notifications
/// - Anti-duplication logic verification
/// - No passenger activity scenarios
/// - Real-time notification delivery testing
class CANotificationTestService {
  static const Map<String, Map<String, String>> _stationCoordinates = {
    'ARA': {'lat': '25.5500', 'lng': '84.6667'},
    'BTA': {'lat': '25.2167', 'lng': '84.3667'},
    'DNR': {'lat': '25.4167', 'lng': '85.0167'},
    'PNBE': {'lat': '25.5941', 'lng': '85.1376'},
    'RJPB': {'lat': '25.6093', 'lng': '85.1947'},
    'PNC': {'lat': '25.6167', 'lng': '85.2167'},
    'DDU': {'lat': '25.4167', 'lng': '82.9833'},
    'NEW_DELHI': {'lat': '28.6139', 'lng': '77.2090'},
  };

  static const List<String> _testRoute = [
    'ARA',
    'BTA',
    'DNR',
    'PNBE',
    'RJPB',
    'PNC'
  ];
  static const List<String> _defaultCoaches = ['A1', 'B3'];

  /// Execute comprehensive CA/CS/EHK notification test suite
  static Future<Map<String, dynamic>> executeComprehensiveTestSuite({
    String trainNumber = 'TEST12157',
    String userId = 'test_ca_001',
    List<String> coaches = _defaultCoaches,
  }) async {
    final results = <String, dynamic>{
      'test_suite': 'CA/CS/EHK Comprehensive',
      'start_time': DateTime.now().toIso8601String(),
      'train_number': trainNumber,
      'user_id': userId,
      'coaches': coaches,
      'scenarios': <String, dynamic>{},
    };

    try {
      if (kDebugMode) {
        print('🧪 Starting CA/CS/EHK Comprehensive Test Suite');
        print(
            '🚂 Train: $trainNumber, User: $userId, Coaches: ${coaches.join(', ')}');
      }

      // Scenario 1: Multi-Station Proximity Testing
      results['scenarios']['multi_station_proximity'] =
          await _testMultiStationProximity(trainNumber, userId, coaches);

      // Scenario 2: Multi-Coach Assignment Testing
      results['scenarios']['multi_coach_assignment'] =
          await _testMultiCoachAssignment(trainNumber, userId, coaches);

      // Scenario 3: Anti-Duplication Logic Testing
      results['scenarios']['anti_duplication'] =
          await _testAntiDuplicationLogic(trainNumber, userId, coaches);

      // Scenario 4: No Passenger Activity Testing
      results['scenarios']['no_passenger_activity'] =
          await _testNoPassengerActivity(trainNumber, userId, coaches);

      results['status'] = 'completed';
      results['end_time'] = DateTime.now().toIso8601String();

      if (kDebugMode) {
        print('✅ CA/CS/EHK Comprehensive Test Suite completed successfully');
      }
    } catch (e) {
      results['status'] = 'failed';
      results['error'] = e.toString();
      results['end_time'] = DateTime.now().toIso8601String();

      if (kDebugMode) {
        print('❌ CA/CS/EHK Comprehensive Test Suite failed: $e');
      }
    }

    return results;
  }

  /// Test multi-station proximity notifications along the route
  static Future<Map<String, dynamic>> _testMultiStationProximity(
    String trainNumber,
    String userId,
    List<String> coaches,
  ) async {
    final results = <String, dynamic>{
      'scenario': 'Multi-Station Proximity',
      'route': _testRoute,
      'stations_tested': <String, dynamic>{},
    };

    try {
      if (kDebugMode) {
        print('🧪 Testing Multi-Station Proximity: ${_testRoute.join(' → ')}');
      }

      for (final station in _testRoute) {
        final coordinates = _stationCoordinates[station];
        if (coordinates != null) {
          final stationResult = await _callNotifyEndpoint(
            station: station,
            lat: coordinates['lat']!,
            lng: coordinates['lng']!,
            trainNumber: trainNumber,
            userId: userId,
            passengerData: _generateTestPassengerData(coaches),
          );

          results['stations_tested'][station] = stationResult;

          if (kDebugMode) {
            print(
                '📍 $station: ${stationResult['status']} - ${stationResult['message']}');
          }

          // Small delay between station tests
          await Future.delayed(const Duration(milliseconds: 500));
        }
      }

      final sentCount = (results['stations_tested'] as Map)
          .values
          .where((r) => r['status'] == 'sent')
          .length;
      final skippedCount = (results['stations_tested'] as Map)
          .values
          .where((r) => r['status'] == 'skipped')
          .length;

      results['summary'] = {
        'total_stations': _testRoute.length,
        'notifications_sent': sentCount,
        'notifications_skipped': skippedCount,
        'success_rate': sentCount / _testRoute.length,
      };

      results['status'] = 'completed';
    } catch (e) {
      results['status'] = 'failed';
      results['error'] = e.toString();
    }

    return results;
  }

  /// Test multi-coach assignment notifications
  static Future<Map<String, dynamic>> _testMultiCoachAssignment(
    String trainNumber,
    String userId,
    List<String> coaches,
  ) async {
    final results = <String, dynamic>{
      'scenario': 'Multi-Coach Assignment',
      'coaches': coaches,
      'test_station': 'DDU',
    };

    try {
      if (kDebugMode) {
        print(
            '🧪 Testing Multi-Coach Assignment for coaches: ${coaches.join(', ')}');
      }

      final coordinates = _stationCoordinates['DDU']!;
      final result = await _callNotifyEndpoint(
        station: 'DDU',
        lat: coordinates['lat']!,
        lng: coordinates['lng']!,
        trainNumber: trainNumber,
        userId: userId,
        passengerData: _generateMultiCoachTestData(coaches),
      );

      results['notification_result'] = result;
      results['expected_format'] = {
        'table_headers': [
          'StationCode',
          'Coach',
          'Onboarding',
          'Deboarding',
          'Vacant'
        ],
        'expected_rows': coaches
            .map((coach) => {
                  'station': 'DDU',
                  'coach': coach,
                  'onboarding': coach == 'A1' ? 5 : 6,
                  'deboarding': 3,
                  'vacant': coach == 'A1' ? 2 : 6,
                })
            .toList(),
      };

      results['status'] = result['status'] == 'sent' ? 'completed' : 'failed';

      if (kDebugMode) {
        print(
            '📊 Expected format: StationCode | Coach | Onboarding | Deboarding | Vacant');
        for (final coach in coaches) {
          final onboarding = coach == 'A1' ? 5 : 6;
          final vacant = coach == 'A1' ? 2 : 6;
          print('📊 DDU | $coach | $onboarding | 3 | $vacant');
        }
      }
    } catch (e) {
      results['status'] = 'failed';
      results['error'] = e.toString();
    }

    return results;
  }

  /// Test anti-duplication logic
  static Future<Map<String, dynamic>> _testAntiDuplicationLogic(
    String trainNumber,
    String userId,
    List<String> coaches,
  ) async {
    final results = <String, dynamic>{
      'scenario': 'Anti-Duplication Logic',
      'test_station': 'PNBE',
      'calls': <Map<String, dynamic>>[],
    };

    try {
      if (kDebugMode) {
        print('🧪 Testing Anti-Duplication Logic at PNBE');
      }

      final coordinates = _stationCoordinates['PNBE']!;

      // First call - should send
      final firstCall = await _callNotifyEndpoint(
        station: 'PNBE',
        lat: coordinates['lat']!,
        lng: coordinates['lng']!,
        trainNumber: trainNumber,
        userId: userId,
        passengerData: _generateTestPassengerData(coaches),
      );
      results['calls']
          .add({'call': 1, 'expected': 'sent', 'actual': firstCall});

      await Future.delayed(const Duration(seconds: 1));

      // Second call with same data - should be skipped
      final secondCall = await _callNotifyEndpoint(
        station: 'PNBE',
        lat: coordinates['lat']!,
        lng: coordinates['lng']!,
        trainNumber: trainNumber,
        userId: userId,
        passengerData: _generateTestPassengerData(coaches),
      );
      results['calls']
          .add({'call': 2, 'expected': 'skipped', 'actual': secondCall});

      await Future.delayed(const Duration(seconds: 1));

      // Third call with changed data - should send
      final thirdCall = await _callNotifyEndpoint(
        station: 'PNBE',
        lat: coordinates['lat']!,
        lng: coordinates['lng']!,
        trainNumber: trainNumber,
        userId: userId,
        passengerData: _generateChangedPassengerData(coaches),
      );
      results['calls']
          .add({'call': 3, 'expected': 'sent', 'actual': thirdCall});

      // Analyze results
      final antiDuplicationWorking = secondCall['status'] == 'skipped';
      results['anti_duplication_working'] = antiDuplicationWorking;
      results['status'] = antiDuplicationWorking ? 'completed' : 'failed';

      if (kDebugMode) {
        print('📋 Anti-duplication test results:');
        print('   First call: ${firstCall['status']} (expected: sent)');
        print('   Second call: ${secondCall['status']} (expected: skipped)');
        print('   Third call: ${thirdCall['status']} (expected: sent)');
        print(
            '   Anti-duplication logic: ${antiDuplicationWorking ? 'WORKING' : 'FAILED'}');
      }
    } catch (e) {
      results['status'] = 'failed';
      results['error'] = e.toString();
    }

    return results;
  }

  /// Test no passenger activity scenarios
  static Future<Map<String, dynamic>> _testNoPassengerActivity(
    String trainNumber,
    String userId,
    List<String> coaches,
  ) async {
    final results = <String, dynamic>{
      'scenario': 'No Passenger Activity',
      'test_stations': ['RJPB', 'PNC'],
    };

    try {
      if (kDebugMode) {
        print('🧪 Testing No Passenger Activity notifications');
      }

      final testStations = ['RJPB', 'PNC'];
      final stationResults = <String, dynamic>{};

      for (final station in testStations) {
        final coordinates = _stationCoordinates[station];
        if (coordinates != null) {
          final result = await _callNotifyEndpoint(
            station: station,
            lat: coordinates['lat']!,
            lng: coordinates['lng']!,
            trainNumber: trainNumber,
            userId: userId,
            passengerData: _generateNoActivityPassengerData(coaches),
          );

          stationResults[station] = result;

          if (kDebugMode) {
            print(
                '📍 $station (no activity): ${result['status']} - ${result['message']}');
          }

          await Future.delayed(const Duration(milliseconds: 500));
        }
      }

      results['station_results'] = stationResults;
      results['expected_message'] =
          'No passenger onboarding/deboarding at station ${testStations.join(', ')}';
      results['status'] = 'completed';
    } catch (e) {
      results['status'] = 'failed';
      results['error'] = e.toString();
    }

    return results;
  }

  /// Call the Firebase Cloud Function /notify endpoint
  static Future<Map<String, dynamic>> _callNotifyEndpoint({
    required String station,
    required String lat,
    required String lng,
    required String trainNumber,
    required String userId,
    required Map<String, Map<String, int>> passengerData,
  }) async {
    try {
      final fcmToken = await FcmTokenService.getFreshFcmToken() ??
          'test_fcm_token_${DateTime.now().millisecondsSinceEpoch}';

      final result = await FirebaseCloudFunctionService.callNotifyFunction(
        userId: userId,
        trainNumber: trainNumber,
        date: DateTime.now().toIso8601String().split('T')[0],
        lat: lat,
        lng: lng,
        fcmToken: fcmToken,
      );

      return result;
    } catch (e) {
      return {
        'status': 'error',
        'message': 'Failed to call notify endpoint: $e',
        'error': e.toString(),
      };
    }
  }

  /// Generate test passenger data for coaches
  static Map<String, Map<String, int>> _generateTestPassengerData(
      List<String> coaches) {
    final data = <String, Map<String, int>>{};
    for (final coach in coaches) {
      data[coach] = {
        'onboarding': 5,
        'deboarding': 3,
        'vacant': 2,
      };
    }
    return data;
  }

  /// Generate multi-coach specific test data
  static Map<String, Map<String, int>> _generateMultiCoachTestData(
      List<String> coaches) {
    return {
      'A1': {'onboarding': 5, 'deboarding': 3, 'vacant': 2},
      'B3': {'onboarding': 6, 'deboarding': 3, 'vacant': 6},
    };
  }

  /// Generate changed passenger data for anti-duplication testing
  static Map<String, Map<String, int>> _generateChangedPassengerData(
      List<String> coaches) {
    final data = <String, Map<String, int>>{};
    for (final coach in coaches) {
      data[coach] = {
        'onboarding': 7, // Changed
        'deboarding': 4, // Changed
        'vacant': 1, // Changed
      };
    }
    return data;
  }

  /// Generate no activity passenger data
  static Map<String, Map<String, int>> _generateNoActivityPassengerData(
      List<String> coaches) {
    final data = <String, Map<String, int>>{};
    for (final coach in coaches) {
      data[coach] = {
        'onboarding': 0,
        'deboarding': 0,
        'vacant': 10,
      };
    }
    return data;
  }

  /// Quick test for single station
  static Future<Map<String, dynamic>> testSingleStation({
    String station = 'NEW_DELHI',
    String trainNumber = 'TEST12157',
    String userId = 'test_ca_001',
    List<String> coaches = _defaultCoaches,
  }) async {
    try {
      if (kDebugMode) {
        print('🧪 Quick test for station: $station');
      }

      final coordinates = _stationCoordinates[station];
      if (coordinates == null) {
        throw Exception('Unknown station: $station');
      }

      final result = await _callNotifyEndpoint(
        station: station,
        lat: coordinates['lat']!,
        lng: coordinates['lng']!,
        trainNumber: trainNumber,
        userId: userId,
        passengerData: _generateTestPassengerData(coaches),
      );

      return {
        'test_type': 'single_station',
        'station': station,
        'coordinates': coordinates,
        'result': result,
        'status': 'completed',
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'test_type': 'single_station',
        'station': station,
        'status': 'failed',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Test FCM token functionality
  static Future<Map<String, dynamic>> testFCMTokenFunctionality() async {
    try {
      if (kDebugMode) {
        print('🧪 Testing FCM Token functionality');
      }

      final token = await FcmTokenService.getFreshFcmToken();
      final firestoreSync = await FirestoreTokenService.syncWithFirestore();

      return {
        'test_type': 'fcm_token',
        'token_available': token != null,
        'token_length': token?.length ?? 0,
        'firestore_sync': firestoreSync,
        'token_preview':
            token?.substring(0, token.length > 20 ? 20 : token.length),
        'status': 'completed',
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'test_type': 'fcm_token',
        'status': 'failed',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
}
