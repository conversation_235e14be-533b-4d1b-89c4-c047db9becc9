import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'dart:convert';

/// Notification Quality Validator
///
/// This service validates notification content quality and formatting
/// to ensure proper display of train data (station codes, coach information,
/// passenger counts) in readable table format for both users with and
/// without coach assignments.
class NotificationQualityValidator {
  /// Validate notification content quality
  static Map<String, dynamic> validateNotificationContent(
      RemoteMessage message) {
    final validationResults = <String, dynamic>{
      'overall_quality': 'unknown',
      'title_quality': 'unknown',
      'body_quality': 'unknown',
      'data_quality': 'unknown',
      'table_format_quality': 'unknown',
      'coach_assignment_handling': 'unknown',
      'issues': <String>[],
      'recommendations': <String>[],
      'score': 0.0,
    };

    try {
      // Validate title
      final titleValidation = _validateTitle(message.notification?.title);
      validationResults['title_quality'] = titleValidation['quality'];
      if (titleValidation['issues'].isNotEmpty) {
        validationResults['issues'].addAll(titleValidation['issues']);
      }

      // Validate body content
      final bodyValidation = _validateBody(message.notification?.body);
      validationResults['body_quality'] = bodyValidation['quality'];
      if (bodyValidation['issues'].isNotEmpty) {
        validationResults['issues'].addAll(bodyValidation['issues']);
      }

      // Validate data payload
      final dataValidation = _validateDataPayload(message.data);
      validationResults['data_quality'] = dataValidation['quality'];
      if (dataValidation['issues'].isNotEmpty) {
        validationResults['issues'].addAll(dataValidation['issues']);
      }

      // Validate table format (if applicable)
      final tableValidation =
          _validateTableFormat(message.notification?.body, message.data);
      validationResults['table_format_quality'] = tableValidation['quality'];
      if (tableValidation['issues'].isNotEmpty) {
        validationResults['issues'].addAll(tableValidation['issues']);
      }

      // Validate coach assignment handling
      final coachValidation = _validateCoachAssignmentHandling(message.data);
      validationResults['coach_assignment_handling'] =
          coachValidation['quality'];
      if (coachValidation['issues'].isNotEmpty) {
        validationResults['issues'].addAll(coachValidation['issues']);
      }

      // Calculate overall quality score
      final qualityScores = [
        _qualityToScore(validationResults['title_quality']),
        _qualityToScore(validationResults['body_quality']),
        _qualityToScore(validationResults['data_quality']),
        _qualityToScore(validationResults['table_format_quality']),
        _qualityToScore(validationResults['coach_assignment_handling']),
      ];

      final averageScore =
          qualityScores.reduce((a, b) => a + b) / qualityScores.length;
      validationResults['score'] = averageScore;
      validationResults['overall_quality'] = _scoreToQuality(averageScore);

      // Generate recommendations
      validationResults['recommendations'] =
          _generateRecommendations(validationResults);

      if (kDebugMode) {
        print('📊 Notification Quality Validation Complete');
        print('🎯 Overall Quality: ${validationResults['overall_quality']}');
        print('📈 Score: ${(averageScore * 100).toStringAsFixed(1)}%');
      }
    } catch (e) {
      validationResults['overall_quality'] = 'error';
      validationResults['issues'].add('Validation error: $e');

      if (kDebugMode) {
        print('❌ Notification quality validation failed: $e');
      }
    }

    return validationResults;
  }

  /// Validate notification title quality
  static Map<String, dynamic> _validateTitle(String? title) {
    final issues = <String>[];
    String quality = 'good';

    if (title == null || title.isEmpty) {
      issues.add('Title is missing or empty');
      quality = 'poor';
    } else {
      // Check title length
      if (title.length > 65) {
        issues
            .add('Title too long (${title.length} chars, max 65 recommended)');
        quality = 'fair';
      }

      // Check for train information
      if (!title
          .contains(RegExp(r'Train \d+|train \d+', caseSensitive: false))) {
        issues.add('Title missing train number information');
        if (quality == 'good') quality = 'fair';
      }

      // Check for station information
      if (!title
          .contains(RegExp(r'Station|Update|Alert', caseSensitive: false))) {
        issues.add('Title missing context (Station/Update/Alert)');
        if (quality == 'good') quality = 'fair';
      }
    }

    return {'quality': quality, 'issues': issues};
  }

  /// Validate notification body quality
  static Map<String, dynamic> _validateBody(String? body) {
    final issues = <String>[];
    String quality = 'good';

    if (body == null || body.isEmpty) {
      issues.add('Body is missing or empty');
      quality = 'poor';
    } else {
      // Check body length
      if (body.length > 500) {
        issues.add('Body too long (${body.length} chars, may be truncated)');
        quality = 'fair';
      }

      // Check for table format
      if (body.contains('|') && body.contains('Station')) {
        // Validate table structure
        final lines = body.split('\n');
        if (lines.length < 3) {
          issues.add('Table format incomplete (missing header or separator)');
          quality = 'fair';
        } else {
          // Check header format
          final header = lines[0];
          if (!header.contains('Station') || !header.contains('Coach')) {
            issues
                .add('Table header missing required columns (Station, Coach)');
            quality = 'fair';
          }
        }
      }

      // Check for passenger count information
      if (!body.contains(RegExp(r'\d+')) && !body.contains('No passenger')) {
        issues.add('Body missing passenger count information');
        if (quality == 'good') quality = 'fair';
      }
    }

    return {'quality': quality, 'issues': issues};
  }

  /// Validate data payload quality
  static Map<String, dynamic> _validateDataPayload(Map<String, dynamic> data) {
    final issues = <String>[];
    String quality = 'good';

    // Check for required fields
    if (!data.containsKey('type')) {
      issues.add('Data missing notification type');
      quality = 'fair';
    }

    if (!data.containsKey('timestamp')) {
      issues.add('Data missing timestamp');
      if (quality == 'good') quality = 'fair';
    }

    // Check for enhanced train location data
    if (data['type'] == 'enhanced_train_location_update') {
      if (!data.containsKey('coach_data')) {
        issues.add('Enhanced notification missing coach_data');
        quality = 'poor';
      } else {
        try {
          final coachDataStr = data['coach_data'] as String;
          final coachData = json.decode(coachDataStr) as Map<String, dynamic>;

          if (!coachData.containsKey('has_coach_assignments')) {
            issues.add('Coach data missing assignment status');
            if (quality == 'good') quality = 'fair';
          }

          if (!coachData.containsKey('stations')) {
            issues.add('Coach data missing stations information');
            if (quality == 'good') quality = 'fair';
          }
        } catch (e) {
          issues.add('Coach data format invalid: $e');
          quality = 'poor';
        }
      }
    }

    return {'quality': quality, 'issues': issues};
  }

  /// Validate table format quality
  static Map<String, dynamic> _validateTableFormat(
      String? body, Map<String, dynamic> data) {
    final issues = <String>[];
    String quality = 'good';

    if (body != null && body.contains('|')) {
      final lines = body.split('\n');

      // Check table structure
      if (lines.length < 3) {
        issues.add('Table structure incomplete');
        quality = 'poor';
      } else {
        // Validate header
        final header = lines[0];
        final expectedColumns = [
          'Station',
          'Coach',
          'Board',
          'Deboard',
          'Vacant'
        ];

        for (final column in expectedColumns) {
          if (!header.contains(column)) {
            issues.add('Table missing column: $column');
            quality = 'fair';
          }
        }

        // Check separator line
        if (lines.length > 1 && !lines[1].contains('-')) {
          issues.add('Table missing separator line');
          if (quality == 'good') quality = 'fair';
        }

        // Validate data rows
        final dataRows = lines.skip(2).where((line) => line.trim().isNotEmpty);
        if (dataRows.isEmpty) {
          issues.add('Table has no data rows');
          quality = 'fair';
        } else {
          // Check row format
          for (final row in dataRows) {
            final columns = row.split('|');
            if (columns.length < 5) {
              issues.add('Table row has insufficient columns: $row');
              quality = 'fair';
              break;
            }
          }
        }
      }
    } else if (data['type'] == 'enhanced_train_location_update') {
      // Enhanced notification should have table format
      issues.add('Enhanced notification missing table format');
      quality = 'fair';
    }

    return {'quality': quality, 'issues': issues};
  }

  /// Validate coach assignment handling
  static Map<String, dynamic> _validateCoachAssignmentHandling(
      Map<String, dynamic> data) {
    final issues = <String>[];
    String quality = 'good';

    if (data['type'] == 'enhanced_train_location_update' &&
        data.containsKey('coach_data')) {
      try {
        final coachDataStr = data['coach_data'] as String;
        final coachData = json.decode(coachDataStr) as Map<String, dynamic>;

        final hasCoachAssignments = coachData['has_coach_assignments'] ?? false;

        // Check appropriate handling for users without assignments
        if (!hasCoachAssignments) {
          final summary = coachData['summary'] as String? ?? '';
          if (!summary.contains('No coach assignments') &&
              !summary.contains('General') &&
              !summary.contains('contact admin')) {
            issues.add(
                'Missing appropriate message for users without coach assignments');
            quality = 'fair';
          }
        }

        // Check station information is present
        final stations = coachData['stations'] as List<dynamic>? ?? [];
        if (stations.isEmpty) {
          issues.add('No station information provided');
          quality = 'fair';
        }
      } catch (e) {
        issues.add('Error validating coach assignment handling: $e');
        quality = 'poor';
      }
    }

    return {'quality': quality, 'issues': issues};
  }

  /// Convert quality string to numeric score
  static double _qualityToScore(String quality) {
    switch (quality) {
      case 'excellent':
        return 1.0;
      case 'good':
        return 0.8;
      case 'fair':
        return 0.6;
      case 'poor':
        return 0.3;
      case 'error':
        return 0.0;
      default:
        return 0.5;
    }
  }

  /// Convert numeric score to quality string
  static String _scoreToQuality(double score) {
    if (score >= 0.9) return 'excellent';
    if (score >= 0.7) return 'good';
    if (score >= 0.5) return 'fair';
    if (score >= 0.2) return 'poor';
    return 'error';
  }

  /// Generate recommendations based on validation results
  static List<String> _generateRecommendations(Map<String, dynamic> results) {
    final recommendations = <String>[];
    final issues = results['issues'] as List<String>;

    for (final issue in issues) {
      if (issue.contains('Title too long')) {
        recommendations
            .add('Shorten notification title to under 65 characters');
      } else if (issue.contains('Body too long')) {
        recommendations
            .add('Consider using summary format for long coach tables');
      } else if (issue.contains('missing train number')) {
        recommendations.add('Include train number in notification title');
      } else if (issue.contains('Table structure incomplete')) {
        recommendations
            .add('Ensure table has header, separator, and data rows');
      } else if (issue.contains('missing coach assignments')) {
        recommendations.add(
            'Add appropriate messaging for users without coach assignments');
      }
    }

    // Add general recommendations based on score
    final score = results['score'] as double;
    if (score < 0.7) {
      recommendations.add('Review notification content formatting guidelines');
      recommendations.add('Test notifications on real Android devices');
    }

    return recommendations;
  }

  /// Generate quality report
  static String generateQualityReport(Map<String, dynamic> validation) {
    final score = ((validation['score'] as double) * 100).toStringAsFixed(1);
    final quality = validation['overall_quality'];
    final issues = validation['issues'] as List<String>;
    final recommendations = validation['recommendations'] as List<String>;

    return '''
📊 NOTIFICATION QUALITY REPORT

🎯 Overall Quality: ${quality.toUpperCase()}
📈 Quality Score: $score%

📋 Component Scores:
- Title Quality: ${validation['title_quality']}
- Body Quality: ${validation['body_quality']}
- Data Quality: ${validation['data_quality']}
- Table Format: ${validation['table_format_quality']}
- Coach Assignment Handling: ${validation['coach_assignment_handling']}

${issues.isNotEmpty ? '⚠️ Issues Found:\n${issues.map((i) => '• $i').join('\n')}\n' : '✅ No issues found\n'}

${recommendations.isNotEmpty ? '💡 Recommendations:\n${recommendations.map((r) => '• $r').join('\n')}' : '🎉 No recommendations needed'}
''';
  }
}
