import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:railops/constants/api_constant.dart';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/services/api_services/api_service.dart';
import 'package:railops/services/fcm_token_service.dart';
import 'package:railops/services/background_services/notification_background_service.dart';
import 'package:railops/types/train_types/upcoming_station_response.dart';

class UpcomingStationService {
  // Calls the API to fetch upcoming station details
  static Future<UpcomingStationResponse> fetchUpcomingStationDetails({
    required String lat,
    required String lng,
    required String token,
    // Phase 3.1: Enhanced notification integration options
    bool enableNotificationProcessing = false,
    bool enableBoardingNotifications = true,
    bool enableOffBoardingNotifications = true,
    bool enableStationApproachingNotifications = false,
    bool enableLocationBasedScheduling = false,
  }) async {
    try {
      // Temporarily comment out FCM and device info to match Postman exactly
      // final fcmToken = await FcmTokenService.getTokenForApiRequest(token);
      // final deviceInfo = await FcmTokenService.getDeviceInfo();

      // Prepare request body - match Postman format exactly for debugging
      final requestBody = {
        'lat': lat,
        'lng': lng,
        'token': token,
        // Temporarily comment out extra fields to match Postman exactly
        // if (fcmToken != null && FcmTokenService.isValidFcmToken(fcmToken))
        //   'fcm_token': fcmToken,
        // 'device_info': deviceInfo,
      };

      debugPrint('🔍 API Request Debug:');
      debugPrint('URL: ${ApiConstant.baseUrl}/api/onboarding_details_popup/');
      debugPrint('Body: ${jsonEncode(requestBody)}');

      final responseJson = await ApiService.post(
        '/api/onboarding_details_popup/',
        requestBody,
      );

      // Log FCM token registration status if present in response
      if (responseJson.containsKey('fcm_registration')) {
        final fcmStatus = responseJson['fcm_registration'];
        debugPrint(
            'FCM Registration Status: ${fcmStatus['status']} - ${fcmStatus['message']}');
      }

      final response = UpcomingStationResponse.fromJson(responseJson);

      // NOTE: Onboarding notification processing has been removed
      // The Firebase Cloud Function system handles notifications properly with formatted tables
      if (enableNotificationProcessing) {
        if (kDebugMode) {
          print(
              'UpcomingStationService: Using Firebase Cloud Function system for notifications');
        }
      }

      return response;
    } on ApiException catch (e) {
      throw (e.message);
    } catch (e) {
      throw Exception(e);
    }
  }

  /// Phase 3.1: Enhanced location-based notification scheduling
  static Future<void> _processLocationBasedScheduling(
    UpcomingStationResponse response, {
    required String currentLat,
    required String currentLng,
  }) async {
    try {
      // Store current location for background processing
      await _storeLocationForBackgroundProcessing(
        response,
        currentLat: currentLat,
        currentLng: currentLng,
      );

      if (kDebugMode) {
        print(
            'UpcomingStationService: Location-based scheduling configured for train ${response.trainNumber}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('UpcomingStationService: Error in location-based scheduling: $e');
      }
    }
  }

  /// Store location and train data for background notification processing
  static Future<void> _storeLocationForBackgroundProcessing(
    UpcomingStationResponse response, {
    required String currentLat,
    required String currentLng,
  }) async {
    try {
      // Phase 3.2: Start background notification processing
      await NotificationBackgroundService.startBackgroundProcessing(
        trainData: response,
        currentLat: currentLat,
        currentLng: currentLng,
        enableBoardingNotifications: true,
        enableOffBoardingNotifications: true,
        enableProximityAlerts: true,
        proximityThresholdKm: 2.0,
      );

      if (kDebugMode) {
        print(
            'UpcomingStationService: Started background notification processing for train ${response.trainNumber}');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'UpcomingStationService: Error starting background notification processing: $e');
      }
    }
  }

  /// Phase 3.1: Stop background notification processing
  static Future<void> stopBackgroundNotificationProcessing() async {
    try {
      await NotificationBackgroundService.stopBackgroundProcessing();

      if (kDebugMode) {
        print(
            'UpcomingStationService: Stopped background notification processing');
      }
    } catch (e) {
      if (kDebugMode) {
        print(
            'UpcomingStationService: Error stopping background notification processing: $e');
      }
    }
  }

  /// Phase 3.1: Get background notification processing status
  static Future<Map<String, dynamic>> getBackgroundNotificationStatus() async {
    try {
      return await NotificationBackgroundService
          .getBackgroundProcessingStatus();
    } catch (e) {
      if (kDebugMode) {
        print(
            'UpcomingStationService: Error getting background notification status: $e');
      }
      return {
        'is_active': false,
        'error': e.toString(),
      };
    }
  }
}
