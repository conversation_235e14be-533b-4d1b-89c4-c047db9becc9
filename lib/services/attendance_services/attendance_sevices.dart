import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:image_picker/image_picker.dart';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/constants/index.dart';
import 'package:http/http.dart' as http;
import 'package:railops/services/api_services/api_service.dart';
import 'package:railops/types/attendance_types/attendance_data.dart';


class AttendanceService {
  static Future<Map<String, dynamic>?> createAttendance({
    required XFile imageFile,
    required String username,
    required String trainNumber,
    required String stationCode,
    required String date,
    required String token,
    required String latitude,
    required String longitude
  }) async {
    try {
      final endpoint = '/attendance/create/';
      
      var request = http.MultipartRequest('POST', Uri.parse(ApiConstant.baseUrl + endpoint));
      request.headers.addAll({
        'Authorization': 'Bearer $token',
      });

      Uint8List bytes = await imageFile.readAsBytes();
      String filename = imageFile.name;

      var file = http.MultipartFile(
        'file',
        http.ByteStream.fromBytes(bytes),
        bytes.length,
        filename: filename,
      );


      request.files.add(file);

      request.fields['username'] = username;
      request.fields['train_number'] = trainNumber;
      request.fields['station_code'] = stationCode;
      request.fields['date'] = date;
      request.fields['latitude'] = latitude;
      request.fields['longitude'] = longitude;


      var response = await request.send();
      
      if (response.statusCode == 200) {
        var responseData = await response.stream.bytesToString();
        return json.decode(responseData);
      } 
      if (response.statusCode == 500) {
        var responseData = await response.stream.bytesToString();
        print(json.decode(responseData));
      } 
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      rethrow;
    }
  }


  static Future<List<AttendanceData>?> fetchAttendance({
    String? userName,
    String? stationCode,
    String? date,
    String? trainNumber,
    String? token
  }) async {
    try {
      final endpoint = "/attendance/get/?train_number=$trainNumber&username=$userName&date=$date&station_code=$stationCode";
    
      final body = <String, String>{};
      body["token"] = token!;

      final responseJson = await ApiService.get(endpoint, body);
      if (responseJson != null && responseJson is List) {
        return responseJson
            .map((json) => AttendanceData.fromJson(json))
            .toList();
      }
      return null;
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      return null;
    }
  }

  // Function to fetch attendance details
  static Future<List<AttendanceData>?> fetchAttendanceDetails({
    String? userName,
    String? stationCode,
    String? date,
    String? trainNumber,
    String? token,
  }) async {
    try {
      final responseJson = await ApiService.post('/attendance/get_attendance_data_by_station/', {'token': token,'train_number':trainNumber,'station_code':stationCode,'date':date});
      final data = responseJson["data"];
      if (responseJson != null && responseJson['data'] is List) {
        final data = responseJson['data'] as List;
        return data.map((json) => AttendanceData.fromJson(json)).toList();
      }
      else if (data is Map) {
        return [AttendanceData.fromJson(Map<String, dynamic>.from(data))];
      } else {
        print("Invalid or missing 'data' in response JSON");
        return [];
      }
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      return null;
    }
  }
  
  static Future<String> deleteAttendance(String username, String stationCode, String date, String trainNumber, String? token,) async {
    try {
      final endpoint = "/attendance/delete_attendance/";
    
      final body ={
        "token":token!,
        "username":username,
        "train_number":trainNumber,
        "station_code":stationCode,
        "date":date,
      };
      body["token"] = token;

      final responseJson = await ApiService.post(endpoint, body);
      return responseJson["message"] ?? "Unknown response from server.";
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      return "Error while deleting Image:$e";
    }
  }

  static Future<Map<String, dynamic>> fetchAllUsers(String token, String? trainNumber, String date) async {
    try {
      final response = await ApiService.get(
        '/attendance/get_users_with_train_access/?train_number=$trainNumber&origin_date=$date',
        {
          "token": token
        },
      );

      final responseData = Map<String, dynamic>.from(response);

      Map<String, Map<String, String>> userDetails = {};
      final allUserDetails = Map<String, dynamic>.from(responseData["all_user_details"] ?? {});

      allUserDetails.forEach((username, details) {
        userDetails[username] = {
          "user_type": details["user_type"] ?? "",
          "emp_number": details["emp_number"] ?? "NA",
        };
      });

      return {
        "users_ca_ehk": List<String>.from(responseData["users_ca_ehk"] ?? []),
        "users_obhs": List<String>.from(responseData["users_obhs"] ?? []),
        "user_details": userDetails,  // Added user details
      };

    } on ApiException catch (e) {
      throw e.message;
    }
  }


  static Future<Map<String, int>> fetchStationAttendanceCount({
    required String trainNumber,
    required String date,
    required token
  }) async {
    try {

      final endpoint = "/attendance/station-attendance-count/?train_number=$trainNumber&date=$date";

      final response = await ApiService.get(
        endpoint,
        {
          'token': token
        },
      );

      return (response as Map<String, dynamic>)
          .map((key, value) => MapEntry(key, value as int));
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      throw 'An error occurred while fetching station attendance count';
    }
  }

  static Future<Map<String, dynamic>> fetchInsideTrainDetails(String? token) async {
    try {
      final response = await ApiService.get('/api/new_inside_train/',{'token': token});

      final responseData = response as Map<String, dynamic>;

      if (responseData.containsKey("inside_train_number") &&
          responseData.containsKey("date") &&
          responseData.containsKey("coach_numbers")) {
        return {
          "insideTrainNumber": responseData["inside_train_number"],
          "date": responseData["date"],
          "coachNumbers": List<String>.from(responseData["coach_numbers"]),
        };
      } else if (responseData.containsKey("message")) {
        throw responseData["message"];
      } else {
        throw 'Unexpected response format';
      }
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      throw 'An error occurred while fetching inside train details';
    }
  }

  static Future<void> updateJourneyStatus(
    String token,
    String trainNumber,
    String date,
    String status,
  ) async {
    try {
      final body = {
        'token': token,
        'train_number': trainNumber,
        'origin_date': date,
        'journey_status': status,
      };
      
      await ApiService.put("/api/update-journey-status/", body);
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      throw 'Failed to update journey status: $e.m';
    }
  }

  static Future<Map<String, dynamic>> getJourneyStatus(
    String token,
    String trainNumber,
    String date,
  ) async {
    try {
      
      return await ApiService.get('/api/get-journey-status/?train_number=${trainNumber}&origin_date=${date}', {
        'token': token,
      });
    } on ApiException catch (e) {
      throw e.message;
    } catch (e) {
      throw 'Failed to fetch journey status: $e';
    }
  }
}
