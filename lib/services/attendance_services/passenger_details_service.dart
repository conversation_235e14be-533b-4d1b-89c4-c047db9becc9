import 'dart:convert';
import 'package:railops/services/api_services/api_service.dart';
import 'package:railops/services/api_services/api_exception.dart';
import 'package:railops/types/attendance_types/passchart_detail_types.dart';

class PassengerChartDetails {
  static Future<TrainScheduleResponse> getAllDetails({
    String? userName,
    String? queryDate,
    String? trainNo,
    String? stationCode,
    String? token,
  }) async {
    try {
      // Validate required parameters
      if (userName == null ||
          userName.isEmpty ||
          queryDate == null ||
          queryDate.isEmpty ||
          trainNo == null ||
          trainNo.isEmpty ||
          token == null ||
          token.isEmpty) {
        throw ArgumentError(
            'Username, queryDate, trainNo, and token are required');
      }

      // Updated endpoint to match the new API
      final endpoint =
          '/api/curated-berth-details/?username=$userName&date=$queryDate&train_number=$trainNo';

      final body = <String, String>{'token': token};

      // ApiService.get() returns JSON data directly, not HTTP response
      final jsonData = await ApiService.get(endpoint, body);

      // Convert to Map<String, dynamic> and create response object
      Map<String, dynamic> responseMap;

      if (jsonData is Map<String, dynamic>) {
        responseMap = jsonData;
      } else if (jsonData is String) {
        try {
          responseMap = jsonDecode(jsonData);
        } on FormatException catch (e) {
          throw Exception('Invalid JSON response: ${e.message}');
        }
      } else {
        throw Exception('Unexpected response format: ${jsonData.runtimeType}');
      }

      // Validate the new response structure
      if (!responseMap.containsKey('trains') ||
          responseMap['trains'] is! List) {
        throw Exception(
            'Invalid response structure: missing or invalid trains array');
      }

      if ((responseMap['trains'] as List).isEmpty) {
        throw Exception('No train data found in response');
      }

      // Additional validation for new fields
      if (!responseMap.containsKey('username') ||
          !responseMap.containsKey('user_type') ||
          !responseMap.containsKey('query_date') ||
          !responseMap.containsKey('fallback_mode')) {
        throw Exception('Missing required fields in API response');
      }

      return TrainScheduleResponse.fromJson(responseMap);
    } on ApiException catch (e) {
      throw Exception('API Error: ${e.message}');
    } on ArgumentError catch (e) {
      throw Exception('Parameter Error: ${e.message}');
    } catch (e) {
      throw Exception(
          'Unexpected error while fetching passenger chart details: $e');
    }
  }

  /// Fetches assigned coaches data for a specific train
  static Future<Map<String, CoachData>?> getAssignedCoaches({
    required String userName,
    required String queryDate,
    required String trainNo,
    required String token,
  }) async {
    try {
      final response = await getAllDetails(
        userName: userName,
        queryDate: queryDate,
        trainNo: trainNo,
        token: token,
      );

      // Find the train in the response
      TrainData? targetTrain;
      for (var train in response.trains) {
        if (train.trainNo.toString() == trainNo) {
          targetTrain = train;
          break;
        }
      }

      if (targetTrain == null) {
        throw Exception('Train $trainNo not found in response');
      }

      // Return assigned coaches data
      return targetTrain.assignedCoaches;
    } catch (e) {
      throw Exception('Error fetching assigned coaches: $e');
    }
  }

  /// Fetches other coaches data for a specific train
  static Future<Map<String, CoachData>?> getOtherCoaches({
    required String userName,
    required String queryDate,
    required String trainNo,
    required String token,
  }) async {
    try {
      final response = await getAllDetails(
        userName: userName,
        queryDate: queryDate,
        trainNo: trainNo,
        token: token,
      );

      // Find the train in the response
      TrainData? targetTrain;
      for (var train in response.trains) {
        if (train.trainNo.toString() == trainNo) {
          targetTrain = train;
          break;
        }
      }

      if (targetTrain == null) {
        throw Exception('Train $trainNo not found in response');
      }

      // Return other coaches data
      return targetTrain.otherCoaches;
    } catch (e) {
      throw Exception('Error fetching other coaches: $e');
    }
  }

  /// Fetches onboarding passengers for a specific coach from assigned coaches
  static Future<Map<String, BerthInfo>?> getCoachOnboardingFromAssigned({
    required String userName,
    required String queryDate,
    required String trainNo,
    required String coachNo,
    required String token,
  }) async {
    try {
      final assignedCoaches = await getAssignedCoaches(
        userName: userName,
        queryDate: queryDate,
        trainNo: trainNo,
        token: token,
      );

      if (assignedCoaches == null || !assignedCoaches.containsKey(coachNo)) {
        return null;
      }

      final coachData = assignedCoaches[coachNo];
      if (coachData == null) {
        return null;
      }

      return coachData.onboarding;
    } catch (e) {
      throw Exception('Error fetching coach onboarding from assigned: $e');
    }
  }

  /// Fetches offboarding passengers for a specific coach from assigned coaches
  static Future<Map<String, BerthInfo>?> getCoachOffboardingFromAssigned({
    required String userName,
    required String queryDate,
    required String trainNo,
    required String coachNo,
    required String token,
  }) async {
    try {
      final assignedCoaches = await getAssignedCoaches(
        userName: userName,
        queryDate: queryDate,
        trainNo: trainNo,
        token: token,
      );

      if (assignedCoaches == null || !assignedCoaches.containsKey(coachNo)) {
        return null;
      }

      final coachData = assignedCoaches[coachNo];
      if (coachData == null) {
        return null;
      }

      return coachData.offboarding;
    } catch (e) {
      throw Exception('Error fetching coach offboarding from assigned: $e');
    }
  }

  /// Fetches available berths for a specific coach from assigned coaches
  static Future<Map<String, String>?> getCoachAvailableFromAssigned({
    required String userName,
    required String queryDate,
    required String trainNo,
    required String coachNo,
    required String token,
  }) async {
    try {
      final assignedCoaches = await getAssignedCoaches(
        userName: userName,
        queryDate: queryDate,
        trainNo: trainNo,
        token: token,
      );

      if (assignedCoaches == null || !assignedCoaches.containsKey(coachNo)) {
        return null;
      }

      final coachData = assignedCoaches[coachNo];
      if (coachData == null) {
        return null;
      }

      return coachData.available;
    } catch (e) {
      throw Exception('Error fetching coach available from assigned: $e');
    }
  }

  /// Gets all assigned coach numbers for a train
  static Future<List<String>> getAssignedCoachNumbers({
    required String userName,
    required String queryDate,
    required String trainNo,
    required String token,
  }) async {
    try {
      final assignedCoaches = await getAssignedCoaches(
        userName: userName,
        queryDate: queryDate,
        trainNo: trainNo,
        token: token,
      );

      if (assignedCoaches == null) return [];

      return assignedCoaches.keys.toList();
    } catch (e) {
      throw Exception('Error fetching assigned coach numbers: $e');
    }
  }

  /// Gets all other coach numbers for a train
  static Future<List<String>> getOtherCoachNumbers({
    required String userName,
    required String queryDate,
    required String trainNo,
    required String token,
  }) async {
    try {
      final otherCoaches = await getOtherCoaches(
        userName: userName,
        queryDate: queryDate,
        trainNo: trainNo,
        token: token,
      );

      if (otherCoaches == null) return [];

      return otherCoaches.keys.toList();
    } catch (e) {
      throw Exception('Error fetching other coach numbers: $e');
    }
  }

  /// Gets user information from the API response
  static Future<Map<String, dynamic>> getUserInfo({
    required String userName,
    required String queryDate,
    required String trainNo,
    required String token,
  }) async {
    try {
      final response = await getAllDetails(
        userName: userName,
        queryDate: queryDate,
        trainNo: trainNo,
        token: token,
      );

      return {
        'username': response.username,
        'user_type': response.userType,
        'query_date': response.queryDate,
        'fallback_mode': response.fallbackMode,
      };
    } catch (e) {
      throw Exception('Error fetching user info: $e');
    }
  }

  /// Gets train stoppages
  static Future<List<String>> getTrainStoppages({
    required String userName,
    required String queryDate,
    required String trainNo,
    required String token,
  }) async {
    try {
      final response = await getAllDetails(
        userName: userName,
        queryDate: queryDate,
        trainNo: trainNo,
        token: token,
      );

      for (var train in response.trains) {
        if (train.trainNo.toString() == trainNo) {
          return train.stoppages;
        }
      }

      throw Exception('Train $trainNo not found in response');
    } catch (e) {
      throw Exception('Error fetching train stoppages: $e');
    }
  }

  /// Gets all trains from the response
  static Future<List<TrainData>> getAllTrains({
    required String userName,
    required String queryDate,
    required String token,
    String? trainNo, // Optional - if provided, will filter by train number
  }) async {
    try {
      final response = await getAllDetails(
        userName: userName,
        queryDate: queryDate,
        trainNo: trainNo ?? '',
        token: token,
      );

      if (trainNo != null && trainNo.isNotEmpty) {
        // Filter by specific train number
        return response.trains
            .where((train) => train.trainNo.toString() == trainNo)
            .toList();
      }

      return response.trains;
    } catch (e) {
      throw Exception('Error fetching trains: $e');
    }
  }

  /// Checks if user is in fallback mode
  static Future<bool> isFallbackMode({
    required String userName,
    required String queryDate,
    required String trainNo,
    required String token,
  }) async {
    try {
      final response = await getAllDetails(
        userName: userName,
        queryDate: queryDate,
        trainNo: trainNo,
        token: token,
      );

      return response.fallbackMode;
    } catch (e) {
      throw Exception('Error checking fallback mode: $e');
    }
  }

  /// Gets complete coach data structure for a specific coach
  static Future<Map<String, dynamic>?> getCompleteCoachData({
    required String userName,
    required String queryDate,
    required String trainNo,
    required String coachNo,
    required String token,
  }) async {
    try {
      final response = await getAllDetails(
        userName: userName,
        queryDate: queryDate,
        trainNo: trainNo,
        token: token,
      );

      for (var train in response.trains) {
        if (train.trainNo.toString() == trainNo) {
          // Check in assigned coaches first
          if (train.assignedCoaches.containsKey(coachNo)) {
            return {
              'coach_type': 'assigned',
              'data': train.assignedCoaches[coachNo]?.toJson(),
            };
          }

          // Check in other coaches
          if (train.otherCoaches.containsKey(coachNo)) {
            return {
              'coach_type': 'other',
              'data': train.otherCoaches[coachNo]?.toJson(),
            };
          }
        }
      }

      return null;
    } catch (e) {
      throw Exception('Error fetching complete coach data: $e');
    }
  }

  /// Gets summary statistics for all coaches
  static Future<Map<String, dynamic>> getTrainSummary({
    required String userName,
    required String queryDate,
    required String trainNo,
    required String token,
  }) async {
    try {
      final response = await getAllDetails(
        userName: userName,
        queryDate: queryDate,
        trainNo: trainNo,
        token: token,
      );

      for (var train in response.trains) {
        if (train.trainNo.toString() == trainNo) {
          int assignedCoachCount = train.assignedCoaches.length;
          int otherCoachCount = train.otherCoaches.length;
          int totalCoaches = assignedCoachCount + otherCoachCount;

          return {
            'train_no': train.trainNo,
            'total_coaches': totalCoaches,
            'assigned_coaches_count': assignedCoachCount,
            'other_coaches_count': otherCoachCount,
            'assigned_coach_numbers': train.assignedCoaches.keys.toList(),
            'other_coach_numbers': train.otherCoaches.keys.toList(),
            'stoppages_count': train.stoppages.length,
            'stoppages': train.stoppages,
          };
        }
      }

      throw Exception('Train $trainNo not found in response');
    } catch (e) {
      throw Exception('Error fetching train summary: $e');
    }
  }

  // Legacy methods for backward compatibility (marked as deprecated)

  @deprecated
  static Future<Map<String, List<int>>> getStationDetails({
    required String userName,
    required String queryDate,
    required String trainNo,
    required String stationCode,
    required String token,
  }) async {
    print(
        'Warning: getStationDetails is deprecated. Use the new coach-centric methods instead.');
    // This method is deprecated as the new API structure is coach-centric, not station-centric
    throw Exception(
        'This method is deprecated. The new API structure is coach-centric. Use getCompleteCoachData instead.');
  }

  @deprecated
  static Future<Map<String, List<int>>> getOnboardingPassengers({
    required String userName,
    required String queryDate,
    required String trainNo,
    required String stationCode,
    required String token,
  }) async {
    print(
        'Warning: getOnboardingPassengers is deprecated. Use getCoachOnboardingFromAssigned instead.');
    throw Exception(
        'This method is deprecated. Use getCoachOnboardingFromAssigned instead.');
  }

  @deprecated
  static Future<Map<String, List<int>>> getOffboardingPassengers({
    required String userName,
    required String queryDate,
    required String trainNo,
    required String stationCode,
    required String token,
  }) async {
    print(
        'Warning: getOffboardingPassengers is deprecated. Use getCoachOffboardingFromAssigned instead.');
    throw Exception(
        'This method is deprecated. Use getCoachOffboardingFromAssigned instead.');
  }

  @deprecated
  static Future<Map<String, List<int>>> getAvailableBerths({
    required String userName,
    required String queryDate,
    required String trainNo,
    required String stationCode,
    required String token,
  }) async {
    print(
        'Warning: getAvailableBerths is deprecated. Use getCoachAvailableFromAssigned instead.');
    throw Exception(
        'This method is deprecated. Use getCoachAvailableFromAssigned instead.');
  }
}