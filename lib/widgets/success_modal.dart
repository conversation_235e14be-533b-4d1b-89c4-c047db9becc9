import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

void showSuccessModal(
  BuildContext context,
  String errorMessage,
  String messageType,
  VoidCallback onClose,
) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: Text(messageType),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Image.asset(
              'assets/images/Verified.gif',
              height: 150,
              width: 150,
              color: Colors.blue,
            ),
            const SizedBox(height: 10),
            Text(errorMessage),
          ],
        ),
        actions: <Widget>[
          TextButton(
            child: Text(AppLocalizations.of(context).text_close),
            onPressed: () async {
              Navigator.of(context).pop();
              await Future.delayed(const Duration(milliseconds: 100));
              onClose();
            },
          ),
        ],
      );
    },
  );
}
