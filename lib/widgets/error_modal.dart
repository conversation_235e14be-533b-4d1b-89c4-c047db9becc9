import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

void showErrorModal(BuildContext context, String errorMessage,
    String messageType, VoidCallback onClose) {
  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: Text(messageType),
        content: Text(errorMessage),
        actions: <Widget>[
          TextButton(
            child: Text(AppLocalizations.of(context).text_close),
            onPressed: () {
              onClose();
              Navigator.of(context).pop();
            },
          ),
        ],
      );
    },
  );
}
