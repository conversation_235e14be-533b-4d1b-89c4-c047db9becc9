import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/services/locale_service.dart';

class LanguageSelector extends StatefulWidget {
  final bool showLabel;
  final bool isCompact;
  
  const LanguageSelector({
    Key? key,
    this.showLabel = true,
    this.isCompact = false,
  }) : super(key: key);

  @override
  _LanguageSelectorState createState() => _LanguageSelectorState();
}

class _LanguageSelectorState extends State<LanguageSelector> {
  @override
  void initState() {
    super.initState();
  }

  final List<Map<String, String>> _languages = [
    {'code': 'en', 'name': 'English', 'native': 'English'},
    {'code': 'hi', 'name': 'Hindi', 'native': 'हिंदी'},
    {'code': 'bn', 'name': 'Bengali', 'native': 'বাংলা'},
    {'code': 'as', 'name': 'Assamese', 'native': 'অসমীয়া'},
    {'code': 'pa', 'name': 'Punjabi', 'native': 'ਪੰਜਾਬੀ'},
    {'code': 'mr', 'name': 'Marathi', 'native': 'मराठी'},
    {'code': 'kn', 'name': 'Kannada', 'native': 'ಕನ್ನಡ'},
    {'code': 'ta', 'name': 'Tamil', 'native': 'தமிழ்'},
    {'code': 'te', 'name': 'Telugu', 'native': 'తెలుగు'},
    {'code': 'ml', 'name': 'Malayalam', 'native': 'മലയാളം'},
  ];

  void _changeLanguage(String? languageCode) async {
    if (languageCode != null) {
      final localeService = Provider.of<LocaleService>(context, listen: false);
      
      // Change locale using the singleton instance
      await localeService.changeLocale(Locale(languageCode));
      
      // Show a snackbar to inform user about the language change
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Language changed to ${_getLanguageName(languageCode)}',
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 2),
          ),
        );
        // No navigation hack needed - Provider will handle the rebuild automatically
      }
    }
  }

  String _getLanguageName(String code) {
    final language = _languages.firstWhere(
      (lang) => lang['code'] == code,
      orElse: () => _languages.first,
    );
    return language['native'] ?? language['name'] ?? 'English';
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isCompact) {
      return _buildCompactSelector();
    } else {
      return _buildFullSelector();
    }
  }

  Widget _buildCompactSelector() {
    return Consumer<LocaleService>(
      builder: (context, localeService, child) {
        return PopupMenuButton<String>(
          icon: const Icon(Icons.language, color: Colors.black87),
          onSelected: _changeLanguage,
          itemBuilder: (BuildContext context) {
            return _languages.map((language) {
              return PopupMenuItem<String>(
                value: language['code'],
                child: Row(
                  children: [
                    Text(
                      language['native']!,
                      style: TextStyle(
                        fontWeight: localeService.currentLocale.languageCode == language['code']
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '(${language['name']})',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              );
            }).toList();
          },
        );
      },
    );
  }

  Widget _buildFullSelector() {
    return Consumer<LocaleService>(
      builder: (context, localeService, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.showLabel)
              const Padding(
                padding: EdgeInsets.only(bottom: 8.0),
                child: Text(
                  'Language / भाषा',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
              ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
                color: Colors.white,
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: localeService.currentLocale.languageCode,
                  isExpanded: true,
                  icon: const Icon(Icons.arrow_drop_down, color: Colors.black54),
                  items: _languages.map((language) {
                    return DropdownMenuItem<String>(
                      value: language['code'],
                      child: Row(
                        children: [
                          Text(
                            language['native']!,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.black87,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '(${language['name']})',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: _changeLanguage,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

// Convenience widget for drawer/menu usage
class LanguageSelectorListTile extends StatelessWidget {
  const LanguageSelectorListTile({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<LocaleService>(
      builder: (context, localeService, child) {
        return ListTile(
          leading: const Icon(Icons.language, color: Colors.black54),
          title: const Text('Language'),
          subtitle: Text(LocaleService.getLanguageName(
            localeService.currentLocale.languageCode,
          )),
          onTap: () {
            showDialog(
              context: context,
              builder: (BuildContext context) {
                return AlertDialog(
                  title: const Text('Select Language'),
                  content: const SizedBox(
                    width: double.maxFinite,
                    child: LanguageSelector(showLabel: false),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Close'),
                    ),
                  ],
                );
              },
            );
          },
        );
      },
    );
  }
}
