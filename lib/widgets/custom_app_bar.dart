import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/index.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:railops/screens/attendance/upload_manager.dart';
import 'package:railops/services/assign_ehk_ca_services/assign_ehk_ca_services.dart';
import 'package:railops/services/profile_services/profile_services.dart';
import 'package:railops/services/train_services/train_details_services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class UploadStatusIndicator extends StatelessWidget {
  final VoidCallback? onViewDetails;

  const UploadStatusIndicator({
    super.key,
    this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    // Use Consumer to listen to UploadManager changes
    return Consumer<UploadManager>(
      builder: (context, uploadManager, child) {
        final bool isUploading = uploadManager.uploadCount > 0;
        final bool isCompressing = uploadManager.isCompressing;
        final int uploadCount = uploadManager.uploadCount;
        final double progress = uploadManager.averageProgress;

        if (!isUploading && !isCompressing && uploadCount == 0) {
          return const SizedBox.shrink();
        }

        return GestureDetector(
          onTap: onViewDetails,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.blue.shade200, width: 1),
            ),
            constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width *
                    0.25), // Add max width constraint
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isUploading || isCompressing)
                  SizedBox(
                    width: 14,
                    height: 14,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      value: progress > 0 ? progress : null,
                      valueColor:
                          const AlwaysStoppedAnimation<Color>(Colors.blue),
                    ),
                  ),
                const SizedBox(width: 4),
                Flexible(
                  // Make text flexible
                  child: Text(
                    isCompressing
                        ? 'Compressing...'
                        : uploadCount > 0
                            ? '$uploadCount ${uploadCount == 1 ? 'upload' : 'uploads'}'
                            : 'Preparing...',
                    style: TextStyle(
                      fontSize: 11, // Slightly smaller font
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade700,
                    ),
                    overflow:
                        TextOverflow.ellipsis, // Add ellipsis for overflow
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class CustomAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String title;
  final VoidCallback? onViewUploads;
  final List<Widget>? actions;

  const CustomAppBar({
    super.key,
    required this.title,
    this.onViewUploads,
    this.actions,
  });

  @override
  _CustomAppBarState createState() => _CustomAppBarState();

  @override
  Size get preferredSize {
    double height = kToolbarHeight;
    if (MediaQueryData.fromView(
                WidgetsBinding.instance.platformDispatcher.views.first)
            .size
            .width <
        600) {
      height = kToolbarHeight + 10;
    }
    return Size.fromHeight(height);
  }
}

class _CustomAppBarState extends State<CustomAppBar> {
  String appVersion = '';
  String? token = '';
  String _depotCode = '';
  late TextEditingController _depotController;
  final DateTime _selectedDate = DateTime.now(); // Add depot code variable
  final TextEditingController _dateController = TextEditingController();
  List<String> _trainNumbers = [];
  bool showLoader = false;
  String loaderText = 'Loading...';
  bool isLoadingTrainData = false;
  bool isLoadingProfile = false;
  bool showErrorModal = false;
  String? errorMessage; // Add depot code variable
  String? _inDate;
  String? _outDate;
  String? _inTrain;
  String? _outTrain;
  String _previousTrainNumber = '';

  @override
  void initState() {
    super.initState();
    final userModel = Provider.of<UserModel>(context, listen: false);
    token = userModel.token;
    _depotController = TextEditingController();
    _getAppVersion();
    _getProfile();
    _fetchTrainNumbers();
    _updateDateController();
    _fetchTrainDates(); // Add this to fetch train dates on init
  }

  @override
  void dispose() {
    _depotController.dispose();
    super.dispose();
  }

  // Custom date formatter function
  String _formatDateToCustomFormat(DateTime date) {
    final day = date.day.toString().padLeft(2, '0');
    final month =
        DateFormat('MMM').format(date); // Short month name (Jan, Feb, etc.)
    return '$day $month';
  }

  String _formatApiDateToDisplay(String apiDate) {
    try {
      final date = DateTime.parse(apiDate);
      return DateFormat('dd MMM').format(date);
    } catch (e) {
      return apiDate; // Return original if parsing fails
    }
  }

  void _updateDateController() {
    _dateController.text = _formatDateToCustomFormat(_selectedDate);
  }

  // Train API methods - integrated from AssignEhkCaFilters
  Future<void> _fetchTrainNumbers() async {
    try {
      setState(() {
        isLoadingTrainData = true;
      });
      _trainNumbers = await TrainService.getTrainNumbers();
      setState(() {
        isLoadingTrainData = false;
      });
    } catch (e) {
      print('Error fetching train numbers: $e');
      setState(() {
        isLoadingTrainData = false;
      });
      // Show error snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load train numbers: $e')),
        );
      }
    }
  }

  Future<void> _refreshTrainData() async {
    await _fetchTrainNumbers();
  }

  Future<void> fetchTrainsCoachWise(
      String trainNumber, String selectedDate) async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final token = userModel.token;
    final forUserType = userModel.userType;
    if (token.isNotEmpty) {
      setState(() {
        showLoader = true;
        loaderText = 'Loading train data...';
      });

      try {
        final response = await AdminAssignService.fetchTrainsCoachWise(
            trainNumber, selectedDate, forUserType, token);

        setState(() {
          _outTrain = response['out']?['train_no'].toString();
          _inTrain = response['in']?['train_no'].toString();
          _outDate = response['out']?['date'];
          _inDate = response['in']?['date'];
        });
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading train data: ${e.toString()}')),
        );
      } finally {
        setState(() => showLoader = false);
      }
    }
  }

  Future<void> _getProfile() async {
    setState(() {
      isLoadingProfile = true;
    });
    try {
      final profileResponse = await ProfileService.getProfile(token!);
      setState(() {
        final depot = profileResponse.user?.depo ?? '';
        _depotController.text = depot;

        // Extract first depot code from comma-separated list
        if (depot.isNotEmpty) {
          _depotCode = depot.split(',').first.trim();
        } else {
          _depotCode = '';
        }
      });
    } catch (e) {
      setState(() {
        showErrorModal = true;
        errorMessage = '$e';
      });
    } finally {
      setState(() {
        isLoadingProfile = false;
      });
    }
  }

  // Helper method to fetch train dates when component initializes
  Future<void> _fetchTrainDates() async {
    final userModel = Provider.of<UserModel>(context, listen: false);
    final trainNumber = userModel.trainNo;

    if (trainNumber.isNotEmpty) {
      final selectedDateStr = DateFormat('yyyy-MM-dd').format(_selectedDate);
      await fetchTrainsCoachWise(trainNumber, selectedDateStr);
    }
  }

  // Helper method to get first depot code
  String _getFirstDepotCode() {
    if (_depotCode.isNotEmpty) {
      return _depotCode;
    }
    return '';
  }

  Future<void> _getAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    if (mounted) {
      setState(() {
        appVersion = packageInfo.version;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final uploadManager = Provider.of<UploadManager>(context);
    return AppBar(
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(1.0),
        child: Container(
          color: Colors.black26,
          height: 1.0,
        ),
      ),
      backgroundColor: Colors.white,
      leading: Builder(
        builder: (BuildContext context) {
          return IconButton(
            icon: const Icon(Icons.menu, color: Colors.black87),
            onPressed: () {
              Scaffold.of(context).openDrawer();
            },
          );
        },
      ),
      title: Consumer<UserModel>(
        builder: (context, userModel, child) {
          final roleLetter = _getRoleLetter(userModel.userType);
          final trainNumber = userModel.trainNo;
          final depotCode = _getFirstDepotCode();

          String extractNameFromUsername(String username) {
            // Remove all non-alphabetic characters and return only letters
            String nameOnly = username.replaceAll(RegExp(r'[^a-zA-Z]'), '');

            // If no alphabetic characters found, return original username
            if (nameOnly.isEmpty) {
              return username;
            }

            return nameOnly;
          }

          // Only call API when train number actually changes
          if (trainNumber != _previousTrainNumber && trainNumber.isNotEmpty) {
            _previousTrainNumber = trainNumber;
            WidgetsBinding.instance.addPostFrameCallback((_) {
              final selectedDateStr =
                  DateFormat('yyyy-MM-dd').format(_selectedDate);
              fetchTrainsCoachWise(trainNumber, selectedDateStr);
            });
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              LayoutBuilder(
                builder: (context, constraints) {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize:
                        MainAxisSize.min, // Make the row take only needed space
                    children: [
                      Flexible(
                        child: Text(
                          '{ ${extractNameFromUsername(userModel.userName)} ${roleLetter.isNotEmpty ? '- $roleLetter' : ''} }',
                          style: const TextStyle(
                            color: Colors.black87,
                            fontSize: 14.0,
                          ),
                          overflow: TextOverflow
                              .ellipsis, // Add ellipsis for overflow
                        ),
                      ),

                      // Show empty brackets if both are empty
                      if (depotCode.isEmpty) ...[
                        const SizedBox(width: 5),
                        const Text(
                          '{ N/A }',
                          style: TextStyle(
                            color: Colors.black87,
                            fontSize: 14.0,
                          ),
                        ),
                      ],

                      // Show depot code if available
                      if (depotCode.isNotEmpty) ...[
                        Text(
                          ' - { $depotCode }',
                          style: const TextStyle(
                            color: Colors.black87,
                            fontSize: 14.0,
                          ),
                        ),
                      ],
                    ],
                  );
                },
              ),
              LayoutBuilder(
                builder: (context, constraints) {
                  if (constraints.maxWidth < 600) {
                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(width: 5),
                        if (_inDate == null && _outDate == null) ...[
                          const Text(
                            '[ N/A ]',
                            style: TextStyle(
                              color: Colors.black87,
                              fontSize: 14.0,
                            ),
                          ),
                          const SizedBox(width: 5),
                          const Text(
                            '[ N/A ]',
                            style: TextStyle(
                              color: Colors.black87,
                              fontSize: 14.0,
                            ),
                          ),
                        ],

                        // Modified logic for displaying dates or N/A
                        if (_inDate != null || _outDate != null) ...[
                          const SizedBox(height: 2),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const SizedBox(height: 2),
                              if (_outDate != null) ...[
                                Text(
                                  '[ ${_outTrain!.isNotEmpty ? '$_outTrain - ' : ''}${_formatApiDateToDisplay(_outDate!)} ]',
                                  style: const TextStyle(
                                    color: Colors.black87,
                                    fontSize: 14.0,
                                  ),
                                ),
                              ] else ...[
                                // Show N/A when both dates are null/empty
                                const Text(
                                  '[ N/A ]',
                                  style: TextStyle(
                                    color: Colors.black87,
                                    fontSize: 14.0,
                                  ),
                                ),
                              ],
                              const SizedBox(width: 2),
                              if (_inDate != null) ...[
                                Text(
                                  '[ ${_inTrain!.isNotEmpty ? '$_inTrain - ' : ''}${_formatApiDateToDisplay(_inDate!)} ]',
                                  style: const TextStyle(
                                    color: Colors.black87,
                                    fontSize: 14.0,
                                  ),
                                ),
                              ] else ...[
                                // Show N/A when both dates are null/empty/
                                const Text(
                                  '[ N/A ]',
                                  style: TextStyle(
                                    color: Colors.black87,
                                    fontSize: 14.0,
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ]
                      ],
                    );
                  } else {
                    return Container();
                  }
                },
              ),
            ],
          );
        },
      ),
      actions: [
        // Add custom actions first if provided
        if (widget.actions != null) ...widget.actions!,

        // Wrap in Flexible to prevent overflow
        Flexible(
          child: Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: UploadStatusIndicator(
              onViewDetails: () =>
                  _showUploadStatusDetails(context, uploadManager),
            ),
          ),
        ),

        // Keep version info and menu constant
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
                height: 38,
                child: PopupMenuButton<String>(
                  icon: const Icon(Icons.more_horiz, color: Colors.black87),
                  offset: const Offset(0, kToolbarHeight),
                  onSelected: (String result) async {
                    if (result == AppLocalizations.of(context).text_profile) {
                      Navigator.of(context).pushNamed('/edit_profile');
                    }
                  },
                  itemBuilder: (BuildContext context) {
                    final profileText =
                        AppLocalizations.of(context).text_profile;
                    return {profileText}.map((String choice) {
                      return PopupMenuItem<String>(
                        value: choice,
                        child: Row(
                          children: [
                            if (choice == profileText) const Icon(Icons.person),
                            const SizedBox(width: 10),
                            Text(choice),
                          ],
                        ),
                      );
                    }).toList();
                  },
                )),
            if (appVersion.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(right: 3.0),
                child: Text(
                  'v$appVersion',
                  style: const TextStyle(
                    color: Colors.black87,
                    fontSize: 11.0,
                  ),
                ),
              ),
          ],
        ),
      ],
    );
  }

  String _getRoleLetter(String userType) {
    switch (userType) {
      case 'coach attendent':
        return 'CA';
      case 'contractor':
        return 'CT';
      case 'contractor admin':
        return 'CAd';
      case 'EHK':
        return 'EK';
      case 'OBHS':
        return 'HS';
      case 'railway admin':
        return 'RA';
      case 'railway officer':
        return 'RO';
      case 's2 admin':
        return 'S2A';
      case 'war room user':
        return 'WU';
      case 'write read':
        return 'WR';
      case 'passenger':
        return 'PS';
      default:
        return userType.isNotEmpty ? userType[0].toUpperCase() : '';
    }
  }

  void _showUploadStatusDetails(
      BuildContext context, UploadManager uploadManager) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Upload Status'),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (uploadManager.isCompressing)
                const ListTile(
                  leading: SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  title: Text('Compressing image'),
                  dense: true,
                ),
              if (uploadManager.backgroundTasks.isNotEmpty)
                const Padding(
                  padding: EdgeInsets.only(top: 8.0, bottom: 4.0),
                  child: Text(
                    'Active Uploads:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ...uploadManager.backgroundTasks.entries.map((entry) => ListTile(
                    leading: SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        value: entry.value.progress,
                      ),
                    ),
                    title: Text('Upload ${entry.key.substring(0, 6)}...'),
                    subtitle: Text(
                        '${(entry.value.progress * 100).toStringAsFixed(0)}% complete'),
                    dense: true,
                  )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
