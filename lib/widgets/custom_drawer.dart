import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:railops/models/notification_provider.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:railops/models/user_model.dart';
import 'package:railops/routes.dart';
import 'package:railops/utils/permissions.dart';
import 'package:railops/widgets/language_selector.dart';

class CustomDrawer extends StatefulWidget {
  const CustomDrawer({super.key});

  @override
  _CustomDrawerState createState() => _CustomDrawerState();
}

class _CustomDrawerState extends State<CustomDrawer> {
  String? userType;

  @override
  void initState() {
    super.initState();
    final userModel = Provider.of<UserModel>(context, listen: false);
    userType = userModel.userType;
  }

  List<String> readPermissions = Permissions.getReadPermissions();
  List<String> writePermissions = Permissions.getWritePermissions();
  List<String> adminPermissions = Permissions.getAdminPermissions();
  List<String> contractAdminPermissions =
      Permissions.getContractAdminPermissions();
  List<String> mcc_to_obhs_permissions =
      Permissions.mccToObhsHandoverPermissions;
  List<String> obhs_to_mcc_permissions = Permissions.obhsToMccHandover;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width * 0.6,
      child: Drawer(
        child: Column(
          children: <Widget>[
            Stack(
              children: [
                DrawerHeader(
                  decoration: const BoxDecoration(color: Colors.white),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      AppLocalizations.of(context).text_menu,
                      style:
                          const TextStyle(color: Colors.black87, fontSize: 24),
                    ),
                  ),
                ),
                Positioned(
                  top: 8.0,
                  left: 8.0,
                  child: IconButton(
                    icon: const Icon(Icons.menu, color: Colors.black87),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                ),
              ],
            ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: <Widget>[
                  Consumer<NotificationProvider>(
                    builder: (context, notificationProvider, child) {
                      return ListTile(
                        leading: Badge(
                          isLabelVisible: notificationProvider.unreadCount > 0,
                          label: Text(
                            notificationProvider.unreadCount.toString(),
                            style: const TextStyle(color: Colors.white),
                          ),
                          child: const Icon(Icons.notifications),
                        ),
                        title: const Text('Notifications'),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(
                              context, Routes.notificationCenter);
                        },
                      );
                    },
                  ),
                  if (userType == 'passenger' ||
                      readPermissions.contains(userType))
                    ListTile(
                      leading: const Icon(Icons.person),
                      title:
                          Text(AppLocalizations.of(context).text_train_tracker),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, Routes.attendance);
                      },
                    ),
                  if (userType == 'passenger' ||
                      readPermissions.contains(userType))
                    ListTile(
                      leading: const Icon(Icons.person_add),
                      title: Text(AppLocalizations.of(context).text_assign_ca),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, Routes.AssignEhkCa);
                      },
                    ),
                  if (userType == 'passenger' ||
                      readPermissions.contains(userType))
                    ListTile(
                      leading: const Icon(Icons.person_add),
                      title: Text(AppLocalizations.of(context).text_assign_cs),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, Routes.assignObhs);
                      },
                    ),
                  if (userType == 'passenger' ||
                      readPermissions.contains(userType))
                    ListTile(
                      leading: const Icon(Icons.checklist_sharp),
                      title:
                          Text(AppLocalizations.of(context).text_pnr_details),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, Routes.pnrScrren);
                      },
                    ),
                  // if (userType == 'passenger' || readPermissions.contains(userType))
                  //   ListTile(
                  //     leading: Stack(
                  //       alignment: Alignment.bottomRight,
                  //       children: [
                  //         const Icon(Icons.train),
                  //         Positioned(
                  //           right: 0,
                  //           bottom: 0,
                  //           child: Icon(Icons.feedback, size: 14, color: Color(0xFF1E88E5)),
                  //         ),
                  //       ],
                  //     ),
                  //     title: const Text('Rail Sathi'),
                  //     onTap: () {
                  //       Navigator.pop(context);
                  //       Navigator.pushNamed(context, Routes.railSathi);
                  //     },
                  //   ),
                  if (userType == 's2 admin' ||
                      userType == 'railway admin' ||
                      userType == 'contactor admin') ...{
                    if (adminPermissions.contains(userType)) ...{
                      ListTile(
                        leading: const Icon(Icons.train),
                        title: Text(
                            AppLocalizations.of(context).text_passenger_chart),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, Routes.TrainDetails);
                        },
                      ),
                    }
                  } else if (userType != 'passenger') ...{
                    if (readPermissions.contains(userType)) ...{
                      ListTile(
                        leading: const Icon(Icons.train),
                        title: Text(
                            AppLocalizations.of(context).text_passenger_chart),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, Routes.attendance);
                        },
                      ),
                    }
                  },

                  if (userType != 'passenger') ...[
                    ListTile(
                      leading: const Icon(Icons.location_pin),
                      title: Text(AppLocalizations.of(context).text_map_screen),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, Routes.mapScreen);
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.train),
                      title:
                          Text(AppLocalizations.of(context).text_configuration),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, Routes.editTrain);
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.notes),
                      title: Text(AppLocalizations.of(context).text_reports),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, Routes.pdfScreen);
                      },
                    ),
                    if (readPermissions.contains(userType))
                      ListTile(
                        leading: const Icon(Icons.person),
                        title: Text(AppLocalizations.of(context)
                            .text_passenger_feedback),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(
                              context, Routes.passengerFeedbackScreen);
                        },
                      ),
                    ListTile(
                      leading: const Icon(Icons.train_outlined),
                      title: Text(AppLocalizations.of(context)
                          .text_rake_deficiency_report_1),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.pushNamed(context, Routes.tripReport);
                      },
                    ),
                    if (obhs_to_mcc_permissions.contains(userType))
                      ListTile(
                        leading: const Icon(Icons.train_outlined),
                        title:
                            Text(AppLocalizations.of(context).text_obhs_to_mcc),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(
                              context, Routes.obhsToMccHandover);
                        },
                      ),
                    if (mcc_to_obhs_permissions.contains(userType))
                      ListTile(
                        leading: const Icon(Icons.train_outlined),
                        title:
                            Text(AppLocalizations.of(context).text_mcc_to_obhs),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(
                              context, Routes.mccToObhsHandover);
                        },
                      ),
                    if (writePermissions.contains(userType))
                      ListTile(
                        leading: const Icon(Icons.upload_file),
                        title:
                            Text(AppLocalizations.of(context).text_upload_data),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, Routes.uploadData);
                        },
                      ),
                    if (userType == 'passenger' ||
                        readPermissions.contains(userType))
                      ListTile(
                        leading: const Icon(Icons.person_add),
                        title: Text(
                            AppLocalizations.of(context).text_user_management),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, Routes.userInfo);
                        },
                      ),
                    if (writePermissions.contains(userType))
                      ListTile(
                        leading: const Icon(Icons.add_alarm_sharp),
                        title: Text(
                            AppLocalizations.of(context).text_issue_management),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, Routes.issueScrren);
                        },
                      ),
                    if (userType == 'passenger' ||
                        readPermissions.contains(userType))
                      ListTile(
                        leading: const Icon(Icons.qr_code),
                        title: Text(
                            AppLocalizations.of(context).text_rail_sathi_qr),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, Routes.railSathiQr);
                        },
                      ),
                    if (userType == 'passenger' ||
                        readPermissions.contains(userType))
                      ListTile(
                        leading: const Icon(Icons.support_agent),
                        title: Text(
                            AppLocalizations.of(context).text_customer_care),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.pushNamed(context, Routes.customerCare);
                        },
                      ),
                    // Language Selector - Available to all users
                    const LanguageSelectorListTile(),
                  ]
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
