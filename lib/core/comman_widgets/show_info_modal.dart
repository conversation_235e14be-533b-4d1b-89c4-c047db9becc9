import 'package:flutter/material.dart';

void showInfoModal(BuildContext context, String infoMessage, String messageType, VoidCallback onClose) {
  showDialog(
    context: context,
    barrierDismissible: true, // Allow dismiss on outside tap
    builder: (BuildContext context) {
      return AlertDialog(
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(messageType),
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: () {
                onClose();
                Navigator.of(context).pop();
              },
            ),
          ],
        ),
        content: Text(infoMessage),
        actions: <Widget>[
          TextButton(
            child: const Text('Close'),
            onPressed: () {
              onClose();
              Navigator.of(context).pop();
            },
          ),
        ],
      );
    },
  );
}