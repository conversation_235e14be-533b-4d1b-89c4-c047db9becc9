import 'package:flutter/material.dart';
import 'package:railops/core/utilities/color_constants.dart';
import 'package:railops/core/utilities/size_config.dart';

class AppButtonWithIcon extends StatelessWidget {
  String title = "";
  double? mWidth = null;
  double? mHeight = 50;
  Function()? onTap;
  TextStyle? textStyle;
  Widget? icon;
  Icon? iconData;
  Color? buttonColor;
  Color? borderColor;
  double? iconSize;
  double? borderRadius;
  bool? isEnabled;
  Color? fontColor;

  AppButtonWithIcon(
      {super.key,
      required this.title,
      this.mWidth,
      this.onTap,
      this.mHeight,
      this.textStyle,
      this.icon,
      this.buttonColor,
      this.borderColor,
      this.iconSize,
      this.iconData,
      this.fontColor,
      this.borderRadius,
      this.isEnabled});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        splashColor: kWhiteColor.withValues(alpha: 0.4),
        highlightColor: kSecondaryColor.withValues(alpha: 0.4),
        splashFactory: InkRipple.splashFactory,
        onTap: onTap,
        child: Ink(
          height: mHeight ?? 50,
          width: mWidth ?? SizeConfig.screenWidth * 0.8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(borderRadius ?? 10),
            border: borderColor != null
                ? Border.all(color: borderColor!, width: 1)
                : null,
            gradient: LinearGradient(
              begin: const Alignment(-0.5, 1),
              end: const Alignment(1, 1),
              colors: [
                buttonColor ?? kPrimaryColor,
                buttonColor ?? kPrimaryColor,
              ],
            ),
          ),
          child: Center(
            child: Padding(
              padding: const EdgeInsets.only(left: 20.0, right: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding:
                        EdgeInsets.only(right: getProportionateScreenHeight(5)),
                    child: Text(
                      title,
                      style: textStyle ??
                          TextStyle(
                              fontWeight: FontWeight.normal,
                              color: (fontColor != null)
                                  ? fontColor
                                  : kWhiteColor),
                    ),
                  ),
                  const Spacer(),
                  icon ??
                      const Icon(
                        Icons.arrow_forward,
                        color: kWhiteColor,
                      ),
                  Padding(
                    padding: EdgeInsets.only(
                        right: getProportionateScreenHeight(5),
                        left: getProportionateScreenHeight(5)),
                    child: iconData ?? const SizedBox.shrink(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
