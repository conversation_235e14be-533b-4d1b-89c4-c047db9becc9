// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBCbuv3LEODNdQ2GYDTFVk37WElOxQW7F8',
    appId: '1:513557807469:web:4f88f89be348433a1e5a64',
    messagingSenderId: '513557807469',
    projectId: 'railwaysapp-prod',
    authDomain: 'railwaysapp-prod.firebaseapp.com',
    storageBucket: 'railwaysapp-prod.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCEjC5KzouydftXlurNrxYMnJJEzAHDaYY',
    appId: '1:513557807469:android:0f06856533db70111e5a64',
    messagingSenderId: '513557807469',
    projectId: 'railwaysapp-prod',
    storageBucket: 'railwaysapp-prod.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBT4LjgQQ_2hwjbarhjlAgIJugv40CG0rM',
    appId: '1:513557807469:ios:7efc23d0a5be0a591e5a64',
    messagingSenderId: '513557807469',
    projectId: 'railwaysapp-prod',
    storageBucket: 'railwaysapp-prod.firebasestorage.app',
    androidClientId: '513557807469-tb6jssdqkdo6aheoh6bc3conpfs5g84c.apps.googleusercontent.com',
    iosClientId: '513557807469-k91bmql2hqr2034kvf2dep4vh0afqj9t.apps.googleusercontent.com',
    iosBundleId: 'com.biputri.railops',
  );
}
