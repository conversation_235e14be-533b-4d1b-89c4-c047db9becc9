import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:developer';

/// Enum for notification types used in categorization
enum NotificationType {
  general,
  onboarding,
  stationApproach,
  boardingAlert,
  offBoardingAlert,
  proximityAlert,
  trainStatus,
  coachSpecific,
}

/// Extension to convert NotificationType to/from string for JSON serialization
extension NotificationTypeExtension on NotificationType {
  String get value {
    switch (this) {
      case NotificationType.general:
        return 'general';
      case NotificationType.onboarding:
        return 'onboarding';
      case NotificationType.stationApproach:
        return 'station_approach';
      case NotificationType.boardingAlert:
        return 'boarding_alert';
      case NotificationType.offBoardingAlert:
        return 'off_boarding_alert';
      case NotificationType.proximityAlert:
        return 'proximity_alert';
      case NotificationType.trainStatus:
        return 'train_status';
      case NotificationType.coachSpecific:
        return 'coach_specific';
    }
  }

  static NotificationType fromString(String value) {
    switch (value) {
      case 'general':
        return NotificationType.general;
      case 'onboarding':
        return NotificationType.onboarding;
      case 'station_approach':
        return NotificationType.stationApproach;
      case 'boarding_alert':
        return NotificationType.boardingAlert;
      case 'off_boarding_alert':
        return NotificationType.offBoardingAlert;
      case 'proximity_alert':
        return NotificationType.proximityAlert;
      case 'train_status':
        return NotificationType.trainStatus;
      case 'coach_specific':
        return NotificationType.coachSpecific;
      default:
        return NotificationType.general;
    }
  }
}

/// Model for storing user notification preferences
/// Integrates with SharedPreferences for persistent storage
class NotificationPreferencesModel {
  // Core onboarding notification settings
  final bool enableOnboardingNotifications;
  final bool enableStationApproachNotifications;
  final bool enableBoardingAlerts;
  final bool enableOffBoardingAlerts;
  final bool enableProximityAlerts;

  // Timing preferences
  final int advanceNoticeMinutes; // Minutes before event to show notification
  final int proximityThresholdKm; // Kilometers for proximity alerts

  // Coach-specific filters
  final List<String> enabledCoachTypes; // e.g., ["AC1", "AC2", "SL", "CC"]
  final bool enableCoachSpecificFiltering;

  // Sound and vibration preferences
  final bool enableSound;
  final bool enableVibration;
  final String notificationTone; // For future use

  // Advanced settings
  final bool enableBackgroundNotifications;
  final bool enableLocationBasedNotifications;
  final int maxNotificationsPerHour; // Rate limiting

  const NotificationPreferencesModel({
    this.enableOnboardingNotifications = true,
    this.enableStationApproachNotifications =
        false, // Disabled by default to avoid spam
    this.enableBoardingAlerts = true,
    this.enableOffBoardingAlerts = true,
    this.enableProximityAlerts = true,
    this.advanceNoticeMinutes = 5,
    this.proximityThresholdKm = 2,
    this.enabledCoachTypes = const ["AC1", "AC2", "AC3", "SL", "CC", "2S"],
    this.enableCoachSpecificFiltering = false,
    this.enableSound = true,
    this.enableVibration = true,
    this.notificationTone = 'default',
    this.enableBackgroundNotifications = true,
    this.enableLocationBasedNotifications = true,
    this.maxNotificationsPerHour = 10,
  });

  /// Create default preferences
  factory NotificationPreferencesModel.defaultPreferences() {
    return const NotificationPreferencesModel();
  }

  /// Create from JSON (for SharedPreferences storage)
  factory NotificationPreferencesModel.fromJson(Map<String, dynamic> json) {
    return NotificationPreferencesModel(
      enableOnboardingNotifications:
          json['enableOnboardingNotifications'] ?? true,
      enableStationApproachNotifications:
          json['enableStationApproachNotifications'] ?? false,
      enableBoardingAlerts: json['enableBoardingAlerts'] ?? true,
      enableOffBoardingAlerts: json['enableOffBoardingAlerts'] ?? true,
      enableProximityAlerts: json['enableProximityAlerts'] ?? true,
      advanceNoticeMinutes: json['advanceNoticeMinutes'] ?? 5,
      proximityThresholdKm: json['proximityThresholdKm'] ?? 2,
      enabledCoachTypes: List<String>.from(
          json['enabledCoachTypes'] ?? ["AC1", "AC2", "AC3", "SL", "CC", "2S"]),
      enableCoachSpecificFiltering:
          json['enableCoachSpecificFiltering'] ?? false,
      enableSound: json['enableSound'] ?? true,
      enableVibration: json['enableVibration'] ?? true,
      notificationTone: json['notificationTone'] ?? 'default',
      enableBackgroundNotifications:
          json['enableBackgroundNotifications'] ?? true,
      enableLocationBasedNotifications:
          json['enableLocationBasedNotifications'] ?? true,
      maxNotificationsPerHour: json['maxNotificationsPerHour'] ?? 10,
    );
  }

  /// Convert to JSON (for SharedPreferences storage)
  Map<String, dynamic> toJson() {
    return {
      'enableOnboardingNotifications': enableOnboardingNotifications,
      'enableStationApproachNotifications': enableStationApproachNotifications,
      'enableBoardingAlerts': enableBoardingAlerts,
      'enableOffBoardingAlerts': enableOffBoardingAlerts,
      'enableProximityAlerts': enableProximityAlerts,
      'advanceNoticeMinutes': advanceNoticeMinutes,
      'proximityThresholdKm': proximityThresholdKm,
      'enabledCoachTypes': enabledCoachTypes,
      'enableCoachSpecificFiltering': enableCoachSpecificFiltering,
      'enableSound': enableSound,
      'enableVibration': enableVibration,
      'notificationTone': notificationTone,
      'enableBackgroundNotifications': enableBackgroundNotifications,
      'enableLocationBasedNotifications': enableLocationBasedNotifications,
      'maxNotificationsPerHour': maxNotificationsPerHour,
    };
  }

  /// Create a copy with modified values
  NotificationPreferencesModel copyWith({
    bool? enableOnboardingNotifications,
    bool? enableStationApproachNotifications,
    bool? enableBoardingAlerts,
    bool? enableOffBoardingAlerts,
    bool? enableProximityAlerts,
    int? advanceNoticeMinutes,
    int? proximityThresholdKm,
    List<String>? enabledCoachTypes,
    bool? enableCoachSpecificFiltering,
    bool? enableSound,
    bool? enableVibration,
    String? notificationTone,
    bool? enableBackgroundNotifications,
    bool? enableLocationBasedNotifications,
    int? maxNotificationsPerHour,
  }) {
    return NotificationPreferencesModel(
      enableOnboardingNotifications:
          enableOnboardingNotifications ?? this.enableOnboardingNotifications,
      enableStationApproachNotifications: enableStationApproachNotifications ??
          this.enableStationApproachNotifications,
      enableBoardingAlerts: enableBoardingAlerts ?? this.enableBoardingAlerts,
      enableOffBoardingAlerts:
          enableOffBoardingAlerts ?? this.enableOffBoardingAlerts,
      enableProximityAlerts:
          enableProximityAlerts ?? this.enableProximityAlerts,
      advanceNoticeMinutes: advanceNoticeMinutes ?? this.advanceNoticeMinutes,
      proximityThresholdKm: proximityThresholdKm ?? this.proximityThresholdKm,
      enabledCoachTypes: enabledCoachTypes ?? this.enabledCoachTypes,
      enableCoachSpecificFiltering:
          enableCoachSpecificFiltering ?? this.enableCoachSpecificFiltering,
      enableSound: enableSound ?? this.enableSound,
      enableVibration: enableVibration ?? this.enableVibration,
      notificationTone: notificationTone ?? this.notificationTone,
      enableBackgroundNotifications:
          enableBackgroundNotifications ?? this.enableBackgroundNotifications,
      enableLocationBasedNotifications: enableLocationBasedNotifications ??
          this.enableLocationBasedNotifications,
      maxNotificationsPerHour:
          maxNotificationsPerHour ?? this.maxNotificationsPerHour,
    );
  }

  /// Validation methods
  bool get isValid {
    return advanceNoticeMinutes >= 1 &&
        advanceNoticeMinutes <= 60 &&
        proximityThresholdKm >= 1 &&
        proximityThresholdKm <= 50 &&
        maxNotificationsPerHour >= 1 &&
        maxNotificationsPerHour <= 100;
  }

  /// Check if a specific notification type should be shown
  bool shouldShowNotificationType(NotificationType type) {
    switch (type) {
      case NotificationType.general:
        return true; // General notifications are always enabled
      case NotificationType.onboarding:
        return enableOnboardingNotifications;
      case NotificationType.stationApproach:
        return enableOnboardingNotifications &&
            enableStationApproachNotifications;
      case NotificationType.boardingAlert:
        return enableOnboardingNotifications && enableBoardingAlerts;
      case NotificationType.offBoardingAlert:
        return enableOnboardingNotifications && enableOffBoardingAlerts;
      case NotificationType.proximityAlert:
        return enableOnboardingNotifications && enableProximityAlerts;
      case NotificationType.trainStatus:
        return enableOnboardingNotifications;
      case NotificationType.coachSpecific:
        return enableOnboardingNotifications && enableCoachSpecificFiltering;
    }
  }

  /// Check if a specific coach type should receive notifications
  bool shouldShowForCoachType(String coachType) {
    if (!enableCoachSpecificFiltering) {
      return true; // If filtering is disabled, show for all coaches
    }
    return enabledCoachTypes.contains(coachType.toUpperCase());
  }

  @override
  String toString() {
    return 'NotificationPreferencesModel(enableOnboarding: $enableOnboardingNotifications, '
        'advanceNotice: ${advanceNoticeMinutes}min, coaches: $enabledCoachTypes)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationPreferencesModel &&
        other.enableOnboardingNotifications == enableOnboardingNotifications &&
        other.enableStationApproachNotifications ==
            enableStationApproachNotifications &&
        other.enableBoardingAlerts == enableBoardingAlerts &&
        other.enableOffBoardingAlerts == enableOffBoardingAlerts &&
        other.enableProximityAlerts == enableProximityAlerts &&
        other.advanceNoticeMinutes == advanceNoticeMinutes &&
        other.proximityThresholdKm == proximityThresholdKm &&
        other.enabledCoachTypes.toString() == enabledCoachTypes.toString() &&
        other.enableCoachSpecificFiltering == enableCoachSpecificFiltering &&
        other.enableSound == enableSound &&
        other.enableVibration == enableVibration &&
        other.notificationTone == notificationTone &&
        other.enableBackgroundNotifications == enableBackgroundNotifications &&
        other.enableLocationBasedNotifications ==
            enableLocationBasedNotifications &&
        other.maxNotificationsPerHour == maxNotificationsPerHour;
  }

  @override
  int get hashCode {
    return Object.hash(
      enableOnboardingNotifications,
      enableStationApproachNotifications,
      enableBoardingAlerts,
      enableOffBoardingAlerts,
      enableProximityAlerts,
      advanceNoticeMinutes,
      proximityThresholdKm,
      enabledCoachTypes.toString(),
      enableCoachSpecificFiltering,
      enableSound,
      enableVibration,
      notificationTone,
      enableBackgroundNotifications,
      enableLocationBasedNotifications,
      maxNotificationsPerHour,
    );
  }
}

/// Service class for managing notification preferences with SharedPreferences
class NotificationPreferencesService {
  static const String _preferencesKey = 'notification_preferences';

  /// Load preferences from SharedPreferences
  static Future<NotificationPreferencesModel> loadPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final preferencesJson = prefs.getString(_preferencesKey);

      if (preferencesJson != null) {
        final Map<String, dynamic> json = jsonDecode(preferencesJson);
        return NotificationPreferencesModel.fromJson(json);
      }
    } catch (e) {
      log('Error loading notification preferences: $e');
    }

    // Return default preferences if loading fails or no preferences exist
    return NotificationPreferencesModel.defaultPreferences();
  }

  /// Save preferences to SharedPreferences
  static Future<bool> savePreferences(
      NotificationPreferencesModel preferences) async {
    try {
      if (!preferences.isValid) {
        log('Invalid notification preferences, not saving');
        return false;
      }

      final prefs = await SharedPreferences.getInstance();
      final preferencesJson = jsonEncode(preferences.toJson());
      return await prefs.setString(_preferencesKey, preferencesJson);
    } catch (e) {
      log('Error saving notification preferences: $e');
      return false;
    }
  }

  /// Clear all preferences (reset to defaults)
  static Future<bool> clearPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_preferencesKey);
    } catch (e) {
      log('Error clearing notification preferences: $e');
      return false;
    }
  }

  /// Check if preferences exist
  static Future<bool> hasPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey(_preferencesKey);
    } catch (e) {
      log('Error checking notification preferences: $e');
      return false;
    }
  }
}
