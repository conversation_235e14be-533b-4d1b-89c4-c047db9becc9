{"../lib/core/comman_widgets/input_fields/otp_boxes.dart": {"text_widgets": [{"text": "Re-enter OTP", "file": "../lib/core/comman_widgets/input_fields/otp_boxes.dart", "line_number": 67, "category": "text_widgets", "suggested_key": "text_reenter_otp"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/utils/permission_handler_service.dart": {"text_widgets": [{"text": "<PERSON><PERSON>", "file": "../lib/utils/permission_handler_service.dart", "line_number": 98, "category": "text_widgets", "suggested_key": "text_deny"}, {"text": "Enable", "file": "../lib/utils/permission_handler_service.dart", "line_number": 111, "category": "text_widgets", "suggested_key": "text_enable"}], "app_bar_titles": [], "form_labels": [], "button_labels": [{"text": "<PERSON><PERSON>", "file": "../lib/utils/permission_handler_service.dart", "line_number": 98, "category": "button_labels", "suggested_key": "btn_deny"}, {"text": "Enable", "file": "../lib/utils/permission_handler_service.dart", "line_number": 111, "category": "button_labels", "suggested_key": "btn_enable"}], "snackbar_messages": [], "dialog_content": []}, "../lib/utils/show_location_permission_disclosure.dart": {"text_widgets": [{"text": "Location Access Required", "file": "../lib/utils/show_location_permission_disclosure.dart", "line_number": 8, "category": "text_widgets", "suggested_key": "text_location_access_required"}, {"text": "Decline", "file": "../lib/utils/show_location_permission_disclosure.dart", "line_number": 27, "category": "text_widgets", "suggested_key": "text_decline"}, {"text": "Accept", "file": "../lib/utils/show_location_permission_disclosure.dart", "line_number": 34, "category": "text_widgets", "suggested_key": "text_accept"}], "app_bar_titles": [{"text": "Location Access Required", "file": "../lib/utils/show_location_permission_disclosure.dart", "line_number": 8, "category": "app_bar_titles", "suggested_key": "title_location_access_required"}], "form_labels": [], "button_labels": [{"text": "Decline", "file": "../lib/utils/show_location_permission_disclosure.dart", "line_number": 27, "category": "button_labels", "suggested_key": "btn_decline"}, {"text": "Accept", "file": "../lib/utils/show_location_permission_disclosure.dart", "line_number": 34, "category": "button_labels", "suggested_key": "btn_accept"}], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/attendance/widget/delete_button.dart": {"text_widgets": [{"text": "Confirm Delete", "file": "../lib/screens/attendance/widget/delete_button.dart", "line_number": 95, "category": "text_widgets", "suggested_key": "text_confirm_delete"}, {"text": "Cancel", "file": "../lib/screens/attendance/widget/delete_button.dart", "line_number": 104, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Delete", "file": "../lib/screens/attendance/widget/delete_button.dart", "line_number": 114, "category": "text_widgets", "suggested_key": "text_delete"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/attendance/attendance_screen.dart": {"text_widgets": [{"text": "Storage permission is required to download files", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 191, "category": "text_widgets", "suggested_key": "text_storage_permission_is"}, {"text": "Please select a train first", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 209, "category": "text_widgets", "suggested_key": "text_please_select_a"}, {"text": "Update Required", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 394, "category": "text_widgets", "suggested_key": "text_update_required"}, {"text": "Update Now", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 417, "category": "text_widgets", "suggested_key": "text_update_now"}, {"text": "Please select a train number and date.", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 430, "category": "text_widgets", "suggested_key": "text_please_select_a"}, {"text": "All details updated successfully.", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 474, "category": "text_widgets", "suggested_key": "text_all_details_updated"}, {"text": "Failed to update details: $e", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 482, "category": "text_widgets", "suggested_key": "text_failed_to_update"}, {"text": "Data refreshed successfully", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1030, "category": "text_widgets", "suggested_key": "text_data_refreshed_successfully"}, {"text": "Train Location Saved Successfully", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1059, "category": "text_widgets", "suggested_key": "text_train_location_saved"}, {"text": "Self", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1898, "category": "text_widgets", "suggested_key": "text_self"}, {"text": "Other CA", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1902, "category": "text_widgets", "suggested_key": "text_other_ca"}, {"text": "Other EHK/OBHS", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1906, "category": "text_widgets", "suggested_key": "text_other_ehkobhs"}, {"text": "Self", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1941, "category": "text_widgets", "suggested_key": "text_self"}, {"text": "Other CA", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1945, "category": "text_widgets", "suggested_key": "text_other_ca"}, {"text": "Other EHK/OBHS", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1949, "category": "text_widgets", "suggested_key": "text_other_ehkobhs"}, {"text": "Chart has not been prepared for this station", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 2076, "category": "text_widgets", "suggested_key": "text_chart_has_not"}], "app_bar_titles": [], "form_labels": [{"text": "Train Number", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1324, "category": "form_labels", "suggested_key": "form_train_number"}, {"text": "Date", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1379, "category": "form_labels", "suggested_key": "form_date"}], "button_labels": [{"text": "Self", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1898, "category": "button_labels", "suggested_key": "btn_self"}, {"text": "Other CA", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1902, "category": "button_labels", "suggested_key": "btn_other_ca"}, {"text": "Other EHK/OBHS", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1906, "category": "button_labels", "suggested_key": "btn_other_ehkobhs"}, {"text": "Self", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1941, "category": "button_labels", "suggested_key": "btn_self"}, {"text": "Other CA", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1945, "category": "button_labels", "suggested_key": "btn_other_ca"}, {"text": "Other EHK/OBHS", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1949, "category": "button_labels", "suggested_key": "btn_other_ehkobhs"}], "snackbar_messages": [{"text": "Please select a train first", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 209, "category": "snackbar_messages", "suggested_key": "snackbar_please_select_a"}, {"text": "Data refreshed successfully", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1030, "category": "snackbar_messages", "suggested_key": "snackbar_data_refreshed_successfully"}, {"text": "Train Location Saved Successfully", "file": "../lib/screens/attendance/attendance_screen.dart", "line_number": 1059, "category": "snackbar_messages", "suggested_key": "snackbar_train_location_saved"}], "dialog_content": []}, "../lib/screens/attendance/image_upload.dart": {"text_widgets": [{"text": "Camera", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 197, "category": "text_widgets", "suggested_key": "text_camera"}, {"text": "Gallery", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 203, "category": "text_widgets", "suggested_key": "text_gallery"}, {"text": "Getting location...", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 274, "category": "text_widgets", "suggested_key": "text_getting_location"}, {"text": "Attendance marked successfully!", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 313, "category": "text_widgets", "suggested_key": "text_attendance_marked_successfully"}, {"text": "Error: $message", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 324, "category": "text_widgets", "suggested_key": "text_error_message"}, {"text": "Failed to get location: $e", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 335, "category": "text_widgets", "suggested_key": "text_failed_to_get"}, {"text": "Error fetching images: $e", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 402, "category": "text_widgets", "suggested_key": "text_error_fetching_images"}, {"text": "Error fetching images: $e", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 433, "category": "text_widgets", "suggested_key": "text_error_fetching_images"}, {"text": "Attendance Already Submitted", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 467, "category": "text_widgets", "suggested_key": "text_attendance_already_submitted"}, {"text": "Back to all users", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 595, "category": "text_widgets", "suggested_key": "text_back_to_all"}, {"text": "Submit", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 709, "category": "text_widgets", "suggested_key": "text_submit"}, {"text": "Upload Status", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 781, "category": "text_widgets", "suggested_key": "text_upload_status"}, {"text": "Compressing image", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 795, "category": "text_widgets", "suggested_key": "text_compressing_image"}, {"text": "Upload ${entry.key.substring(0, 6)}...", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 816, "category": "text_widgets", "suggested_key": "text_upload_entrykeysubstring0_6"}, {"text": "Close", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 828, "category": "text_widgets", "suggested_key": "text_close"}, {"text": "Image Uploading", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 841, "category": "text_widgets", "suggested_key": "text_image_uploading"}, {"text": "Submit", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 989, "category": "text_widgets", "suggested_key": "text_submit"}], "app_bar_titles": [{"text": "Upload Status", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 781, "category": "app_bar_titles", "suggested_key": "title_upload_status"}, {"text": "Compressing image", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 795, "category": "app_bar_titles", "suggested_key": "title_compressing_image"}, {"text": "Upload ${entry.key.substring(0, 6)}...", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 816, "category": "app_bar_titles", "suggested_key": "title_upload_entrykeysubstring0_6"}], "form_labels": [], "button_labels": [{"text": "Close", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 828, "category": "button_labels", "suggested_key": "btn_close"}], "snackbar_messages": [{"text": "Error fetching images: $e", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 402, "category": "snackbar_messages", "suggested_key": "snackbar_error_fetching_images"}, {"text": "Error fetching images: $e", "file": "../lib/screens/attendance/image_upload.dart", "line_number": 433, "category": "snackbar_messages", "suggested_key": "snackbar_error_fetching_images"}], "dialog_content": []}, "../lib/screens/attendance/attendance_details.dart": {"text_widgets": [{"text": "No attendance found.", "file": "../lib/screens/attendance/attendance_details.dart", "line_number": 124, "category": "text_widgets", "suggested_key": "text_no_attendance_found"}, {"text": "Error: ${snapshot.error}", "file": "../lib/screens/attendance/attendance_details.dart", "line_number": 127, "category": "text_widgets", "suggested_key": "text_error_snapshoterror"}, {"text": "latitude: ${entry.latitude}", "file": "../lib/screens/attendance/attendance_details.dart", "line_number": 183, "category": "text_widgets", "suggested_key": "text_latitude_entrylatitude"}, {"text": "longitude: ${entry.longitude}", "file": "../lib/screens/attendance/attendance_details.dart", "line_number": 186, "category": "text_widgets", "suggested_key": "text_longitude_entrylongitude"}, {"text": "Distance: ${entry.distance} km", "file": "../lib/screens/attendance/attendance_details.dart", "line_number": 193, "category": "text_widgets", "suggested_key": "text_distance_entrydistance_km"}, {"text": "Updated By: ${entry.updatedBy}", "file": "../lib/screens/attendance/attendance_details.dart", "line_number": 196, "category": "text_widgets", "suggested_key": "text_updated_by_entryupdatedby"}, {"text": "No data available.", "file": "../lib/screens/attendance/attendance_details.dart", "line_number": 227, "category": "text_widgets", "suggested_key": "text_no_data_available"}], "app_bar_titles": [], "form_labels": [], "button_labels": [{"text": "No attendance found.", "file": "../lib/screens/attendance/attendance_details.dart", "line_number": 124, "category": "button_labels", "suggested_key": "btn_no_attendance_found"}, {"text": "Error: ${snapshot.error}", "file": "../lib/screens/attendance/attendance_details.dart", "line_number": 127, "category": "button_labels", "suggested_key": "btn_error_snapshoterror"}, {"text": "No data available.", "file": "../lib/screens/attendance/attendance_details.dart", "line_number": 227, "category": "button_labels", "suggested_key": "btn_no_data_available"}], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/pdf_screen/pdf_screen.dart": {"text_widgets": [{"text": "Show More", "file": "../lib/screens/pdf_screen/pdf_screen.dart", "line_number": 905, "category": "text_widgets", "suggested_key": "text_show_more"}, {"text": "Show Less", "file": "../lib/screens/pdf_screen/pdf_screen.dart", "line_number": 918, "category": "text_widgets", "suggested_key": "text_show_less"}, {"text": "Show More", "file": "../lib/screens/pdf_screen/pdf_screen.dart", "line_number": 1068, "category": "text_widgets", "suggested_key": "text_show_more"}, {"text": "Show Less", "file": "../lib/screens/pdf_screen/pdf_screen.dart", "line_number": 1081, "category": "text_widgets", "suggested_key": "text_show_less"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/pdf_screen/widgets/date_select.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Select Date (DD-MMM-YYYY)", "file": "../lib/screens/pdf_screen/widgets/date_select.dart", "line_number": 213, "category": "form_labels", "suggested_key": "form_select_date_ddmmmyyyy"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/pdf_screen/widgets/pdf_buttons.dart": {"text_widgets": [{"text": "Could not open file: ${result.message}", "file": "../lib/screens/pdf_screen/widgets/pdf_buttons.dart", "line_number": 112, "category": "text_widgets", "suggested_key": "text_could_not_open"}, {"text": "Download Started!", "file": "../lib/screens/pdf_screen/widgets/pdf_buttons.dart", "line_number": 220, "category": "text_widgets", "suggested_key": "text_download_started"}, {"text": "PDF downloaded successfully to ${path}", "file": "../lib/screens/pdf_screen/widgets/pdf_buttons.dart", "line_number": 258, "category": "text_widgets", "suggested_key": "text_pdf_downloaded_successfully"}, {"text": "Download", "file": "../lib/screens/pdf_screen/widgets/pdf_buttons.dart", "line_number": 337, "category": "text_widgets", "suggested_key": "text_download"}, {"text": "Get in Email", "file": "../lib/screens/pdf_screen/widgets/pdf_buttons.dart", "line_number": 352, "category": "text_widgets", "suggested_key": "text_get_in_email"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [{"text": "Download Started!", "file": "../lib/screens/pdf_screen/widgets/pdf_buttons.dart", "line_number": 220, "category": "snackbar_messages", "suggested_key": "snackbar_download_started"}, {"text": "PDF downloaded successfully to ${path}", "file": "../lib/screens/pdf_screen/widgets/pdf_buttons.dart", "line_number": 258, "category": "snackbar_messages", "suggested_key": "snackbar_pdf_downloaded_successfully"}], "dialog_content": []}, "../lib/screens/rail_sathi_qr/rail_sathi_qr_screen.dart": {"text_widgets": [{"text": "Could not launch the link", "file": "../lib/screens/rail_sathi_qr/rail_sathi_qr_screen.dart", "line_number": 17, "category": "text_widgets", "suggested_key": "text_could_not_launch"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [{"text": "Could not launch the link", "file": "../lib/screens/rail_sathi_qr/rail_sathi_qr_screen.dart", "line_number": 17, "category": "snackbar_messages", "suggested_key": "snackbar_could_not_launch"}], "dialog_content": []}, "../lib/screens/enable_disable_user/enable_disable_user.dart": {"text_widgets": [{"text": "Permission Denied", "file": "../lib/screens/enable_disable_user/enable_disable_user.dart", "line_number": 183, "category": "text_widgets", "suggested_key": "text_permission_denied"}, {"text": "Requested Users", "file": "../lib/screens/enable_disable_user/enable_disable_user.dart", "line_number": 364, "category": "text_widgets", "suggested_key": "text_requested_users"}, {"text": "End date cannot be before start date", "file": "../lib/screens/enable_disable_user/enable_disable_user.dart", "line_number": 828, "category": "text_widgets", "suggested_key": "text_end_date_cannot"}, {"text": "Please select both suspension dates", "file": "../lib/screens/enable_disable_user/enable_disable_user.dart", "line_number": 902, "category": "text_widgets", "suggested_key": "text_please_select_both"}, {"text": "End date cannot be before start date", "file": "../lib/screens/enable_disable_user/enable_disable_user.dart", "line_number": 912, "category": "text_widgets", "suggested_key": "text_end_date_cannot"}, {"text": "Update User Details", "file": "../lib/screens/enable_disable_user/enable_disable_user.dart", "line_number": 1280, "category": "text_widgets", "suggested_key": "text_update_user_details"}, {"text": "Request For Update User Details", "file": "../lib/screens/enable_disable_user/enable_disable_user.dart", "line_number": 1281, "category": "text_widgets", "suggested_key": "text_request_for_update"}], "app_bar_titles": [{"text": "Permission Denied", "file": "../lib/screens/enable_disable_user/enable_disable_user.dart", "line_number": 183, "category": "app_bar_titles", "suggested_key": "title_permission_denied"}], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/add_user/add_user_from.dart": {"text_widgets": [{"text": "I don't have an email", "file": "../lib/screens/add_user/add_user_from.dart", "line_number": 432, "category": "text_widgets", "suggested_key": "text_i_dont_have"}, {"text": "Information", "file": "../lib/screens/add_user/add_user_from.dart", "line_number": 785, "category": "text_widgets", "suggested_key": "text_information"}], "app_bar_titles": [], "form_labels": [{"text": "First Name *", "file": "../lib/screens/add_user/add_user_from.dart", "line_number": 358, "category": "form_labels", "suggested_key": "form_first_name"}, {"text": "Enter first name", "file": "../lib/screens/add_user/add_user_from.dart", "line_number": 359, "category": "form_labels", "suggested_key": "form_enter_first_name"}, {"text": "Middle Name (Optional)", "file": "../lib/screens/add_user/add_user_from.dart", "line_number": 373, "category": "form_labels", "suggested_key": "form_middle_name_optional"}, {"text": "Enter middle name", "file": "../lib/screens/add_user/add_user_from.dart", "line_number": 374, "category": "form_labels", "suggested_key": "form_enter_middle_name"}, {"text": "Last Name *", "file": "../lib/screens/add_user/add_user_from.dart", "line_number": 385, "category": "form_labels", "suggested_key": "form_last_name"}, {"text": "Enter last name", "file": "../lib/screens/add_user/add_user_from.dart", "line_number": 386, "category": "form_labels", "suggested_key": "form_enter_last_name"}, {"text": "Phone Number", "file": "../lib/screens/add_user/add_user_from.dart", "line_number": 447, "category": "form_labels", "suggested_key": "form_phone_number"}, {"text": "Secondary Phone Number (Optional)", "file": "../lib/screens/add_user/add_user_from.dart", "line_number": 482, "category": "form_labels", "suggested_key": "form_secondary_phone_number"}, {"text": "WhatsApp Number", "file": "../lib/screens/add_user/add_user_from.dart", "line_number": 566, "category": "form_labels", "suggested_key": "form_whatsapp_number"}, {"text": "Enter 10-digit WhatsApp number", "file": "../lib/screens/add_user/add_user_from.dart", "line_number": 567, "category": "form_labels", "suggested_key": "form_enter_10digit_whatsapp"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/add_user/widget/new_user_email_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Email *", "file": "../lib/screens/add_user/widget/new_user_email_field.dart", "line_number": 45, "category": "form_labels", "suggested_key": "form_email"}, {"text": "Enter your email address", "file": "../lib/screens/add_user/widget/new_user_email_field.dart", "line_number": 46, "category": "form_labels", "suggested_key": "form_enter_your_email"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/form/signup_form.dart": {"text_widgets": [{"text": "I don't have an email", "file": "../lib/screens/user_screen/form/signup_form.dart", "line_number": 436, "category": "text_widgets", "suggested_key": "text_i_dont_have"}, {"text": "Request For Sign Up", "file": "../lib/screens/user_screen/form/signup_form.dart", "line_number": 796, "category": "text_widgets", "suggested_key": "text_request_for_sign"}, {"text": "Information", "file": "../lib/screens/user_screen/form/signup_form.dart", "line_number": 806, "category": "text_widgets", "suggested_key": "text_information"}], "app_bar_titles": [], "form_labels": [{"text": "First Name *", "file": "../lib/screens/user_screen/form/signup_form.dart", "line_number": 354, "category": "form_labels", "suggested_key": "form_first_name"}, {"text": "Enter your first name", "file": "../lib/screens/user_screen/form/signup_form.dart", "line_number": 355, "category": "form_labels", "suggested_key": "form_enter_your_first"}, {"text": "Middle Name (Optional)", "file": "../lib/screens/user_screen/form/signup_form.dart", "line_number": 369, "category": "form_labels", "suggested_key": "form_middle_name_optional"}, {"text": "Enter your middle name", "file": "../lib/screens/user_screen/form/signup_form.dart", "line_number": 370, "category": "form_labels", "suggested_key": "form_enter_your_middle"}, {"text": "Last Name *", "file": "../lib/screens/user_screen/form/signup_form.dart", "line_number": 381, "category": "form_labels", "suggested_key": "form_last_name"}, {"text": "Enter your last name", "file": "../lib/screens/user_screen/form/signup_form.dart", "line_number": 382, "category": "form_labels", "suggested_key": "form_enter_your_last"}, {"text": "Secondary Phone Number (Optional)", "file": "../lib/screens/user_screen/form/signup_form.dart", "line_number": 486, "category": "form_labels", "suggested_key": "form_secondary_phone_number"}, {"text": "Enter 10-digit secondary phone number", "file": "../lib/screens/user_screen/form/signup_form.dart", "line_number": 487, "category": "form_labels", "suggested_key": "form_enter_10digit_secondary"}, {"text": "WhatsApp Number", "file": "../lib/screens/user_screen/form/signup_form.dart", "line_number": 594, "category": "form_labels", "suggested_key": "form_whatsapp_number"}, {"text": "Enter 10-digit WhatsApp number", "file": "../lib/screens/user_screen/form/signup_form.dart", "line_number": 595, "category": "form_labels", "suggested_key": "form_enter_10digit_whatsapp"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/form/forgot_password_form.dart": {"text_widgets": [{"text": "Close", "file": "../lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 61, "category": "text_widgets", "suggested_key": "text_close"}], "app_bar_titles": [], "form_labels": [{"text": "Email", "file": "../lib/screens/user_screen/form/forgot_password_form.dart", "line_number": 98, "category": "form_labels", "suggested_key": "form_email"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/form/login_form.dart": {"text_widgets": [{"text": "Forgot Password", "file": "../lib/screens/user_screen/form/login_form.dart", "line_number": 108, "category": "text_widgets", "suggested_key": "text_forgot_password"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/form/otp_form.dart": {"text_widgets": [{"text": "Error", "file": "../lib/screens/user_screen/form/otp_form.dart", "line_number": 189, "category": "text_widgets", "suggested_key": "text_error"}, {"text": "Close", "file": "../lib/screens/user_screen/form/otp_form.dart", "line_number": 193, "category": "text_widgets", "suggested_key": "text_close"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/login_mobile_screen.dart": {"text_widgets": [{"text": "Error", "file": "../lib/screens/user_screen/login_mobile_screen.dart", "line_number": 83, "category": "text_widgets", "suggested_key": "text_error"}, {"text": "Close", "file": "../lib/screens/user_screen/login_mobile_screen.dart", "line_number": 87, "category": "text_widgets", "suggested_key": "text_close"}], "app_bar_titles": [], "form_labels": [{"text": "Mobile Number", "file": "../lib/screens/user_screen/login_mobile_screen.dart", "line_number": 184, "category": "form_labels", "suggested_key": "form_mobile_number"}, {"text": "Enter your 10-digit mobile number only", "file": "../lib/screens/user_screen/login_mobile_screen.dart", "line_number": 185, "category": "form_labels", "suggested_key": "form_enter_your_10digit"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/otp/send_otp_button.dart": {"text_widgets": [{"text": "Send OTP", "file": "../lib/screens/user_screen/widgets/otp/send_otp_button.dart", "line_number": 42, "category": "text_widgets", "suggested_key": "text_send_otp"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/otp/otp_boxes.dart": {"text_widgets": [{"text": "Re-enter OTP", "file": "../lib/screens/user_screen/widgets/otp/otp_boxes.dart", "line_number": 86, "category": "text_widgets", "suggested_key": "text_reenter_otp"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/otp/verify_button.dart": {"text_widgets": [{"text": "Verify OTP", "file": "../lib/screens/user_screen/widgets/otp/verify_button.dart", "line_number": 41, "category": "text_widgets", "suggested_key": "text_verify_otp"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/mobile_login/mobile_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Mobile Number", "file": "../lib/screens/user_screen/widgets/mobile_login/mobile_field.dart", "line_number": 21, "category": "form_labels", "suggested_key": "form_mobile_number"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/login_page/google_login_button.dart": {"text_widgets": [{"text": "Sign in with Google", "file": "../lib/screens/user_screen/widgets/login_page/google_login_button.dart", "line_number": 76, "category": "text_widgets", "suggested_key": "text_sign_in_with"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/login_page/password_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Password *", "file": "../lib/screens/user_screen/widgets/login_page/password_field.dart", "line_number": 25, "category": "form_labels", "suggested_key": "form_password"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/login_page/mobile_number_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Mobile Number", "file": "../lib/screens/user_screen/widgets/login_page/mobile_number_field.dart", "line_number": 17, "category": "form_labels", "suggested_key": "form_mobile_number"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/login_page/signup_button.dart": {"text_widgets": [{"text": "New User? Sign Up Here", "file": "../lib/screens/user_screen/widgets/login_page/signup_button.dart", "line_number": 21, "category": "text_widgets", "suggested_key": "text_new_user_sign"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Select Train Numbers", "file": "../lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart", "line_number": 117, "category": "form_labels", "suggested_key": "form_select_train_numbers"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/signup_page/first_name_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "First Name *", "file": "../lib/screens/user_screen/widgets/signup_page/first_name_field.dart", "line_number": 42, "category": "form_labels", "suggested_key": "form_first_name"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Zone", "file": "../lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 116, "category": "form_labels", "suggested_key": "form_zone"}, {"text": "Zone *", "file": "../lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart", "line_number": 117, "category": "form_labels", "suggested_key": "form_zone"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/signup_page/emp_number_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Employee Id *", "file": "../lib/screens/user_screen/widgets/signup_page/emp_number_field.dart", "line_number": 53, "category": "form_labels", "suggested_key": "form_employee_id"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Depot *", "file": "../lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart", "line_number": 111, "category": "form_labels", "suggested_key": "form_depot"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/signup_page/last_name_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Last Name *", "file": "../lib/screens/user_screen/widgets/signup_page/last_name_field.dart", "line_number": 42, "category": "form_labels", "suggested_key": "form_last_name"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/signup_page/re_password.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Re-enter Password *", "file": "../lib/screens/user_screen/widgets/signup_page/re_password.dart", "line_number": 25, "category": "form_labels", "suggested_key": "form_reenter_password"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/signup_page/email_field.dart": {"text_widgets": [{"text": "Send OTP", "file": "../lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 149, "category": "text_widgets", "suggested_key": "text_send_otp"}, {"text": "Resend OTP", "file": "../lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 170, "category": "text_widgets", "suggested_key": "text_resend_otp"}], "app_bar_titles": [], "form_labels": [{"text": "Email", "file": "../lib/screens/user_screen/widgets/signup_page/email_field.dart", "line_number": 114, "category": "form_labels", "suggested_key": "form_email"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/signup_page/division_dropdown.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Divisions", "file": "../lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 110, "category": "form_labels", "suggested_key": "form_divisions"}, {"text": "Divisions *", "file": "../lib/screens/user_screen/widgets/signup_page/division_dropdown.dart", "line_number": 111, "category": "form_labels", "suggested_key": "form_divisions"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Select Coaches", "file": "../lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart", "line_number": 83, "category": "form_labels", "suggested_key": "form_select_coaches"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart": {"text_widgets": [{"text": "Send OTP", "file": "../lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 160, "category": "text_widgets", "suggested_key": "text_send_otp"}, {"text": "Resend OTP", "file": "../lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 184, "category": "text_widgets", "suggested_key": "text_resend_otp"}], "app_bar_titles": [], "form_labels": [{"text": "Enter your 10-digit mobile number only", "file": "../lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart", "line_number": 119, "category": "form_labels", "suggested_key": "form_enter_your_10digit"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/user_screen/widgets/signup_page/middle_name_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Middle Name", "file": "../lib/screens/user_screen/widgets/signup_page/middle_name_field.dart", "line_number": 42, "category": "form_labels", "suggested_key": "form_middle_name"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/pnr_screen/pnr_status.dart": {"text_widgets": [{"text": "Please enter a valid 10-digit PNR number", "file": "../lib/screens/pnr_screen/pnr_status.dart", "line_number": 26, "category": "text_widgets", "suggested_key": "text_please_enter_a"}, {"text": "Failed to fetch PNR data. Please try again.", "file": "../lib/screens/pnr_screen/pnr_status.dart", "line_number": 38, "category": "text_widgets", "suggested_key": "text_failed_to_fetch"}, {"text": "Check PNR Status", "file": "../lib/screens/pnr_screen/pnr_status.dart", "line_number": 65, "category": "text_widgets", "suggested_key": "text_check_pnr_status"}, {"text": "PNR Number", "file": "../lib/screens/pnr_screen/pnr_status.dart", "line_number": 76, "category": "text_widgets", "suggested_key": "text_pnr_number"}, {"text": "No PNR Data Found", "file": "../lib/screens/pnr_screen/pnr_status.dart", "line_number": 92, "category": "text_widgets", "suggested_key": "text_no_pnr_data"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/map_screen/map_screen.dart": {"text_widgets": [{"text": "Please turn on location services", "file": "../lib/screens/map_screen/map_screen.dart", "line_number": 311, "category": "text_widgets", "suggested_key": "text_please_turn_on"}, {"text": "An error occurred: $e", "file": "../lib/screens/map_screen/map_screen.dart", "line_number": 324, "category": "text_widgets", "suggested_key": "text_an_error_occurred"}, {"text": "Please turn on location services", "file": "../lib/screens/map_screen/map_screen.dart", "line_number": 530, "category": "text_widgets", "suggested_key": "text_please_turn_on"}, {"text": "Submit", "file": "../lib/screens/map_screen/map_screen.dart", "line_number": 653, "category": "text_widgets", "suggested_key": "text_submit"}, {"text": "Your current location", "file": "../lib/screens/map_screen/map_screen.dart", "line_number": 698, "category": "text_widgets", "suggested_key": "text_your_current_location"}], "app_bar_titles": [], "form_labels": [{"text": "Select Train Number", "file": "../lib/screens/map_screen/map_screen.dart", "line_number": 584, "category": "form_labels", "suggested_key": "form_select_train_number"}, {"text": "Train Name", "file": "../lib/screens/map_screen/map_screen.dart", "line_number": 611, "category": "form_labels", "suggested_key": "form_train_name"}, {"text": "Select Date (DD-MMM-YYYY)", "file": "../lib/screens/map_screen/map_screen.dart", "line_number": 624, "category": "form_labels", "suggested_key": "form_select_date_ddmmmyyyy"}], "button_labels": [{"text": "Your current location", "file": "../lib/screens/map_screen/map_screen.dart", "line_number": 698, "category": "button_labels", "suggested_key": "btn_your_current_location"}], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/train_details/train_details_screen.dart": {"text_widgets": [{"text": "Location Services Disabled", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 207, "category": "text_widgets", "suggested_key": "text_location_services_disabled"}, {"text": "Please enable location services to proceed.", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 208, "category": "text_widgets", "suggested_key": "text_please_enable_location"}, {"text": "Cancel", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 214, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Enable", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 222, "category": "text_widgets", "suggested_key": "text_enable"}, {"text": "Location Permission Denied", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 242, "category": "text_widgets", "suggested_key": "text_location_permission_denied"}, {"text": "Cancel", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 250, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Open Settings", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 257, "category": "text_widgets", "suggested_key": "text_open_settings"}, {"text": "Location Permission Denied Forever", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 275, "category": "text_widgets", "suggested_key": "text_location_permission_denied"}, {"text": "Cancel", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 283, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Open Settings", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 290, "category": "text_widgets", "suggested_key": "text_open_settings"}, {"text": "Refresh failed: ${e}", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 344, "category": "text_widgets", "suggested_key": "text_refresh_failed_e"}, {"text": "No location data available.", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 426, "category": "text_widgets", "suggested_key": "text_no_location_data"}, {"text": "Error: ${snapshot.error}", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 443, "category": "text_widgets", "suggested_key": "text_error_snapshoterror"}, {"text": "No data available", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 447, "category": "text_widgets", "suggested_key": "text_no_data_available"}], "app_bar_titles": [], "form_labels": [], "button_labels": [{"text": "No location data available.", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 426, "category": "button_labels", "suggested_key": "btn_no_location_data"}, {"text": "Error: ${snapshot.error}", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 443, "category": "button_labels", "suggested_key": "btn_error_snapshoterror"}, {"text": "No data available", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 447, "category": "button_labels", "suggested_key": "btn_no_data_available"}], "snackbar_messages": [{"text": "Refresh failed: ${e}", "file": "../lib/screens/train_details/train_details_screen.dart", "line_number": 344, "category": "snackbar_messages", "suggested_key": "snackbar_refresh_failed_e"}], "dialog_content": []}, "../lib/screens/train_details/widgets/train_popup.dart": {"text_widgets": [{"text": "No train details available.", "file": "../lib/screens/train_details/widgets/train_popup.dart", "line_number": 190, "category": "text_widgets", "suggested_key": "text_no_train_details"}], "app_bar_titles": [], "form_labels": [{"text": "Select Train Number", "file": "../lib/screens/train_details/widgets/train_popup.dart", "line_number": 138, "category": "form_labels", "suggested_key": "form_select_train_number"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/train_details/widgets/station_details_table.dart": {"text_widgets": [{"text": "None", "file": "../lib/screens/train_details/widgets/station_details_table.dart", "line_number": 437, "category": "text_widgets", "suggested_key": "text_none"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/train_details/widgets/train_details_table.dart": {"text_widgets": [{"text": "Could not open file: ${result.message}", "file": "../lib/screens/train_details/widgets/train_details_table.dart", "line_number": 97, "category": "text_widgets", "suggested_key": "text_could_not_open"}, {"text": "Download PDF for all stations", "file": "../lib/screens/train_details/widgets/train_details_table.dart", "line_number": 174, "category": "text_widgets", "suggested_key": "text_download_pdf_for"}, {"text": "Mail PDF for all stations", "file": "../lib/screens/train_details/widgets/train_details_table.dart", "line_number": 195, "category": "text_widgets", "suggested_key": "text_mail_pdf_for"}, {"text": "Download Started!", "file": "../lib/screens/train_details/widgets/train_details_table.dart", "line_number": 293, "category": "text_widgets", "suggested_key": "text_download_started"}, {"text": "PDF downloaded successfully to ${path}", "file": "../lib/screens/train_details/widgets/train_details_table.dart", "line_number": 324, "category": "text_widgets", "suggested_key": "text_pdf_downloaded_successfully"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [{"text": "Download Started!", "file": "../lib/screens/train_details/widgets/train_details_table.dart", "line_number": 293, "category": "snackbar_messages", "suggested_key": "snackbar_download_started"}, {"text": "PDF downloaded successfully to ${path}", "file": "../lib/screens/train_details/widgets/train_details_table.dart", "line_number": 324, "category": "snackbar_messages", "suggested_key": "snackbar_pdf_downloaded_successfully"}], "dialog_content": []}, "../lib/screens/train_details/widgets/train_filter_with_station.dart": {"text_widgets": [{"text": "Submit", "file": "../lib/screens/train_details/widgets/train_filter_with_station.dart", "line_number": 161, "category": "text_widgets", "suggested_key": "text_submit"}], "app_bar_titles": [], "form_labels": [{"text": "Train Number", "file": "../lib/screens/train_details/widgets/train_filter_with_station.dart", "line_number": 188, "category": "form_labels", "suggested_key": "form_train_number"}, {"text": "Train Name", "file": "../lib/screens/train_details/widgets/train_filter_with_station.dart", "line_number": 227, "category": "form_labels", "suggested_key": "form_train_name"}, {"text": "Select Stations", "file": "../lib/screens/train_details/widgets/train_filter_with_station.dart", "line_number": 256, "category": "form_labels", "suggested_key": "form_select_stations"}, {"text": "Select Date", "file": "../lib/screens/train_details/widgets/train_filter_with_station.dart", "line_number": 337, "category": "form_labels", "suggested_key": "form_select_date"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/update_user/widget/update_user_whatsapp_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "WhatsApp Number *", "file": "../lib/screens/update_user/widget/update_user_whatsapp_field.dart", "line_number": 87, "category": "form_labels", "suggested_key": "form_whatsapp_number"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/update_user/widget/update_user_email_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Email *", "file": "../lib/screens/update_user/widget/update_user_email_field.dart", "line_number": 45, "category": "form_labels", "suggested_key": "form_email"}, {"text": "Enter your email address", "file": "../lib/screens/update_user/widget/update_user_email_field.dart", "line_number": 46, "category": "form_labels", "suggested_key": "form_enter_your_email"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/update_user/widget/update_secondary_phone_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Secondary Phone Number (Optional)", "file": "../lib/screens/update_user/widget/update_secondary_phone_field.dart", "line_number": 71, "category": "form_labels", "suggested_key": "form_secondary_phone_number"}, {"text": "Enter secondary phone number (Optional)", "file": "../lib/screens/update_user/widget/update_secondary_phone_field.dart", "line_number": 72, "category": "form_labels", "suggested_key": "form_enter_secondary_phone"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/update_user/widget/update_user_mobile_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Mobile Number *", "file": "../lib/screens/update_user/widget/update_user_mobile_field.dart", "line_number": 85, "category": "form_labels", "suggested_key": "form_mobile_number"}, {"text": "Enter mobile number to fetch details", "file": "../lib/screens/update_user/widget/update_user_mobile_field.dart", "line_number": 86, "category": "form_labels", "suggested_key": "form_enter_mobile_number"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/update_user/update_user_screen.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Enter Mobile Number", "file": "../lib/screens/update_user/update_user_screen.dart", "line_number": 141, "category": "form_labels", "suggested_key": "form_enter_mobile_number"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/edit_train/form/edit_train_form.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Related Train", "file": "../lib/screens/edit_train/form/edit_train_form.dart", "line_number": 144, "category": "form_labels", "suggested_key": "form_related_train"}, {"text": "Zone", "file": "../lib/screens/edit_train/form/edit_train_form.dart", "line_number": 159, "category": "form_labels", "suggested_key": "form_zone"}, {"text": "Division", "file": "../lib/screens/edit_train/form/edit_train_form.dart", "line_number": 173, "category": "form_labels", "suggested_key": "form_division"}, {"text": "Depot", "file": "../lib/screens/edit_train/form/edit_train_form.dart", "line_number": 187, "category": "form_labels", "suggested_key": "form_depot"}, {"text": "Charting Day", "file": "../lib/screens/edit_train/form/edit_train_form.dart", "line_number": 245, "category": "form_labels", "suggested_key": "form_charting_day"}, {"text": "From Station", "file": "../lib/screens/edit_train/form/edit_train_form.dart", "line_number": 259, "category": "form_labels", "suggested_key": "form_from_station"}, {"text": "To Station", "file": "../lib/screens/edit_train/form/edit_train_form.dart", "line_number": 277, "category": "form_labels", "suggested_key": "form_to_station"}, {"text": "Direction (Up/Down)", "file": "../lib/screens/edit_train/form/edit_train_form.dart", "line_number": 297, "category": "form_labels", "suggested_key": "form_direction_updown"}, {"text": "Start Time", "file": "../lib/screens/edit_train/form/edit_train_form.dart", "line_number": 310, "category": "form_labels", "suggested_key": "form_start_time"}, {"text": "e.g. 09:00 AM", "file": "../lib/screens/edit_train/form/edit_train_form.dart", "line_number": 311, "category": "form_labels", "suggested_key": "form_eg_0900_am"}, {"text": "End Time", "file": "../lib/screens/edit_train/form/edit_train_form.dart", "line_number": 326, "category": "form_labels", "suggested_key": "form_end_time"}, {"text": "e.g. 05:00 PM", "file": "../lib/screens/edit_train/form/edit_train_form.dart", "line_number": 327, "category": "form_labels", "suggested_key": "form_eg_0500_pm"}, {"text": "Charting Time", "file": "../lib/screens/edit_train/form/edit_train_form.dart", "line_number": 339, "category": "form_labels", "suggested_key": "form_charting_time"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/edit_train/add_train_screen.dart": {"text_widgets": [{"text": "Add Configuration", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 385, "category": "text_widgets", "suggested_key": "text_add_configuration"}, {"text": "Select Charting Day", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 728, "category": "text_widgets", "suggested_key": "text_select_charting_day"}], "app_bar_titles": [], "form_labels": [{"text": "Train Number", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 407, "category": "form_labels", "suggested_key": "form_train_number"}, {"text": "Return Gap (Days)", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 474, "category": "form_labels", "suggested_key": "form_return_gap_days"}, {"text": "In/Out", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 500, "category": "form_labels", "suggested_key": "form_inout"}, {"text": "Train Name", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 595, "category": "form_labels", "suggested_key": "form_train_name"}, {"text": "Related Train Number", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 615, "category": "form_labels", "suggested_key": "form_related_train_number"}, {"text": "Zone", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 632, "category": "form_labels", "suggested_key": "form_zone"}, {"text": "Division", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 661, "category": "form_labels", "suggested_key": "form_division"}, {"text": "Depot", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 689, "category": "form_labels", "suggested_key": "form_depot"}, {"text": "Charting Day", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 719, "category": "form_labels", "suggested_key": "form_charting_day"}, {"text": "From Station", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 759, "category": "form_labels", "suggested_key": "form_from_station"}, {"text": "To Station", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 781, "category": "form_labels", "suggested_key": "form_to_station"}, {"text": "Up/Down", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 803, "category": "form_labels", "suggested_key": "form_updown"}, {"text": "Start Time", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 837, "category": "form_labels", "suggested_key": "form_start_time"}, {"text": "End Time", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 864, "category": "form_labels", "suggested_key": "form_end_time"}, {"text": "Charting Time", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 894, "category": "form_labels", "suggested_key": "form_charting_time"}, {"text": "Train Type", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 919, "category": "form_labels", "suggested_key": "form_train_type"}], "button_labels": [{"text": "Add Configuration", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 385, "category": "button_labels", "suggested_key": "btn_add_configuration"}, {"text": "Select Charting Day", "file": "../lib/screens/edit_train/add_train_screen.dart", "line_number": 728, "category": "button_labels", "suggested_key": "btn_select_charting_day"}], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/edit_train/edit_train_screen.dart": {"text_widgets": [{"text": "Submitting...", "file": "../lib/screens/edit_train/edit_train_screen.dart", "line_number": 512, "category": "text_widgets", "suggested_key": "text_submitting"}, {"text": "Return gap updated successfully", "file": "../lib/screens/edit_train/edit_train_screen.dart", "line_number": 543, "category": "text_widgets", "suggested_key": "text_return_gap_updated"}, {"text": "Data refreshed successfully", "file": "../lib/screens/edit_train/edit_train_screen.dart", "line_number": 661, "category": "text_widgets", "suggested_key": "text_data_refreshed_successfully"}, {"text": "Data not refreshed: $e", "file": "../lib/screens/edit_train/edit_train_screen.dart", "line_number": 675, "category": "text_widgets", "suggested_key": "text_data_not_refreshed"}, {"text": "Submitting...", "file": "../lib/screens/edit_train/edit_train_screen.dart", "line_number": 753, "category": "text_widgets", "suggested_key": "text_submitting"}, {"text": "Edit Configuration", "file": "../lib/screens/edit_train/edit_train_screen.dart", "line_number": 912, "category": "text_widgets", "suggested_key": "text_edit_configuration"}], "app_bar_titles": [], "form_labels": [{"text": "Search Train Number", "file": "../lib/screens/edit_train/edit_train_screen.dart", "line_number": 931, "category": "form_labels", "suggested_key": "form_search_train_number"}, {"text": "Select Train Number", "file": "../lib/screens/edit_train/edit_train_screen.dart", "line_number": 943, "category": "form_labels", "suggested_key": "form_select_train_number"}], "button_labels": [{"text": "Edit Configuration", "file": "../lib/screens/edit_train/edit_train_screen.dart", "line_number": 912, "category": "button_labels", "suggested_key": "btn_edit_configuration"}], "snackbar_messages": [{"text": "Return gap updated successfully", "file": "../lib/screens/edit_train/edit_train_screen.dart", "line_number": 543, "category": "snackbar_messages", "suggested_key": "snackbar_return_gap_updated"}, {"text": "Data refreshed successfully", "file": "../lib/screens/edit_train/edit_train_screen.dart", "line_number": 661, "category": "snackbar_messages", "suggested_key": "snackbar_data_refreshed_successfully"}, {"text": "Data not refreshed: $e", "file": "../lib/screens/edit_train/edit_train_screen.dart", "line_number": 675, "category": "snackbar_messages", "suggested_key": "snackbar_data_not_refreshed"}], "dialog_content": []}, "../lib/screens/edit_train/widgets/coaches_input_field.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Coaches (comma separated)", "file": "../lib/screens/edit_train/widgets/coaches_input_field.dart", "line_number": 28, "category": "form_labels", "suggested_key": "form_coaches_comma_separated"}, {"text": "E.g., H, GSL, A, B, M", "file": "../lib/screens/edit_train/widgets/coaches_input_field.dart", "line_number": 29, "category": "form_labels", "suggested_key": "form_eg_h_gsl"}, {"text": "Enter coach names separated by commas", "file": "../lib/screens/edit_train/widgets/coaches_input_field.dart", "line_number": 33, "category": "form_labels", "suggested_key": "form_enter_coach_names"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/edit_train/widgets/frequency_checkboxes.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Select Days", "file": "../lib/screens/edit_train/widgets/frequency_checkboxes.dart", "line_number": 75, "category": "form_labels", "suggested_key": "form_select_days"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/edit_train/widgets/train_type.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Train Type", "file": "../lib/screens/edit_train/widgets/train_type.dart", "line_number": 16, "category": "form_labels", "suggested_key": "form_train_type"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/edit_train/widgets/stoppages_sequence_input.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Add Stoppage", "file": "../lib/screens/edit_train/widgets/stoppages_sequence_input.dart", "line_number": 183, "category": "form_labels", "suggested_key": "form_add_stoppage"}, {"text": "Type a station name and press Enter or Space", "file": "../lib/screens/edit_train/widgets/stoppages_sequence_input.dart", "line_number": 184, "category": "form_labels", "suggested_key": "form_type_a_station"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/edit_train/widgets/custom_drop_down.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Search", "file": "../lib/screens/edit_train/widgets/custom_drop_down.dart", "line_number": 67, "category": "form_labels", "suggested_key": "form_search"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/edit_train/widgets/stoppage_sequence.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Stoppages in Sequence", "file": "../lib/screens/edit_train/widgets/stoppage_sequence.dart", "line_number": 194, "category": "form_labels", "suggested_key": "form_stoppages_in_sequence"}, {"text": "Type a Station and hit space or Enter", "file": "../lib/screens/edit_train/widgets/stoppage_sequence.dart", "line_number": 207, "category": "form_labels", "suggested_key": "form_type_a_station"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/edit_train/widgets/train_name.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Train Name", "file": "../lib/screens/edit_train/widgets/train_name.dart", "line_number": 16, "category": "form_labels", "suggested_key": "form_train_name"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/edit_train/widgets/frequency.dart": {"text_widgets": [], "app_bar_titles": [], "form_labels": [{"text": "Frequency", "file": "../lib/screens/edit_train/widgets/frequency.dart", "line_number": 42, "category": "form_labels", "suggested_key": "form_frequency"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/edit_train/widgets/coach_in_sequence.dart": {"text_widgets": [{"text": "No coaches available", "file": "../lib/screens/edit_train/widgets/coach_in_sequence.dart", "line_number": 262, "category": "text_widgets", "suggested_key": "text_no_coaches_available"}], "app_bar_titles": [], "form_labels": [{"text": "Enter new coach", "file": "../lib/screens/edit_train/widgets/coach_in_sequence.dart", "line_number": 348, "category": "form_labels", "suggested_key": "form_enter_new_coach"}, {"text": "Use comma to add multiple coaches", "file": "../lib/screens/edit_train/widgets/coach_in_sequence.dart", "line_number": 351, "category": "form_labels", "suggested_key": "form_use_comma_to"}], "button_labels": [{"text": "No coaches available", "file": "../lib/screens/edit_train/widgets/coach_in_sequence.dart", "line_number": 262, "category": "button_labels", "suggested_key": "btn_no_coaches_available"}], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/mcc_to_obhs_handover/mcc_to_obhs_handover_screen.dart": {"text_widgets": [{"text": "Invalid response format from server", "file": "../lib/screens/mcc_to_obhs_handover/mcc_to_obhs_handover_screen.dart", "line_number": 92, "category": "text_widgets", "suggested_key": "text_invalid_response_format"}, {"text": "Data refreshed successfully", "file": "../lib/screens/mcc_to_obhs_handover/mcc_to_obhs_handover_screen.dart", "line_number": 115, "category": "text_widgets", "suggested_key": "text_data_refreshed_successfully"}, {"text": "Please select a train and date first", "file": "../lib/screens/mcc_to_obhs_handover/mcc_to_obhs_handover_screen.dart", "line_number": 197, "category": "text_widgets", "suggested_key": "text_please_select_a"}, {"text": "Coach Handover Report", "file": "../lib/screens/mcc_to_obhs_handover/mcc_to_obhs_handover_screen.dart", "line_number": 234, "category": "text_widgets", "suggested_key": "text_coach_handover_report"}], "app_bar_titles": [], "form_labels": [], "button_labels": [{"text": "Coach Handover Report", "file": "../lib/screens/mcc_to_obhs_handover/mcc_to_obhs_handover_screen.dart", "line_number": 234, "category": "button_labels", "suggested_key": "btn_coach_handover_report"}], "snackbar_messages": [{"text": "Invalid response format from server", "file": "../lib/screens/mcc_to_obhs_handover/mcc_to_obhs_handover_screen.dart", "line_number": 92, "category": "snackbar_messages", "suggested_key": "snackbar_invalid_response_format"}, {"text": "Data refreshed successfully", "file": "../lib/screens/mcc_to_obhs_handover/mcc_to_obhs_handover_screen.dart", "line_number": 115, "category": "snackbar_messages", "suggested_key": "snackbar_data_refreshed_successfully"}, {"text": "Please select a train and date first", "file": "../lib/screens/mcc_to_obhs_handover/mcc_to_obhs_handover_screen.dart", "line_number": 197, "category": "snackbar_messages", "suggested_key": "snackbar_please_select_a"}], "dialog_content": []}, "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart": {"text_widgets": [{"text": "Save Selection", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 276, "category": "text_widgets", "suggested_key": "text_save_selection"}, {"text": "Camera", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 332, "category": "text_widgets", "suggested_key": "text_camera"}, {"text": "Gallery", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 337, "category": "text_widgets", "suggested_key": "text_gallery"}, {"text": "Select Media Type", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 356, "category": "text_widgets", "suggested_key": "text_select_media_type"}, {"text": "Image", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 363, "category": "text_widgets", "suggested_key": "text_image"}, {"text": "Video", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 368, "category": "text_widgets", "suggested_key": "text_video"}, {"text": "Please select images to upload", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 432, "category": "text_widgets", "suggested_key": "text_please_select_images"}, {"text": "Please select at least one issue", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 439, "category": "text_widgets", "suggested_key": "text_please_select_at"}, {"text": "Failed to upload images", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 471, "category": "text_widgets", "suggested_key": "text_failed_to_upload"}, {"text": "Error updating issue: $e", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 533, "category": "text_widgets", "suggested_key": "text_error_updating_issue"}, {"text": "$statusType By", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 636, "category": "text_widgets", "suggested_key": "text_statustype_by"}, {"text": "No images available", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 680, "category": "text_widgets", "suggested_key": "text_no_images_available"}, {"text": "Error fetching images: $e", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 692, "category": "text_widgets", "suggested_key": "text_error_fetching_images"}, {"text": "Issue/Subissue", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 783, "category": "text_widgets", "suggested_key": "text_issuesubissue"}, {"text": "Status", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 788, "category": "text_widgets", "suggested_key": "text_status"}, {"text": "Submit/Update", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 793, "category": "text_widgets", "suggested_key": "text_submitupdate"}, {"text": "Reported_by", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 798, "category": "text_widgets", "suggested_key": "text_reportedby"}, {"text": "Fixed_by", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 803, "category": "text_widgets", "suggested_key": "text_fixedby"}, {"text": "Resolved_by", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 808, "category": "text_widgets", "suggested_key": "text_resolvedby"}, {"text": "Pick Images", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 933, "category": "text_widgets", "suggested_key": "text_pick_images"}, {"text": "Uploading...", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 1004, "category": "text_widgets", "suggested_key": "text_uploading"}, {"text": "Submit & Upload", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 1006, "category": "text_widgets", "suggested_key": "text_submit_upload"}], "app_bar_titles": [], "form_labels": [{"text": "Add your comments here (max 250 characters)...", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 960, "category": "form_labels", "suggested_key": "form_add_your_comments"}], "button_labels": [{"text": "$statusType By", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 636, "category": "button_labels", "suggested_key": "btn_statustype_by"}], "snackbar_messages": [{"text": "Please select images to upload", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 432, "category": "snackbar_messages", "suggested_key": "snackbar_please_select_images"}, {"text": "Please select at least one issue", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 439, "category": "snackbar_messages", "suggested_key": "snackbar_please_select_at"}, {"text": "Failed to upload images", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 471, "category": "snackbar_messages", "suggested_key": "snackbar_failed_to_upload"}, {"text": "Error updating issue: $e", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 533, "category": "snackbar_messages", "suggested_key": "snackbar_error_updating_issue"}, {"text": "No images available", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 680, "category": "snackbar_messages", "suggested_key": "snackbar_no_images_available"}, {"text": "Error fetching images: $e", "file": "../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart", "line_number": 692, "category": "snackbar_messages", "suggested_key": "snackbar_error_fetching_images"}], "dialog_content": []}, "../lib/screens/request_user_management/requested_user_screen.dart": {"text_widgets": [{"text": "Error", "file": "../lib/screens/request_user_management/requested_user_screen.dart", "line_number": 86, "category": "text_widgets", "suggested_key": "text_error"}, {"text": "Retry", "file": "../lib/screens/request_user_management/requested_user_screen.dart", "line_number": 90, "category": "text_widgets", "suggested_key": "text_retry"}, {"text": "Close", "file": "../lib/screens/request_user_management/requested_user_screen.dart", "line_number": 97, "category": "text_widgets", "suggested_key": "text_close"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/request_user_management/widgets/user_request_list_tile.dart": {"text_widgets": [{"text": "<PERSON><PERSON>", "file": "../lib/screens/request_user_management/widgets/user_request_list_tile.dart", "line_number": 105, "category": "text_widgets", "suggested_key": "text_deny"}, {"text": "Approve", "file": "../lib/screens/request_user_management/widgets/user_request_list_tile.dart", "line_number": 121, "category": "text_widgets", "suggested_key": "text_approve"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/request_user_management/widgets/new_user_request_list_title.dart": {"text_widgets": [{"text": "<PERSON><PERSON>", "file": "../lib/screens/request_user_management/widgets/new_user_request_list_title.dart", "line_number": 200, "category": "text_widgets", "suggested_key": "text_deny"}, {"text": "Approve", "file": "../lib/screens/request_user_management/widgets/new_user_request_list_title.dart", "line_number": 214, "category": "text_widgets", "suggested_key": "text_approve"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/request_user_management/widgets/new_user_request_details_screen.dart": {"text_widgets": [{"text": "No requests selected", "file": "../lib/screens/request_user_management/widgets/new_user_request_details_screen.dart", "line_number": 96, "category": "text_widgets", "suggested_key": "text_no_requests_selected"}, {"text": "Are you sure you want to approve these users:", "file": "../lib/screens/request_user_management/widgets/new_user_request_details_screen.dart", "line_number": 126, "category": "text_widgets", "suggested_key": "text_are_you_sure"}, {"text": "Cancel", "file": "../lib/screens/request_user_management/widgets/new_user_request_details_screen.dart", "line_number": 174, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Approve", "file": "../lib/screens/request_user_management/widgets/new_user_request_details_screen.dart", "line_number": 178, "category": "text_widgets", "suggested_key": "text_approve"}, {"text": "${requestsToProcess.length} users approved successfully", "file": "../lib/screens/request_user_management/widgets/new_user_request_details_screen.dart", "line_number": 222, "category": "text_widgets", "suggested_key": "text_requeststoprocesslength_users_approved"}, {"text": "Confirm Denial", "file": "../lib/screens/request_user_management/widgets/new_user_request_details_screen.dart", "line_number": 253, "category": "text_widgets", "suggested_key": "text_confirm_denial"}, {"text": "Cancel", "file": "../lib/screens/request_user_management/widgets/new_user_request_details_screen.dart", "line_number": 259, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "<PERSON>y <PERSON>", "file": "../lib/screens/request_user_management/widgets/new_user_request_details_screen.dart", "line_number": 269, "category": "text_widgets", "suggested_key": "text_deny_request"}, {"text": "Approve Selected", "file": "../lib/screens/request_user_management/widgets/new_user_request_details_screen.dart", "line_number": 467, "category": "text_widgets", "suggested_key": "text_approve_selected"}, {"text": "Approve All", "file": "../lib/screens/request_user_management/widgets/new_user_request_details_screen.dart", "line_number": 488, "category": "text_widgets", "suggested_key": "text_approve_all"}, {"text": "Processing requests...", "file": "../lib/screens/request_user_management/widgets/new_user_request_details_screen.dart", "line_number": 544, "category": "text_widgets", "suggested_key": "text_processing_requests"}, {"text": "Clear search", "file": "../lib/screens/request_user_management/widgets/new_user_request_details_screen.dart", "line_number": 568, "category": "text_widgets", "suggested_key": "text_clear_search"}], "app_bar_titles": [], "form_labels": [{"text": "Search by name, email, phone or depot", "file": "../lib/screens/request_user_management/widgets/new_user_request_details_screen.dart", "line_number": 355, "category": "form_labels", "suggested_key": "form_search_by_name"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/request_user_management/widgets/update_request_list_tile.dart": {"text_widgets": [{"text": "<PERSON><PERSON>", "file": "../lib/screens/request_user_management/widgets/update_request_list_tile.dart", "line_number": 239, "category": "text_widgets", "suggested_key": "text_deny"}, {"text": "Approve", "file": "../lib/screens/request_user_management/widgets/update_request_list_tile.dart", "line_number": 253, "category": "text_widgets", "suggested_key": "text_approve"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/request_user_management/widgets/update_request_details_screen.dart": {"text_widgets": [{"text": "No requests selected", "file": "../lib/screens/request_user_management/widgets/update_request_details_screen.dart", "line_number": 101, "category": "text_widgets", "suggested_key": "text_no_requests_selected"}, {"text": "Cancel", "file": "../lib/screens/request_user_management/widgets/update_request_details_screen.dart", "line_number": 180, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Approve", "file": "../lib/screens/request_user_management/widgets/update_request_details_screen.dart", "line_number": 184, "category": "text_widgets", "suggested_key": "text_approve"}, {"text": "Confirm Denial", "file": "../lib/screens/request_user_management/widgets/update_request_details_screen.dart", "line_number": 258, "category": "text_widgets", "suggested_key": "text_confirm_denial"}, {"text": "Cancel", "file": "../lib/screens/request_user_management/widgets/update_request_details_screen.dart", "line_number": 264, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "<PERSON>y <PERSON>", "file": "../lib/screens/request_user_management/widgets/update_request_details_screen.dart", "line_number": 274, "category": "text_widgets", "suggested_key": "text_deny_request"}, {"text": "Approve Selected", "file": "../lib/screens/request_user_management/widgets/update_request_details_screen.dart", "line_number": 473, "category": "text_widgets", "suggested_key": "text_approve_selected"}, {"text": "Approve All", "file": "../lib/screens/request_user_management/widgets/update_request_details_screen.dart", "line_number": 494, "category": "text_widgets", "suggested_key": "text_approve_all"}, {"text": "Processing requests...", "file": "../lib/screens/request_user_management/widgets/update_request_details_screen.dart", "line_number": 550, "category": "text_widgets", "suggested_key": "text_processing_requests"}, {"text": "Clear search", "file": "../lib/screens/request_user_management/widgets/update_request_details_screen.dart", "line_number": 574, "category": "text_widgets", "suggested_key": "text_clear_search"}], "app_bar_titles": [], "form_labels": [{"text": "Search by name, email, phone or depot", "file": "../lib/screens/request_user_management/widgets/update_request_details_screen.dart", "line_number": 361, "category": "form_labels", "suggested_key": "form_search_by_name"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/rail_sathi/view_complaints.dart": {"text_widgets": [{"text": "Failed to fetch complaints", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 51, "category": "text_widgets", "suggested_key": "text_failed_to_fetch"}, {"text": "Error fetching trains: $e", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 98, "category": "text_widgets", "suggested_key": "text_error_fetching_trains"}, {"text": "${train.trainNo} - ${train.trainName}", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 268, "category": "text_widgets", "suggested_key": "text_traintrainno_traintrainname"}, {"text": "Other", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 273, "category": "text_widgets", "suggested_key": "text_other"}, {"text": "${train.trainNo} - ${train.trainName}", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 331, "category": "text_widgets", "suggested_key": "text_traintrainno_traintrainname"}, {"text": "Other", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 336, "category": "text_widgets", "suggested_key": "text_other"}, {"text": "Existing Images:", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 528, "category": "text_widgets", "suggested_key": "text_existing_images"}, {"text": "Delete Image", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 559, "category": "text_widgets", "suggested_key": "text_delete_image"}, {"text": "Cancel", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 566, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Delete", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 571, "category": "text_widgets", "suggested_key": "text_delete"}, {"text": "Image deleted", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 593, "category": "text_widgets", "suggested_key": "text_image_deleted"}, {"text": "Newly Selected Images:", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 627, "category": "text_widgets", "suggested_key": "text_newly_selected_images"}, {"text": "Add Image", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 692, "category": "text_widgets", "suggested_key": "text_add_image"}, {"text": "<PERSON><PERSON><PERSON><PERSON> updated", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 752, "category": "text_widgets", "suggested_key": "text_complaint_updated"}, {"text": "Update failed", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 757, "category": "text_widgets", "suggested_key": "text_update_failed"}, {"text": "Save Changes", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 768, "category": "text_widgets", "suggested_key": "text_save_changes"}, {"text": "Delete Co<PERSON>t", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 787, "category": "text_widgets", "suggested_key": "text_delete_complaint"}, {"text": "Are you sure you want to delete this complaint?", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 788, "category": "text_widgets", "suggested_key": "text_are_you_sure"}, {"text": "Cancel", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 792, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Delete", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 795, "category": "text_widgets", "suggested_key": "text_delete"}, {"text": "<PERSON><PERSON><PERSON><PERSON> deleted successfully", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 808, "category": "text_widgets", "suggested_key": "text_complaint_deleted_successfully"}, {"text": "Failed to delete complaint", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 813, "category": "text_widgets", "suggested_key": "text_failed_to_delete"}, {"text": "Select Date", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 841, "category": "text_widgets", "suggested_key": "text_select_date"}, {"text": "No complaints found", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 849, "category": "text_widgets", "suggested_key": "text_no_complaints_found"}, {"text": "Train No: ${complaint.trainNumber}", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 867, "category": "text_widgets", "suggested_key": "text_train_no_complainttrainnumber"}, {"text": "Date: ${complaint.complainDate}", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 870, "category": "text_widgets", "suggested_key": "text_date_complaintcomplaindate"}, {"text": "PNR: ${complaint.pnrNumber}", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 873, "category": "text_widgets", "suggested_key": "text_pnr_complaintpnrnumber"}, {"text": "Edit", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 923, "category": "text_widgets", "suggested_key": "text_edit"}, {"text": "Delete", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 930, "category": "text_widgets", "suggested_key": "text_delete"}], "app_bar_titles": [], "form_labels": [{"text": "Train", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 293, "category": "form_labels", "suggested_key": "form_train"}, {"text": "Train", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 356, "category": "form_labels", "suggested_key": "form_train"}, {"text": "Complaint Type", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 438, "category": "form_labels", "suggested_key": "form_complaint_type"}, {"text": "Status", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 460, "category": "form_labels", "suggested_key": "form_status"}, {"text": "Complaint Type", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 486, "category": "form_labels", "suggested_key": "form_complaint_type"}, {"text": "Status", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 509, "category": "form_labels", "suggested_key": "form_status"}], "button_labels": [{"text": "${train.trainNo} - ${train.trainName}", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 268, "category": "button_labels", "suggested_key": "btn_traintrainno_traintrainname"}, {"text": "Other", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 273, "category": "button_labels", "suggested_key": "btn_other"}, {"text": "${train.trainNo} - ${train.trainName}", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 331, "category": "button_labels", "suggested_key": "btn_traintrainno_traintrainname"}, {"text": "Other", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 336, "category": "button_labels", "suggested_key": "btn_other"}, {"text": "No complaints found", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 849, "category": "button_labels", "suggested_key": "btn_no_complaints_found"}], "snackbar_messages": [{"text": "Failed to fetch complaints", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 51, "category": "snackbar_messages", "suggested_key": "snackbar_failed_to_fetch"}, {"text": "Error fetching trains: $e", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 98, "category": "snackbar_messages", "suggested_key": "snackbar_error_fetching_trains"}, {"text": "<PERSON><PERSON><PERSON><PERSON> updated", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 752, "category": "snackbar_messages", "suggested_key": "snackbar_complaint_updated"}, {"text": "Update failed", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 757, "category": "snackbar_messages", "suggested_key": "snackbar_update_failed"}, {"text": "<PERSON><PERSON><PERSON><PERSON> deleted successfully", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 808, "category": "snackbar_messages", "suggested_key": "snackbar_complaint_deleted_successfully"}, {"text": "Failed to delete complaint", "file": "../lib/screens/rail_sathi/view_complaints.dart", "line_number": 813, "category": "snackbar_messages", "suggested_key": "snackbar_failed_to_delete"}], "dialog_content": []}, "../lib/screens/rail_sathi/write_complaint.dart": {"text_widgets": [{"text": "Success", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 252, "category": "text_widgets", "suggested_key": "text_success"}, {"text": "<PERSON><PERSON><PERSON><PERSON> submitted successfully!", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 253, "category": "text_widgets", "suggested_key": "text_complaint_submitted_successfully"}, {"text": "Failed to submit complaint", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 264, "category": "text_widgets", "suggested_key": "text_failed_to_submit"}, {"text": "Error: $e", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 270, "category": "text_widgets", "suggested_key": "text_error_e"}, {"text": "Camera", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 317, "category": "text_widgets", "suggested_key": "text_camera"}, {"text": "Gallery", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 325, "category": "text_widgets", "suggested_key": "text_gallery"}, {"text": "EHK: $ehkDisplay", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 350, "category": "text_widgets", "suggested_key": "text_ehk_ehkdisplay"}, {"text": "Pending", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 378, "category": "text_widgets", "suggested_key": "text_pending"}, {"text": "Completed", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 379, "category": "text_widgets", "suggested_key": "text_completed"}, {"text": "Upload Image/Video", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 393, "category": "text_widgets", "suggested_key": "text_upload_imagevideo"}, {"text": "Submit Issue", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 404, "category": "text_widgets", "suggested_key": "text_submit_issue"}, {"text": "Validate", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 550, "category": "text_widgets", "suggested_key": "text_validate"}, {"text": "Next", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 729, "category": "text_widgets", "suggested_key": "text_next"}], "app_bar_titles": [], "form_labels": [{"text": "Write your issue", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 364, "category": "form_labels", "suggested_key": "form_write_your_issue"}, {"text": "Issue Status", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 374, "category": "form_labels", "suggested_key": "form_issue_status"}, {"text": "Name", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 558, "category": "form_labels", "suggested_key": "form_name"}, {"text": "Phone Number", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 566, "category": "form_labels", "suggested_key": "form_phone_number"}, {"text": "Search by train number or name", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 578, "category": "form_labels", "suggested_key": "form_search_by_train"}, {"text": "Train Selection", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 591, "category": "form_labels", "suggested_key": "form_train_selection"}, {"text": "Train Number", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 625, "category": "form_labels", "suggested_key": "form_train_number"}, {"text": "Train Name", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 634, "category": "form_labels", "suggested_key": "form_train_name"}, {"text": "Journey Start Date", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 670, "category": "form_labels", "suggested_key": "form_journey_start_date"}, {"text": "DD/MM/YYYY", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 671, "category": "form_labels", "suggested_key": "form_ddmmyyyy"}, {"text": "Coach", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 685, "category": "form_labels", "suggested_key": "form_coach"}, {"text": "<PERSON><PERSON>", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 697, "category": "form_labels", "suggested_key": "form_berth"}], "button_labels": [{"text": "Pending", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 378, "category": "button_labels", "suggested_key": "btn_pending"}, {"text": "Completed", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 379, "category": "button_labels", "suggested_key": "btn_completed"}], "snackbar_messages": [{"text": "Failed to submit complaint", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 264, "category": "snackbar_messages", "suggested_key": "snackbar_failed_to_submit"}, {"text": "Error: $e", "file": "../lib/screens/rail_sathi/write_complaint.dart", "line_number": 270, "category": "snackbar_messages", "suggested_key": "snackbar_error_e"}], "dialog_content": []}, "../lib/screens/upload_screen/upload_screen.dart": {"text_widgets": [{"text": "Error loading authentication state", "file": "../lib/screens/upload_screen/upload_screen.dart", "line_number": 39, "category": "text_widgets", "suggested_key": "text_error_loading_authentication"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/upload_screen/widgets/upload_widget.dart": {"text_widgets": [{"text": "Storage Permission Required", "file": "../lib/screens/upload_screen/widgets/upload_widget.dart", "line_number": 64, "category": "text_widgets", "suggested_key": "text_storage_permission_required"}, {"text": "Cancel", "file": "../lib/screens/upload_screen/widgets/upload_widget.dart", "line_number": 70, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Open Settings", "file": "../lib/screens/upload_screen/widgets/upload_widget.dart", "line_number": 77, "category": "text_widgets", "suggested_key": "text_open_settings"}, {"text": "Please select a JSON file to submit.", "file": "../lib/screens/upload_screen/widgets/upload_widget.dart", "line_number": 93, "category": "text_widgets", "suggested_key": "text_please_select_a"}, {"text": "Error: Json file is not in the correct format", "file": "../lib/screens/upload_screen/widgets/upload_widget.dart", "line_number": 141, "category": "text_widgets", "suggested_key": "text_error_json_file"}, {"text": "Selection cleared.", "file": "../lib/screens/upload_screen/widgets/upload_widget.dart", "line_number": 178, "category": "text_widgets", "suggested_key": "text_selection_cleared"}, {"text": "Upload Json Data", "file": "../lib/screens/upload_screen/widgets/upload_widget.dart", "line_number": 217, "category": "text_widgets", "suggested_key": "text_upload_json_data"}], "app_bar_titles": [], "form_labels": [], "button_labels": [{"text": "Upload Json Data", "file": "../lib/screens/upload_screen/widgets/upload_widget.dart", "line_number": 217, "category": "button_labels", "suggested_key": "btn_upload_json_data"}], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/trip_report/widget/IssueScreen.dart": {"text_widgets": [{"text": "Confirm Delete", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 143, "category": "text_widgets", "suggested_key": "text_confirm_delete"}, {"text": "Cancel", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 148, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Delete", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 152, "category": "text_widgets", "suggested_key": "text_delete"}, {"text": "Confirm Delete", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 283, "category": "text_widgets", "suggested_key": "text_confirm_delete"}, {"text": "Cancel", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 288, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Delete", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 292, "category": "text_widgets", "suggested_key": "text_delete"}, {"text": "Cancel", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 466, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Update", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 472, "category": "text_widgets", "suggested_key": "text_update"}, {"text": "Add Issue", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 485, "category": "text_widgets", "suggested_key": "text_add_issue"}, {"text": "No subissues", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 539, "category": "text_widgets", "suggested_key": "text_no_subissues"}, {"text": "Select Issue", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 643, "category": "text_widgets", "suggested_key": "text_select_issue"}, {"text": "Cancel", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 692, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Update", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 701, "category": "text_widgets", "suggested_key": "text_update"}, {"text": "Add Subissue", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 715, "category": "text_widgets", "suggested_key": "text_add_subissue"}, {"text": "Add New Item", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 829, "category": "text_widgets", "suggested_key": "text_add_new_item"}, {"text": "Add Issue", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 835, "category": "text_widgets", "suggested_key": "text_add_issue"}, {"text": "Add Subissue", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 851, "category": "text_widgets", "suggested_key": "text_add_subissue"}, {"text": "Cancel", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 866, "category": "text_widgets", "suggested_key": "text_cancel"}], "app_bar_titles": [{"text": "Confirm Delete", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 143, "category": "app_bar_titles", "suggested_key": "title_confirm_delete"}, {"text": "Confirm Delete", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 283, "category": "app_bar_titles", "suggested_key": "title_confirm_delete"}, {"text": "Add New Item", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 829, "category": "app_bar_titles", "suggested_key": "title_add_new_item"}, {"text": "Add Issue", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 835, "category": "app_bar_titles", "suggested_key": "title_add_issue"}, {"text": "Add Subissue", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 851, "category": "app_bar_titles", "suggested_key": "title_add_subissue"}], "form_labels": [{"text": "Issue Name", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 447, "category": "form_labels", "suggested_key": "form_issue_name"}, {"text": "Subissue Name", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 672, "category": "form_labels", "suggested_key": "form_subissue_name"}], "button_labels": [{"text": "Cancel", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 148, "category": "button_labels", "suggested_key": "btn_cancel"}, {"text": "Delete", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 152, "category": "button_labels", "suggested_key": "btn_delete"}, {"text": "Cancel", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 288, "category": "button_labels", "suggested_key": "btn_cancel"}, {"text": "Delete", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 292, "category": "button_labels", "suggested_key": "btn_delete"}, {"text": "Cancel", "file": "../lib/screens/trip_report/widget/IssueScreen.dart", "line_number": 866, "category": "button_labels", "suggested_key": "btn_cancel"}], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart": {"text_widgets": [{"text": "Camera", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 134, "category": "text_widgets", "suggested_key": "text_camera"}, {"text": "Gallery", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 139, "category": "text_widgets", "suggested_key": "text_gallery"}, {"text": "Select Media Type", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 158, "category": "text_widgets", "suggested_key": "text_select_media_type"}, {"text": "Image", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 165, "category": "text_widgets", "suggested_key": "text_image"}, {"text": "Video", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 170, "category": "text_widgets", "suggested_key": "text_video"}, {"text": "Submitting...", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 220, "category": "text_widgets", "suggested_key": "text_submitting"}, {"text": "Images or Videos upload initiated successfully", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 233, "category": "text_widgets", "suggested_key": "text_images_or_videos"}, {"text": "Please select an image and issue to upload", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 280, "category": "text_widgets", "suggested_key": "text_please_select_an"}, {"text": "No images available", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 317, "category": "text_widgets", "suggested_key": "text_no_images_available"}, {"text": "Error fetching images: $e", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 329, "category": "text_widgets", "suggested_key": "text_error_fetching_images"}, {"text": "Issues saved upload Images/Videos", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 490, "category": "text_widgets", "suggested_key": "text_issues_saved_upload"}, {"text": "Select Issues for Coach $coach", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 512, "category": "text_widgets", "suggested_key": "text_select_issues_for"}, {"text": "Save Selection", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 614, "category": "text_widgets", "suggested_key": "text_save_selection"}, {"text": "Error updating issue: $e", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 651, "category": "text_widgets", "suggested_key": "text_error_updating_issue"}, {"text": "Pick Images/Videos", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 780, "category": "text_widgets", "suggested_key": "text_pick_imagesvideos"}, {"text": "Please select image/video to upload", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 890, "category": "text_widgets", "suggested_key": "text_please_select_imagevideo"}, {"text": "Uploading...", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 904, "category": "text_widgets", "suggested_key": "text_uploading"}, {"text": "Submit & Upload", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 906, "category": "text_widgets", "suggested_key": "text_submit_upload"}, {"text": "Issue/Subissue", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 933, "category": "text_widgets", "suggested_key": "text_issuesubissue"}, {"text": "Status", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 938, "category": "text_widgets", "suggested_key": "text_status"}, {"text": "Submit/Update", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 943, "category": "text_widgets", "suggested_key": "text_submitupdate"}, {"text": "Reported_by", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 948, "category": "text_widgets", "suggested_key": "text_reportedby"}, {"text": "Fixed_by", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 953, "category": "text_widgets", "suggested_key": "text_fixedby"}, {"text": "Resolved_by", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 958, "category": "text_widgets", "suggested_key": "text_resolvedby"}, {"text": "$statusType By", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 1144, "category": "text_widgets", "suggested_key": "text_statustype_by"}], "app_bar_titles": [], "form_labels": [{"text": "Add your comments here (max 250 characters)...", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 810, "category": "form_labels", "suggested_key": "form_add_your_comments"}], "button_labels": [{"text": "Save Selection", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 614, "category": "button_labels", "suggested_key": "btn_save_selection"}, {"text": "$statusType By", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 1144, "category": "button_labels", "suggested_key": "btn_statustype_by"}], "snackbar_messages": [{"text": "No images available", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 317, "category": "snackbar_messages", "suggested_key": "snackbar_no_images_available"}, {"text": "Error fetching images: $e", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 329, "category": "snackbar_messages", "suggested_key": "snackbar_error_fetching_images"}, {"text": "Issues saved upload Images/Videos", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 490, "category": "snackbar_messages", "suggested_key": "snackbar_issues_saved_upload"}, {"text": "Error updating issue: $e", "file": "../lib/screens/trip_report/widget/CoachIssueImageUpload.dart", "line_number": 651, "category": "snackbar_messages", "suggested_key": "snackbar_error_updating_issue"}], "dialog_content": []}, "../lib/screens/trip_report/widget/TripReportFilePreview.dart": {"text_widgets": [{"text": "Confirm Deletion", "file": "../lib/screens/trip_report/widget/TripReportFilePreview.dart", "line_number": 322, "category": "text_widgets", "suggested_key": "text_confirm_deletion"}, {"text": "Cancel", "file": "../lib/screens/trip_report/widget/TripReportFilePreview.dart", "line_number": 329, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Delete", "file": "../lib/screens/trip_report/widget/TripReportFilePreview.dart", "line_number": 333, "category": "text_widgets", "suggested_key": "text_delete"}, {"text": "Delete Report", "file": "../lib/screens/trip_report/widget/TripReportFilePreview.dart", "line_number": 350, "category": "text_widgets", "suggested_key": "text_delete_report"}, {"text": "Confirm Deletion", "file": "../lib/screens/trip_report/widget/TripReportFilePreview.dart", "line_number": 457, "category": "text_widgets", "suggested_key": "text_confirm_deletion"}, {"text": "Cancel", "file": "../lib/screens/trip_report/widget/TripReportFilePreview.dart", "line_number": 463, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Delete", "file": "../lib/screens/trip_report/widget/TripReportFilePreview.dart", "line_number": 467, "category": "text_widgets", "suggested_key": "text_delete"}, {"text": "Delete Report", "file": "../lib/screens/trip_report/widget/TripReportFilePreview.dart", "line_number": 487, "category": "text_widgets", "suggested_key": "text_delete_report"}, {"text": "$statusType By", "file": "../lib/screens/trip_report/widget/TripReportFilePreview.dart", "line_number": 539, "category": "text_widgets", "suggested_key": "text_statustype_by"}], "app_bar_titles": [], "form_labels": [], "button_labels": [{"text": "$statusType By", "file": "../lib/screens/trip_report/widget/TripReportFilePreview.dart", "line_number": 539, "category": "button_labels", "suggested_key": "btn_statustype_by"}], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/trip_report/widget/CoachIssueStatus.dart": {"text_widgets": [{"text": "No images available", "file": "../lib/screens/trip_report/widget/CoachIssueStatus.dart", "line_number": 79, "category": "text_widgets", "suggested_key": "text_no_images_available"}, {"text": "Error fetching images: $e", "file": "../lib/screens/trip_report/widget/CoachIssueStatus.dart", "line_number": 91, "category": "text_widgets", "suggested_key": "text_error_fetching_images"}, {"text": "Coach Issue Status", "file": "../lib/screens/trip_report/widget/CoachIssueStatus.dart", "line_number": 148, "category": "text_widgets", "suggested_key": "text_coach_issue_status"}], "app_bar_titles": [], "form_labels": [], "button_labels": [{"text": "Coach Issue Status", "file": "../lib/screens/trip_report/widget/CoachIssueStatus.dart", "line_number": 148, "category": "button_labels", "suggested_key": "btn_coach_issue_status"}], "snackbar_messages": [{"text": "No images available", "file": "../lib/screens/trip_report/widget/CoachIssueStatus.dart", "line_number": 79, "category": "snackbar_messages", "suggested_key": "snackbar_no_images_available"}, {"text": "Error fetching images: $e", "file": "../lib/screens/trip_report/widget/CoachIssueStatus.dart", "line_number": 91, "category": "snackbar_messages", "suggested_key": "snackbar_error_fetching_images"}], "dialog_content": []}, "../lib/screens/trip_report/widget/StatusSelectionModal.dart": {"text_widgets": [{"text": "Error updating issue: $e", "file": "../lib/screens/trip_report/widget/StatusSelectionModal.dart", "line_number": 97, "category": "text_widgets", "suggested_key": "text_error_updating_issue"}, {"text": "Error updating issue: $e", "file": "../lib/screens/trip_report/widget/StatusSelectionModal.dart", "line_number": 127, "category": "text_widgets", "suggested_key": "text_error_updating_issue"}, {"text": "Both person and date/time are required.", "file": "../lib/screens/trip_report/widget/StatusSelectionModal.dart", "line_number": 136, "category": "text_widgets", "suggested_key": "text_both_person_and"}, {"text": "Select ${widget.statusType} By & Date", "file": "../lib/screens/trip_report/widget/StatusSelectionModal.dart", "line_number": 156, "category": "text_widgets", "suggested_key": "text_select_widgetstatustype_by"}, {"text": "SubIssue : ${widget.name} ", "file": "../lib/screens/trip_report/widget/StatusSelectionModal.dart", "line_number": 162, "category": "text_widgets", "suggested_key": "text_subissue_widgetname"}, {"text": "Issue : ${widget.name} ", "file": "../lib/screens/trip_report/widget/StatusSelectionModal.dart", "line_number": 164, "category": "text_widgets", "suggested_key": "text_issue_widgetname"}, {"text": "Cancel", "file": "../lib/screens/trip_report/widget/StatusSelectionModal.dart", "line_number": 220, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Confirm", "file": "../lib/screens/trip_report/widget/StatusSelectionModal.dart", "line_number": 231, "category": "text_widgets", "suggested_key": "text_confirm"}], "app_bar_titles": [{"text": "Select ${widget.statusType} By & Date", "file": "../lib/screens/trip_report/widget/StatusSelectionModal.dart", "line_number": 156, "category": "app_bar_titles", "suggested_key": "title_select_widgetstatustype_by"}], "form_labels": [{"text": "Search...", "file": "../lib/screens/trip_report/widget/StatusSelectionModal.dart", "line_number": 171, "category": "form_labels", "suggested_key": "form_search"}, {"text": "${widget.statusType} by", "file": "../lib/screens/trip_report/widget/StatusSelectionModal.dart", "line_number": 178, "category": "form_labels", "suggested_key": "form_widgetstatustype_by"}, {"text": "Select Date & Time", "file": "../lib/screens/trip_report/widget/StatusSelectionModal.dart", "line_number": 203, "category": "form_labels", "suggested_key": "form_select_date_time"}], "button_labels": [], "snackbar_messages": [{"text": "Error updating issue: $e", "file": "../lib/screens/trip_report/widget/StatusSelectionModal.dart", "line_number": 97, "category": "snackbar_messages", "suggested_key": "snackbar_error_updating_issue"}, {"text": "Error updating issue: $e", "file": "../lib/screens/trip_report/widget/StatusSelectionModal.dart", "line_number": 127, "category": "snackbar_messages", "suggested_key": "snackbar_error_updating_issue"}], "dialog_content": []}, "../lib/screens/trip_report/trip_report_screen.dart": {"text_widgets": [{"text": "Invalid response format from server", "file": "../lib/screens/trip_report/trip_report_screen.dart", "line_number": 95, "category": "text_widgets", "suggested_key": "text_invalid_response_format"}, {"text": "Data refreshed successfully", "file": "../lib/screens/trip_report/trip_report_screen.dart", "line_number": 118, "category": "text_widgets", "suggested_key": "text_data_refreshed_successfully"}, {"text": "Manage Issues", "file": "../lib/screens/trip_report/trip_report_screen.dart", "line_number": 255, "category": "text_widgets", "suggested_key": "text_manage_issues"}, {"text": "Please select a train and date first", "file": "../lib/screens/trip_report/trip_report_screen.dart", "line_number": 271, "category": "text_widgets", "suggested_key": "text_please_select_a"}, {"text": "Rake Deficiency Report Issues", "file": "../lib/screens/trip_report/trip_report_screen.dart", "line_number": 361, "category": "text_widgets", "suggested_key": "text_rake_deficiency_report"}], "app_bar_titles": [], "form_labels": [], "button_labels": [{"text": "Rake Deficiency Report Issues", "file": "../lib/screens/trip_report/trip_report_screen.dart", "line_number": 361, "category": "button_labels", "suggested_key": "btn_rake_deficiency_report"}], "snackbar_messages": [{"text": "Invalid response format from server", "file": "../lib/screens/trip_report/trip_report_screen.dart", "line_number": 95, "category": "snackbar_messages", "suggested_key": "snackbar_invalid_response_format"}, {"text": "Data refreshed successfully", "file": "../lib/screens/trip_report/trip_report_screen.dart", "line_number": 118, "category": "snackbar_messages", "suggested_key": "snackbar_data_refreshed_successfully"}, {"text": "Please select a train and date first", "file": "../lib/screens/trip_report/trip_report_screen.dart", "line_number": 271, "category": "snackbar_messages", "suggested_key": "snackbar_please_select_a"}], "dialog_content": []}, "../lib/screens/feedback_screens/passenger_feedback_screen.dart": {"text_widgets": [{"text": "Upload PNR Image", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 710, "category": "text_widgets", "suggested_key": "text_upload_pnr_image"}, {"text": "Camera", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 809, "category": "text_widgets", "suggested_key": "text_camera"}, {"text": "Gallery", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 814, "category": "text_widgets", "suggested_key": "text_gallery"}, {"text": "Pick Images/Videos for Feedback", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 892, "category": "text_widgets", "suggested_key": "text_pick_imagesvideos_for"}, {"text": "Camera", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1054, "category": "text_widgets", "suggested_key": "text_camera"}, {"text": "Gallery", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1059, "category": "text_widgets", "suggested_key": "text_gallery"}, {"text": "Select Media Type", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1072, "category": "text_widgets", "suggested_key": "text_select_media_type"}, {"text": "Image", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1078, "category": "text_widgets", "suggested_key": "text_image"}, {"text": "Video", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1083, "category": "text_widgets", "suggested_key": "text_video"}, {"text": "Please wait until the upload is complete", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1553, "category": "text_widgets", "suggested_key": "text_please_wait_until"}, {"text": "Submit <PERSON>", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1567, "category": "text_widgets", "suggested_key": "text_submit_feedback"}, {"text": "<PERSON><PERSON><PERSON>", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1852, "category": "text_widgets", "suggested_key": "text_verify_email"}, {"text": "Verify OTP", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1892, "category": "text_widgets", "suggested_key": "text_verify_otp"}, {"text": "• Check your inbox first", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1939, "category": "text_widgets", "suggested_key": "text_check_your_inbox"}, {"text": "• If not found, check spam/junk folder", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1940, "category": "text_widgets", "suggested_key": "text_if_not_found"}, {"text": "• Add our domain to your safe sender list", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1941, "category": "text_widgets", "suggested_key": "text_add_our_domain"}, {"text": "I Understand", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1949, "category": "text_widgets", "suggested_key": "text_i_understand"}, {"text": "NONAC", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1998, "category": "text_widgets", "suggested_key": "text_nonac"}], "app_bar_titles": [], "form_labels": [{"text": "Add your feedback here...", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1460, "category": "form_labels", "suggested_key": "form_add_your_feedback"}, {"text": "Task Status *", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1529, "category": "form_labels", "suggested_key": "form_task_status"}, {"text": "Search train number", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1598, "category": "form_labels", "suggested_key": "form_search_train_number"}, {"text": "Train Number *", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1616, "category": "form_labels", "suggested_key": "form_train_number"}, {"text": "PNR Number *", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1637, "category": "form_labels", "suggested_key": "form_pnr_number"}, {"text": "Train Name", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1720, "category": "form_labels", "suggested_key": "form_train_name"}, {"text": "Passenger Name *", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1745, "category": "form_labels", "suggested_key": "form_passenger_name"}, {"text": "Coach No *", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1761, "category": "form_labels", "suggested_key": "form_coach_no"}, {"text": "Berth No *", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1773, "category": "form_labels", "suggested_key": "form_berth_no"}, {"text": "Mobile Number *", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1791, "category": "form_labels", "suggested_key": "form_mobile_number"}, {"text": "Email ID", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1824, "category": "form_labels", "suggested_key": "form_email_id"}, {"text": "Enter OTP", "file": "../lib/screens/feedback_screens/passenger_feedback_screen.dart", "line_number": 1884, "category": "form_labels", "suggested_key": "form_enter_otp"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/feedback_screens/rm_feedback_screen.dart": {"text_widgets": [{"text": "Upload PNR Image", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 520, "category": "text_widgets", "suggested_key": "text_upload_pnr_image"}, {"text": "Camera", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 619, "category": "text_widgets", "suggested_key": "text_camera"}, {"text": "Gallery", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 624, "category": "text_widgets", "suggested_key": "text_gallery"}, {"text": "Camera", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 705, "category": "text_widgets", "suggested_key": "text_camera"}, {"text": "Gallery", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 710, "category": "text_widgets", "suggested_key": "text_gallery"}, {"text": "Select Media Type", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 723, "category": "text_widgets", "suggested_key": "text_select_media_type"}, {"text": "Image", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 729, "category": "text_widgets", "suggested_key": "text_image"}, {"text": "Video", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 734, "category": "text_widgets", "suggested_key": "text_video"}, {"text": "Pick Images/Videos for Feedback", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 857, "category": "text_widgets", "suggested_key": "text_pick_imagesvideos_for"}, {"text": "<PERSON><PERSON><PERSON>", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1208, "category": "text_widgets", "suggested_key": "text_verify_email"}, {"text": "Verify OTP", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1248, "category": "text_widgets", "suggested_key": "text_verify_otp"}, {"text": "• Check your inbox first", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1295, "category": "text_widgets", "suggested_key": "text_check_your_inbox"}, {"text": "• If not found, check spam/junk folder", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1296, "category": "text_widgets", "suggested_key": "text_if_not_found"}, {"text": "• Add our domain to your safe sender list", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1297, "category": "text_widgets", "suggested_key": "text_add_our_domain"}, {"text": "I Understand", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1305, "category": "text_widgets", "suggested_key": "text_i_understand"}, {"text": "Select", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1400, "category": "text_widgets", "suggested_key": "text_select"}, {"text": "Select", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1639, "category": "text_widgets", "suggested_key": "text_select"}, {"text": "Submit <PERSON>", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1742, "category": "text_widgets", "suggested_key": "text_submit_feedback"}], "app_bar_titles": [], "form_labels": [{"text": "Email ID", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1180, "category": "form_labels", "suggested_key": "form_email_id"}, {"text": "Enter OTP", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1240, "category": "form_labels", "suggested_key": "form_enter_otp"}, {"text": "Issue Type", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1319, "category": "form_labels", "suggested_key": "form_issue_type"}, {"text": "Sub Issue Type", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1351, "category": "form_labels", "suggested_key": "form_sub_issue_type"}, {"text": "Resolved (Yes/No) *", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1396, "category": "form_labels", "suggested_key": "form_resolved_yesno"}, {"text": "PNR Number *", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1455, "category": "form_labels", "suggested_key": "form_pnr_number"}, {"text": "CRN Number*", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1532, "category": "form_labels", "suggested_key": "form_crn_number"}, {"text": "Train No *", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1544, "category": "form_labels", "suggested_key": "form_train_no"}, {"text": "Train Name *", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1558, "category": "form_labels", "suggested_key": "form_train_name"}, {"text": "Passenger Name *", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1583, "category": "form_labels", "suggested_key": "form_passenger_name"}, {"text": "Coach No *", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1597, "category": "form_labels", "suggested_key": "form_coach_no"}, {"text": "Berth No *", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1605, "category": "form_labels", "suggested_key": "form_berth_no"}, {"text": "Mobile Number *", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1615, "category": "form_labels", "suggested_key": "form_mobile_number"}, {"text": "Marks (1 to 10) *", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1635, "category": "form_labels", "suggested_key": "form_marks_1_to"}, {"text": "Remarks by Passenger", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1672, "category": "form_labels", "suggested_key": "form_remarks_by_passenger"}, {"text": "Task Status *", "file": "../lib/screens/feedback_screens/rm_feedback_screen.dart", "line_number": 1714, "category": "form_labels", "suggested_key": "form_task_status"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart": {"text_widgets": [{"text": "Failed to load image", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 344, "category": "text_widgets", "suggested_key": "text_failed_to_load"}, {"text": "• Check your inbox first", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 515, "category": "text_widgets", "suggested_key": "text_check_your_inbox"}, {"text": "• If not found, check spam/junk folder", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 516, "category": "text_widgets", "suggested_key": "text_if_not_found"}, {"text": "• Add our domain to your safe sender list", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 517, "category": "text_widgets", "suggested_key": "text_add_our_domain"}, {"text": "I Understand", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 523, "category": "text_widgets", "suggested_key": "text_i_understand"}, {"text": "<PERSON><PERSON><PERSON>", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 652, "category": "text_widgets", "suggested_key": "text_verify_email"}, {"text": "Verify OTP", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 694, "category": "text_widgets", "suggested_key": "text_verify_otp"}, {"text": "Select", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 871, "category": "text_widgets", "suggested_key": "text_select"}, {"text": "Review Feedback", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 909, "category": "text_widgets", "suggested_key": "text_review_feedback"}, {"text": "Select", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 1085, "category": "text_widgets", "suggested_key": "text_select"}, {"text": "Pending", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 1181, "category": "text_widgets", "suggested_key": "text_pending"}, {"text": "Completed", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 1185, "category": "text_widgets", "suggested_key": "text_completed"}, {"text": "Cancel", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 1204, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Update", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 1215, "category": "text_widgets", "suggested_key": "text_update"}], "app_bar_titles": [], "form_labels": [{"text": "Email ID", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 620, "category": "form_labels", "suggested_key": "form_email_id"}, {"text": "Enter OTP", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 681, "category": "form_labels", "suggested_key": "form_enter_otp"}, {"text": "Issue Type", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 766, "category": "form_labels", "suggested_key": "form_issue_type"}, {"text": "Issue Type", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 796, "category": "form_labels", "suggested_key": "form_issue_type"}, {"text": "Sub Issue Type", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 810, "category": "form_labels", "suggested_key": "form_sub_issue_type"}, {"text": "Sub Issue Type", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 850, "category": "form_labels", "suggested_key": "form_sub_issue_type"}, {"text": "Resolved (Yes/No) *", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 867, "category": "form_labels", "suggested_key": "form_resolved_yesno"}, {"text": "Resolved (Yes/No) *", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 893, "category": "form_labels", "suggested_key": "form_resolved_yesno"}, {"text": "Passenger Name", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 967, "category": "form_labels", "suggested_key": "form_passenger_name"}, {"text": "PNR Number", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 984, "category": "form_labels", "suggested_key": "form_pnr_number"}, {"text": "CRN Number", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 1001, "category": "form_labels", "suggested_key": "form_crn_number"}, {"text": "Coach No", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 1021, "category": "form_labels", "suggested_key": "form_coach_no"}, {"text": "Berth No", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 1040, "category": "form_labels", "suggested_key": "form_berth_no"}, {"text": "Mobile Number", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 1062, "category": "form_labels", "suggested_key": "form_mobile_number"}, {"text": "Marks (1 to 10) *", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 1081, "category": "form_labels", "suggested_key": "form_marks_1_to"}, {"text": "Remarks", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 1125, "category": "form_labels", "suggested_key": "form_remarks"}, {"text": "Task Status", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 1175, "category": "form_labels", "suggested_key": "form_task_status"}], "button_labels": [{"text": "Pending", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 1181, "category": "button_labels", "suggested_key": "btn_pending"}, {"text": "Completed", "file": "../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart", "line_number": 1185, "category": "button_labels", "suggested_key": "btn_completed"}], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/feedback_screens/widgets/review_feedback.dart": {"text_widgets": [{"text": "Deleting feedback...", "file": "../lib/screens/feedback_screens/widgets/review_feedback.dart", "line_number": 320, "category": "text_widgets", "suggested_key": "text_deleting_feedback"}, {"text": "Feedback deleted successfully", "file": "../lib/screens/feedback_screens/widgets/review_feedback.dart", "line_number": 342, "category": "text_widgets", "suggested_key": "text_feedback_deleted_successfully"}, {"text": "Error deleting feedback: $e", "file": "../lib/screens/feedback_screens/widgets/review_feedback.dart", "line_number": 349, "category": "text_widgets", "suggested_key": "text_error_deleting_feedback"}, {"text": "Retry", "file": "../lib/screens/feedback_screens/widgets/review_feedback.dart", "line_number": 415, "category": "text_widgets", "suggested_key": "text_retry"}, {"text": "No feedback available for this train.", "file": "../lib/screens/feedback_screens/widgets/review_feedback.dart", "line_number": 425, "category": "text_widgets", "suggested_key": "text_no_feedback_available"}, {"text": "Train No: $trainNumber", "file": "../lib/screens/feedback_screens/widgets/review_feedback.dart", "line_number": 523, "category": "text_widgets", "suggested_key": "text_train_no_trainnumber"}], "app_bar_titles": [], "form_labels": [], "button_labels": [{"text": "No feedback available for this train.", "file": "../lib/screens/feedback_screens/widgets/review_feedback.dart", "line_number": 425, "category": "button_labels", "suggested_key": "btn_no_feedback_available"}], "snackbar_messages": [{"text": "Feedback deleted successfully", "file": "../lib/screens/feedback_screens/widgets/review_feedback.dart", "line_number": 342, "category": "snackbar_messages", "suggested_key": "snackbar_feedback_deleted_successfully"}, {"text": "Error deleting feedback: $e", "file": "../lib/screens/feedback_screens/widgets/review_feedback.dart", "line_number": 349, "category": "snackbar_messages", "suggested_key": "snackbar_error_deleting_feedback"}], "dialog_content": []}, "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart": {"text_widgets": [{"text": "Non AC", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 339, "category": "text_widgets", "suggested_key": "text_non_ac"}, {"text": "• Check your inbox first", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 591, "category": "text_widgets", "suggested_key": "text_check_your_inbox"}, {"text": "• If not found, check spam/junk folder", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 592, "category": "text_widgets", "suggested_key": "text_if_not_found"}, {"text": "• Add our domain to your safe sender list", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 593, "category": "text_widgets", "suggested_key": "text_add_our_domain"}, {"text": "I Understand", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 599, "category": "text_widgets", "suggested_key": "text_i_understand"}, {"text": "<PERSON><PERSON><PERSON>", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 744, "category": "text_widgets", "suggested_key": "text_verify_email"}, {"text": "Verify OTP", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 788, "category": "text_widgets", "suggested_key": "text_verify_otp"}, {"text": "Review Feedback", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 860, "category": "text_widgets", "suggested_key": "text_review_feedback"}, {"text": "Pending", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 1026, "category": "text_widgets", "suggested_key": "text_pending"}, {"text": "Completed", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 1030, "category": "text_widgets", "suggested_key": "text_completed"}, {"text": "Cancel", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 1117, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Update", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 1128, "category": "text_widgets", "suggested_key": "text_update"}], "app_bar_titles": [], "form_labels": [{"text": "Email ID", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 709, "category": "form_labels", "suggested_key": "form_email_id"}, {"text": "Enter OTP", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 775, "category": "form_labels", "suggested_key": "form_enter_otp"}, {"text": "Passenger Name", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 923, "category": "form_labels", "suggested_key": "form_passenger_name"}, {"text": "PNR Number", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 940, "category": "form_labels", "suggested_key": "form_pnr_number"}, {"text": "Coach No", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 960, "category": "form_labels", "suggested_key": "form_coach_no"}, {"text": "Berth No", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 979, "category": "form_labels", "suggested_key": "form_berth_no"}, {"text": "Mobile Number", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 1001, "category": "form_labels", "suggested_key": "form_mobile_number"}, {"text": "Task Status", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 1020, "category": "form_labels", "suggested_key": "form_task_status"}, {"text": "<PERSON><PERSON><PERSON>", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 1058, "category": "form_labels", "suggested_key": "form_feedback"}], "button_labels": [{"text": "Pending", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 1026, "category": "button_labels", "suggested_key": "btn_pending"}, {"text": "Completed", "file": "../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart", "line_number": 1030, "category": "button_labels", "suggested_key": "btn_completed"}], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/assign_obhs/assign_obhs_screen.dart": {"text_widgets": [{"text": "Message", "file": "../lib/screens/assign_obhs/assign_obhs_screen.dart", "line_number": 265, "category": "text_widgets", "suggested_key": "text_message"}, {"text": "Data refreshed successfully", "file": "../lib/screens/assign_obhs/assign_obhs_screen.dart", "line_number": 393, "category": "text_widgets", "suggested_key": "text_data_refreshed_successfully"}, {"text": "Job Chart Status Added", "file": "../lib/screens/assign_obhs/assign_obhs_screen.dart", "line_number": 474, "category": "text_widgets", "suggested_key": "text_job_chart_status"}, {"text": "Error: $e", "file": "../lib/screens/assign_obhs/assign_obhs_screen.dart", "line_number": 477, "category": "text_widgets", "suggested_key": "text_error_e"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [{"text": "Data refreshed successfully", "file": "../lib/screens/assign_obhs/assign_obhs_screen.dart", "line_number": 393, "category": "snackbar_messages", "suggested_key": "snackbar_data_refreshed_successfully"}, {"text": "Job Chart Status Added", "file": "../lib/screens/assign_obhs/assign_obhs_screen.dart", "line_number": 474, "category": "snackbar_messages", "suggested_key": "snackbar_job_chart_status"}, {"text": "Error: $e", "file": "../lib/screens/assign_obhs/assign_obhs_screen.dart", "line_number": 477, "category": "snackbar_messages", "suggested_key": "snackbar_error_e"}], "dialog_content": []}, "../lib/screens/assign_obhs/widgets/assign_obhs_filters.dart": {"text_widgets": [{"text": "Please select all fields before submitting.", "file": "../lib/screens/assign_obhs/widgets/assign_obhs_filters.dart", "line_number": 113, "category": "text_widgets", "suggested_key": "text_please_select_all"}], "app_bar_titles": [], "form_labels": [{"text": "Train Number", "file": "../lib/screens/assign_obhs/widgets/assign_obhs_filters.dart", "line_number": 299, "category": "form_labels", "suggested_key": "form_train_number"}, {"text": "Train Name", "file": "../lib/screens/assign_obhs/widgets/assign_obhs_filters.dart", "line_number": 347, "category": "form_labels", "suggested_key": "form_train_name"}, {"text": "Select Date (DD-MMM-YYYY)", "file": "../lib/screens/assign_obhs/widgets/assign_obhs_filters.dart", "line_number": 361, "category": "form_labels", "suggested_key": "form_select_date_ddmmmyyyy"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/assign_obhs/widgets/assign_obhs_table.dart": {"text_widgets": [{"text": "Update Amount for $userId", "file": "../lib/screens/assign_obhs/widgets/assign_obhs_table.dart", "line_number": 388, "category": "text_widgets", "suggested_key": "text_update_amount_for"}, {"text": "Cancel", "file": "../lib/screens/assign_obhs/widgets/assign_obhs_table.dart", "line_number": 399, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Assigned", "file": "../lib/screens/assign_obhs/widgets/assign_obhs_table.dart", "line_number": 517, "category": "text_widgets", "suggested_key": "text_assigned"}, {"text": "Amount", "file": "../lib/screens/assign_obhs/widgets/assign_obhs_table.dart", "line_number": 526, "category": "text_widgets", "suggested_key": "text_amount"}], "app_bar_titles": [{"text": "Update Amount for $userId", "file": "../lib/screens/assign_obhs/widgets/assign_obhs_table.dart", "line_number": 388, "category": "app_bar_titles", "suggested_key": "title_update_amount_for"}], "form_labels": [{"text": "Amount in Hand (₹)", "file": "../lib/screens/assign_obhs/widgets/assign_obhs_table.dart", "line_number": 393, "category": "form_labels", "suggested_key": "form_amount_in_hand"}, {"text": "Select User", "file": "../lib/screens/assign_obhs/widgets/assign_obhs_table.dart", "line_number": 660, "category": "form_labels", "suggested_key": "form_select_user"}, {"text": "Select User", "file": "../lib/screens/assign_obhs/widgets/assign_obhs_table.dart", "line_number": 905, "category": "form_labels", "suggested_key": "form_select_user"}, {"text": "Amount in Hand (₹)", "file": "../lib/screens/assign_obhs/widgets/assign_obhs_table.dart", "line_number": 938, "category": "form_labels", "suggested_key": "form_amount_in_hand"}, {"text": "Amount in Hand (₹)", "file": "../lib/screens/assign_obhs/widgets/assign_obhs_table.dart", "line_number": 1067, "category": "form_labels", "suggested_key": "form_amount_in_hand"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/pages/image_detail_page.dart": {"text_widgets": [{"text": "No image URL provided", "file": "../lib/screens/pages/image_detail_page.dart", "line_number": 22, "category": "text_widgets", "suggested_key": "text_no_image_url"}, {"text": "Image downloaded successfully!", "file": "../lib/screens/pages/image_detail_page.dart", "line_number": 46, "category": "text_widgets", "suggested_key": "text_image_downloaded_successfully"}, {"text": "Failed to download image", "file": "../lib/screens/pages/image_detail_page.dart", "line_number": 51, "category": "text_widgets", "suggested_key": "text_failed_to_download"}, {"text": "Failed to download image: $error", "file": "../lib/screens/pages/image_detail_page.dart", "line_number": 56, "category": "text_widgets", "suggested_key": "text_failed_to_download"}, {"text": "Image Detail", "file": "../lib/screens/pages/image_detail_page.dart", "line_number": 71, "category": "text_widgets", "suggested_key": "text_image_detail"}, {"text": "Download Image", "file": "../lib/screens/pages/image_detail_page.dart", "line_number": 136, "category": "text_widgets", "suggested_key": "text_download_image"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [{"text": "No image URL provided", "file": "../lib/screens/pages/image_detail_page.dart", "line_number": 22, "category": "snackbar_messages", "suggested_key": "snackbar_no_image_url"}, {"text": "Image downloaded successfully!", "file": "../lib/screens/pages/image_detail_page.dart", "line_number": 46, "category": "snackbar_messages", "suggested_key": "snackbar_image_downloaded_successfully"}, {"text": "Failed to download image", "file": "../lib/screens/pages/image_detail_page.dart", "line_number": 51, "category": "snackbar_messages", "suggested_key": "snackbar_failed_to_download"}, {"text": "Failed to download image: $error", "file": "../lib/screens/pages/image_detail_page.dart", "line_number": 56, "category": "snackbar_messages", "suggested_key": "snackbar_failed_to_download"}], "dialog_content": []}, "../lib/screens/profile_screen/change_email_screen.dart": {"text_widgets": [{"text": "Error loading authentication state", "file": "../lib/screens/profile_screen/change_email_screen.dart", "line_number": 63, "category": "text_widgets", "suggested_key": "text_error_loading_authentication"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/profile_screen/add_train_screen.dart": {"text_widgets": [{"text": "Train $trainNumber details deleted successfully", "file": "../lib/screens/profile_screen/add_train_screen.dart", "line_number": 84, "category": "text_widgets", "suggested_key": "text_train_trainnumber_details"}, {"text": "Error loading authentication state", "file": "../lib/screens/profile_screen/add_train_screen.dart", "line_number": 168, "category": "text_widgets", "suggested_key": "text_error_loading_authentication"}, {"text": "No train details available.", "file": "../lib/screens/profile_screen/add_train_screen.dart", "line_number": 185, "category": "text_widgets", "suggested_key": "text_no_train_details"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/profile_screen/change_whatsapp_screen.dart": {"text_widgets": [{"text": "Error loading authentication state", "file": "../lib/screens/profile_screen/change_whatsapp_screen.dart", "line_number": 37, "category": "text_widgets", "suggested_key": "text_error_loading_authentication"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/profile_screen/change_password_screen.dart": {"text_widgets": [{"text": "Error loading authentication state", "file": "../lib/screens/profile_screen/change_password_screen.dart", "line_number": 39, "category": "text_widgets", "suggested_key": "text_error_loading_authentication"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/profile_screen/edit_profile_screen.dart": {"text_widgets": [{"text": "Error loading authentication state", "file": "../lib/screens/profile_screen/edit_profile_screen.dart", "line_number": 36, "category": "text_widgets", "suggested_key": "text_error_loading_authentication"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/profile_screen/change_mobile_screen.dart": {"text_widgets": [{"text": "Error loading authentication state", "file": "../lib/screens/profile_screen/change_mobile_screen.dart", "line_number": 36, "category": "text_widgets", "suggested_key": "text_error_loading_authentication"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/profile_screen/widgets/edit_profile_form.dart": {"text_widgets": [{"text": "Confirm Deactivation", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 357, "category": "text_widgets", "suggested_key": "text_confirm_deactivation"}, {"text": "Cancel", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 364, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Proceed", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 369, "category": "text_widgets", "suggested_key": "text_proceed"}, {"text": "Add <PERSON>", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 378, "category": "text_widgets", "suggested_key": "text_add_email"}, {"text": "<PERSON><PERSON><PERSON>", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 454, "category": "text_widgets", "suggested_key": "text_verify_email"}, {"text": "Cancel", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 458, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Back", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 508, "category": "text_widgets", "suggested_key": "text_back"}, {"text": "Email Verification", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 516, "category": "text_widgets", "suggested_key": "text_email_verification"}, {"text": "Phone Verification", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 530, "category": "text_widgets", "suggested_key": "text_phone_verification"}, {"text": "Logout Confirmation", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 570, "category": "text_widgets", "suggested_key": "text_logout_confirmation"}, {"text": "Do you want to logout now?", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 571, "category": "text_widgets", "suggested_key": "text_do_you_want"}, {"text": "Success", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 862, "category": "text_widgets", "suggested_key": "text_success"}], "app_bar_titles": [], "form_labels": [{"text": "Enter Email OTP", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 522, "category": "form_labels", "suggested_key": "form_enter_email_otp"}, {"text": "Enter Phone OTP", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 536, "category": "form_labels", "suggested_key": "form_enter_phone_otp"}, {"text": "Select Train Number", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 939, "category": "form_labels", "suggested_key": "form_select_train_number"}, {"text": "Select Train Number", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 1064, "category": "form_labels", "suggested_key": "form_select_train_number"}, {"text": "Select Date", "file": "../lib/screens/profile_screen/widgets/edit_profile_form.dart", "line_number": 1089, "category": "form_labels", "suggested_key": "form_select_date"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/profile_screen/widgets/add_train_form.dart": {"text_widgets": [{"text": "Error", "file": "../lib/screens/profile_screen/widgets/add_train_form.dart", "line_number": 130, "category": "text_widgets", "suggested_key": "text_error"}, {"text": "Add/Update", "file": "../lib/screens/profile_screen/widgets/add_train_form.dart", "line_number": 338, "category": "text_widgets", "suggested_key": "text_addupdate"}], "app_bar_titles": [], "form_labels": [{"text": "Select Train Number", "file": "../lib/screens/profile_screen/widgets/add_train_form.dart", "line_number": 174, "category": "form_labels", "suggested_key": "form_select_train_number"}, {"text": "Select Coaches (optional)", "file": "../lib/screens/profile_screen/widgets/add_train_form.dart", "line_number": 212, "category": "form_labels", "suggested_key": "form_select_coaches_optional"}, {"text": "Select Date", "file": "../lib/screens/profile_screen/widgets/add_train_form.dart", "line_number": 299, "category": "form_labels", "suggested_key": "form_select_date"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/profile_screen/widgets/change_whatsapp_form.dart": {"text_widgets": [{"text": "Success", "file": "../lib/screens/profile_screen/widgets/change_whatsapp_form.dart", "line_number": 148, "category": "text_widgets", "suggested_key": "text_success"}, {"text": "<PERSON><PERSON>", "file": "../lib/screens/profile_screen/widgets/change_whatsapp_form.dart", "line_number": 168, "category": "text_widgets", "suggested_key": "text_alert"}, {"text": "Close", "file": "../lib/screens/profile_screen/widgets/change_whatsapp_form.dart", "line_number": 172, "category": "text_widgets", "suggested_key": "text_close"}, {"text": "Verify OTP", "file": "../lib/screens/profile_screen/widgets/change_whatsapp_form.dart", "line_number": 311, "category": "text_widgets", "suggested_key": "text_verify_otp"}, {"text": "Generate OTP", "file": "../lib/screens/profile_screen/widgets/change_whatsapp_form.dart", "line_number": 329, "category": "text_widgets", "suggested_key": "text_generate_otp"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/profile_screen/widgets/change_mobile_form.dart": {"text_widgets": [{"text": "Success", "file": "../lib/screens/profile_screen/widgets/change_mobile_form.dart", "line_number": 157, "category": "text_widgets", "suggested_key": "text_success"}, {"text": "<PERSON><PERSON>", "file": "../lib/screens/profile_screen/widgets/change_mobile_form.dart", "line_number": 177, "category": "text_widgets", "suggested_key": "text_alert"}, {"text": "Close", "file": "../lib/screens/profile_screen/widgets/change_mobile_form.dart", "line_number": 181, "category": "text_widgets", "suggested_key": "text_close"}, {"text": "Verify OTP", "file": "../lib/screens/profile_screen/widgets/change_mobile_form.dart", "line_number": 319, "category": "text_widgets", "suggested_key": "text_verify_otp"}, {"text": "Generate OTP", "file": "../lib/screens/profile_screen/widgets/change_mobile_form.dart", "line_number": 337, "category": "text_widgets", "suggested_key": "text_generate_otp"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/profile_screen/widgets/change_email_modal.dart": {"text_widgets": [{"text": "OTP sent successfully!", "file": "../lib/screens/profile_screen/widgets/change_email_modal.dart", "line_number": 69, "category": "text_widgets", "suggested_key": "text_otp_sent_successfully"}, {"text": "Failed to send OTP: $e", "file": "../lib/screens/profile_screen/widgets/change_email_modal.dart", "line_number": 74, "category": "text_widgets", "suggested_key": "text_failed_to_send"}, {"text": "Please enter the OTP", "file": "../lib/screens/profile_screen/widgets/change_email_modal.dart", "line_number": 87, "category": "text_widgets", "suggested_key": "text_please_enter_the"}, {"text": "<PERSON><PERSON> saved successfully!", "file": "../lib/screens/profile_screen/widgets/change_email_modal.dart", "line_number": 100, "category": "text_widgets", "suggested_key": "text_email_saved_successfully"}, {"text": "Failed to verify OTP: $e", "file": "../lib/screens/profile_screen/widgets/change_email_modal.dart", "line_number": 105, "category": "text_widgets", "suggested_key": "text_failed_to_verify"}, {"text": "Verify OTP", "file": "../lib/screens/profile_screen/widgets/change_email_modal.dart", "line_number": 165, "category": "text_widgets", "suggested_key": "text_verify_otp"}, {"text": "Send OTP", "file": "../lib/screens/profile_screen/widgets/change_email_modal.dart", "line_number": 179, "category": "text_widgets", "suggested_key": "text_send_otp"}, {"text": "Cancel", "file": "../lib/screens/profile_screen/widgets/change_email_modal.dart", "line_number": 199, "category": "text_widgets", "suggested_key": "text_cancel"}], "app_bar_titles": [], "form_labels": [{"text": "Email", "file": "../lib/screens/profile_screen/widgets/change_email_modal.dart", "line_number": 131, "category": "form_labels", "suggested_key": "form_email"}], "button_labels": [{"text": "Cancel", "file": "../lib/screens/profile_screen/widgets/change_email_modal.dart", "line_number": 199, "category": "button_labels", "suggested_key": "btn_cancel"}], "snackbar_messages": [{"text": "OTP sent successfully!", "file": "../lib/screens/profile_screen/widgets/change_email_modal.dart", "line_number": 69, "category": "snackbar_messages", "suggested_key": "snackbar_otp_sent_successfully"}, {"text": "Failed to send OTP: $e", "file": "../lib/screens/profile_screen/widgets/change_email_modal.dart", "line_number": 74, "category": "snackbar_messages", "suggested_key": "snackbar_failed_to_send"}, {"text": "Please enter the OTP", "file": "../lib/screens/profile_screen/widgets/change_email_modal.dart", "line_number": 87, "category": "snackbar_messages", "suggested_key": "snackbar_please_enter_the"}, {"text": "<PERSON><PERSON> saved successfully!", "file": "../lib/screens/profile_screen/widgets/change_email_modal.dart", "line_number": 100, "category": "snackbar_messages", "suggested_key": "snackbar_email_saved_successfully"}, {"text": "Failed to verify OTP: $e", "file": "../lib/screens/profile_screen/widgets/change_email_modal.dart", "line_number": 105, "category": "snackbar_messages", "suggested_key": "snackbar_failed_to_verify"}], "dialog_content": []}, "../lib/screens/profile_screen/widgets/change_password_form.dart": {"text_widgets": [{"text": "<PERSON><PERSON>", "file": "../lib/screens/profile_screen/widgets/change_password_form.dart", "line_number": 149, "category": "text_widgets", "suggested_key": "text_alert"}, {"text": "Close", "file": "../lib/screens/profile_screen/widgets/change_password_form.dart", "line_number": 153, "category": "text_widgets", "suggested_key": "text_close"}, {"text": "Verify OTP", "file": "../lib/screens/profile_screen/widgets/change_password_form.dart", "line_number": 311, "category": "text_widgets", "suggested_key": "text_verify_otp"}, {"text": "Send Mobile OTP", "file": "../lib/screens/profile_screen/widgets/change_password_form.dart", "line_number": 329, "category": "text_widgets", "suggested_key": "text_send_mobile_otp"}, {"text": "Send Email OTP", "file": "../lib/screens/profile_screen/widgets/change_password_form.dart", "line_number": 346, "category": "text_widgets", "suggested_key": "text_send_email_otp"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/profile_screen/widgets/change_email_form.dart": {"text_widgets": [{"text": "<PERSON><PERSON>", "file": "../lib/screens/profile_screen/widgets/change_email_form.dart", "line_number": 139, "category": "text_widgets", "suggested_key": "text_alert"}, {"text": "Close", "file": "../lib/screens/profile_screen/widgets/change_email_form.dart", "line_number": 143, "category": "text_widgets", "suggested_key": "text_close"}, {"text": "Verify OTP", "file": "../lib/screens/profile_screen/widgets/change_email_form.dart", "line_number": 250, "category": "text_widgets", "suggested_key": "text_verify_otp"}, {"text": "Generate OTP", "file": "../lib/screens/profile_screen/widgets/change_email_form.dart", "line_number": 263, "category": "text_widgets", "suggested_key": "text_generate_otp"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/assign_ehk_ca_screen/assign_ehk_ca_screen.dart": {"text_widgets": [{"text": "Data refreshed successfully", "file": "../lib/screens/assign_ehk_ca_screen/assign_ehk_ca_screen.dart", "line_number": 559, "category": "text_widgets", "suggested_key": "text_data_refreshed_successfully"}, {"text": "Job Chart Status Added", "file": "../lib/screens/assign_ehk_ca_screen/assign_ehk_ca_screen.dart", "line_number": 589, "category": "text_widgets", "suggested_key": "text_job_chart_status"}, {"text": "Error: $e", "file": "../lib/screens/assign_ehk_ca_screen/assign_ehk_ca_screen.dart", "line_number": 592, "category": "text_widgets", "suggested_key": "text_error_e"}, {"text": "Message", "file": "../lib/screens/assign_ehk_ca_screen/assign_ehk_ca_screen.dart", "line_number": 1140, "category": "text_widgets", "suggested_key": "text_message"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [{"text": "Data refreshed successfully", "file": "../lib/screens/assign_ehk_ca_screen/assign_ehk_ca_screen.dart", "line_number": 559, "category": "snackbar_messages", "suggested_key": "snackbar_data_refreshed_successfully"}, {"text": "Job Chart Status Added", "file": "../lib/screens/assign_ehk_ca_screen/assign_ehk_ca_screen.dart", "line_number": 589, "category": "snackbar_messages", "suggested_key": "snackbar_job_chart_status"}, {"text": "Error: $e", "file": "../lib/screens/assign_ehk_ca_screen/assign_ehk_ca_screen.dart", "line_number": 592, "category": "snackbar_messages", "suggested_key": "snackbar_error_e"}], "dialog_content": []}, "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_uploaded_image.dart": {"text_widgets": [{"text": "Uploaded at: $formattedDate", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_uploaded_image.dart", "line_number": 99, "category": "text_widgets", "suggested_key": "text_uploaded_at_formatteddate"}, {"text": "Uploaded by: ${widget.imageResponse.createdBy}", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_uploaded_image.dart", "line_number": 100, "category": "text_widgets", "suggested_key": "text_uploaded_by_widgetimageresponsecreatedby"}, {"text": "id: ${widget.imageResponse.id}", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_uploaded_image.dart", "line_number": 104, "category": "text_widgets", "suggested_key": "text_id_widgetimageresponseid"}, {"text": "Coach: ${widget.imageResponse.coach}", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_uploaded_image.dart", "line_number": 107, "category": "text_widgets", "suggested_key": "text_coach_widgetimageresponsecoach"}, {"text": "Issue: ${widget.imageResponse.issue}", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_uploaded_image.dart", "line_number": 109, "category": "text_widgets", "suggested_key": "text_issue_widgetimageresponseissue"}, {"text": "Delete Confirmation", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_uploaded_image.dart", "line_number": 130, "category": "text_widgets", "suggested_key": "text_delete_confirmation"}, {"text": "Are you sure you want to delete this image?", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_uploaded_image.dart", "line_number": 131, "category": "text_widgets", "suggested_key": "text_are_you_sure"}, {"text": "Cancel", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_uploaded_image.dart", "line_number": 135, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "Delete", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_uploaded_image.dart", "line_number": 139, "category": "text_widgets", "suggested_key": "text_delete"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/assign_ehk_ca_screen/widgets/return_gap.dart": {"text_widgets": [{"text": "Save", "file": "../lib/screens/assign_ehk_ca_screen/widgets/return_gap.dart", "line_number": 142, "category": "text_widgets", "suggested_key": "text_save"}], "app_bar_titles": [], "form_labels": [{"text": "Return Gap (Days)", "file": "../lib/screens/assign_ehk_ca_screen/widgets/return_gap.dart", "line_number": 95, "category": "form_labels", "suggested_key": "form_return_gap_days"}, {"text": "In/Out", "file": "../lib/screens/assign_ehk_ca_screen/widgets/return_gap.dart", "line_number": 121, "category": "form_labels", "suggested_key": "form_inout"}], "button_labels": [{"text": "Save", "file": "../lib/screens/assign_ehk_ca_screen/widgets/return_gap.dart", "line_number": 142, "category": "button_labels", "suggested_key": "btn_save"}], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_table.dart": {"text_widgets": [{"text": "Update Amount for $userId", "file": "../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_table.dart", "line_number": 278, "category": "text_widgets", "suggested_key": "text_update_amount_for"}, {"text": "Cancel", "file": "../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_table.dart", "line_number": 289, "category": "text_widgets", "suggested_key": "text_cancel"}, {"text": "EHK/CA", "file": "../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_table.dart", "line_number": 472, "category": "text_widgets", "suggested_key": "text_ehkca"}, {"text": "Assigned", "file": "../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_table.dart", "line_number": 477, "category": "text_widgets", "suggested_key": "text_assigned"}, {"text": "Amount", "file": "../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_table.dart", "line_number": 488, "category": "text_widgets", "suggested_key": "text_amount"}], "app_bar_titles": [{"text": "Update Amount for $userId", "file": "../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_table.dart", "line_number": 278, "category": "app_bar_titles", "suggested_key": "title_update_amount_for"}], "form_labels": [{"text": "Amount in Hand (₹)", "file": "../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_table.dart", "line_number": 283, "category": "form_labels", "suggested_key": "form_amount_in_hand"}, {"text": "Select User", "file": "../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_table.dart", "line_number": 559, "category": "form_labels", "suggested_key": "form_select_user"}, {"text": "Select User", "file": "../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_table.dart", "line_number": 785, "category": "form_labels", "suggested_key": "form_select_user"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_filters.dart": {"text_widgets": [{"text": "Please select all fields before submitting.", "file": "../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_filters.dart", "line_number": 114, "category": "text_widgets", "suggested_key": "text_please_select_all"}], "app_bar_titles": [], "form_labels": [{"text": "Train Number", "file": "../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_filters.dart", "line_number": 300, "category": "form_labels", "suggested_key": "form_train_number"}, {"text": "Train Name", "file": "../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_filters.dart", "line_number": 348, "category": "form_labels", "suggested_key": "form_train_name"}, {"text": "Select Date (DD-MMM-YYYY)", "file": "../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_filters.dart", "line_number": 362, "category": "form_labels", "suggested_key": "form_select_date_ddmmmyyyy"}], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart": {"text_widgets": [{"text": "Camera", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 84, "category": "text_widgets", "suggested_key": "text_camera"}, {"text": "Gallery", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 89, "category": "text_widgets", "suggested_key": "text_gallery"}, {"text": "Submitting...", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 124, "category": "text_widgets", "suggested_key": "text_submitting"}, {"text": "Image upload initiated successfully", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 150, "category": "text_widgets", "suggested_key": "text_image_upload_initiated"}, {"text": "Failed to upload image", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 154, "category": "text_widgets", "suggested_key": "text_failed_to_upload"}, {"text": "Error: $e", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 168, "category": "text_widgets", "suggested_key": "text_error_e"}, {"text": "Please select an image to upload", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 173, "category": "text_widgets", "suggested_key": "text_please_select_an"}, {"text": "No images available", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 198, "category": "text_widgets", "suggested_key": "text_no_images_available"}, {"text": "Error fetching images: $e", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 210, "category": "text_widgets", "suggested_key": "text_error_fetching_images"}, {"text": "Job<PERSON>hart deleted successfully", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 233, "category": "text_widgets", "suggested_key": "text_jobchart_deleted_successfully"}, {"text": "Failed to delete JobChart", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 238, "category": "text_widgets", "suggested_key": "text_failed_to_delete"}, {"text": "Error: $e", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 243, "category": "text_widgets", "suggested_key": "text_error_e"}, {"text": "Pick Image", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 338, "category": "text_widgets", "suggested_key": "text_pick_image"}, {"text": "Please select an image to upload", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 359, "category": "text_widgets", "suggested_key": "text_please_select_an"}, {"text": "Upload Image", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 371, "category": "text_widgets", "suggested_key": "text_upload_image"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [{"text": "No images available", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 198, "category": "snackbar_messages", "suggested_key": "snackbar_no_images_available"}, {"text": "Error fetching images: $e", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 210, "category": "snackbar_messages", "suggested_key": "snackbar_error_fetching_images"}, {"text": "Job<PERSON>hart deleted successfully", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 233, "category": "snackbar_messages", "suggested_key": "snackbar_jobchart_deleted_successfully"}, {"text": "Failed to delete JobChart", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 238, "category": "snackbar_messages", "suggested_key": "snackbar_failed_to_delete"}, {"text": "Error: $e", "file": "../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart", "line_number": 243, "category": "snackbar_messages", "suggested_key": "snackbar_error_e"}], "dialog_content": []}, "../lib/widgets/success_modal.dart": {"text_widgets": [{"text": "Close", "file": "../lib/widgets/success_modal.dart", "line_number": 20, "category": "text_widgets", "suggested_key": "text_close"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/widgets/custom_app_bar.dart": {"text_widgets": [{"text": "Failed to load train numbers: $e", "file": "../lib/widgets/custom_app_bar.dart", "line_number": 193, "category": "text_widgets", "suggested_key": "text_failed_to_load"}, {"text": "Upload Status", "file": "../lib/widgets/custom_app_bar.dart", "line_number": 581, "category": "text_widgets", "suggested_key": "text_upload_status"}, {"text": "Compressing image", "file": "../lib/widgets/custom_app_bar.dart", "line_number": 595, "category": "text_widgets", "suggested_key": "text_compressing_image"}, {"text": "Upload ${entry.key.substring(0, 6)}...", "file": "../lib/widgets/custom_app_bar.dart", "line_number": 616, "category": "text_widgets", "suggested_key": "text_upload_entrykeysubstring0_6"}, {"text": "Close", "file": "../lib/widgets/custom_app_bar.dart", "line_number": 628, "category": "text_widgets", "suggested_key": "text_close"}], "app_bar_titles": [{"text": "Compressing image", "file": "../lib/widgets/custom_app_bar.dart", "line_number": 595, "category": "app_bar_titles", "suggested_key": "title_compressing_image"}, {"text": "Upload ${entry.key.substring(0, 6)}...", "file": "../lib/widgets/custom_app_bar.dart", "line_number": 616, "category": "app_bar_titles", "suggested_key": "title_upload_entrykeysubstring0_6"}], "form_labels": [], "button_labels": [], "snackbar_messages": [{"text": "Failed to load train numbers: $e", "file": "../lib/widgets/custom_app_bar.dart", "line_number": 193, "category": "snackbar_messages", "suggested_key": "snackbar_failed_to_load"}], "dialog_content": []}, "../lib/widgets/error_modal.dart": {"text_widgets": [{"text": "Close", "file": "../lib/widgets/error_modal.dart", "line_number": 12, "category": "text_widgets", "suggested_key": "text_close"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/widgets/language_selector.dart": {"text_widgets": [{"text": "Language", "file": "../lib/widgets/language_selector.dart", "line_number": 192, "category": "text_widgets", "suggested_key": "text_language"}, {"text": "Select Language", "file": "../lib/widgets/language_selector.dart", "line_number": 201, "category": "text_widgets", "suggested_key": "text_select_language"}, {"text": "Close", "file": "../lib/widgets/language_selector.dart", "line_number": 209, "category": "text_widgets", "suggested_key": "text_close"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}, "../lib/widgets/custom_drawer.dart": {"text_widgets": [{"text": "Train Tracker", "file": "../lib/widgets/custom_drawer.dart", "line_number": 73, "category": "text_widgets", "suggested_key": "text_train_tracker"}, {"text": "Assign <PERSON>", "file": "../lib/widgets/custom_drawer.dart", "line_number": 83, "category": "text_widgets", "suggested_key": "text_assign_ca"}, {"text": "Assign <PERSON>", "file": "../lib/widgets/custom_drawer.dart", "line_number": 93, "category": "text_widgets", "suggested_key": "text_assign_cs"}, {"text": "PNR Details", "file": "../lib/widgets/custom_drawer.dart", "line_number": 103, "category": "text_widgets", "suggested_key": "text_pnr_details"}, {"text": "Rail Sathi", "file": "../lib/widgets/custom_drawer.dart", "line_number": 122, "category": "text_widgets", "suggested_key": "text_rail_sathi"}, {"text": "Passenger Chart", "file": "../lib/widgets/custom_drawer.dart", "line_number": 134, "category": "text_widgets", "suggested_key": "text_passenger_chart"}, {"text": "Passenger Chart", "file": "../lib/widgets/custom_drawer.dart", "line_number": 145, "category": "text_widgets", "suggested_key": "text_passenger_chart"}, {"text": "Map Screen", "file": "../lib/widgets/custom_drawer.dart", "line_number": 157, "category": "text_widgets", "suggested_key": "text_map_screen"}, {"text": "Configuration", "file": "../lib/widgets/custom_drawer.dart", "line_number": 165, "category": "text_widgets", "suggested_key": "text_configuration"}, {"text": "Reports", "file": "../lib/widgets/custom_drawer.dart", "line_number": 173, "category": "text_widgets", "suggested_key": "text_reports"}, {"text": "Passenger Feedback", "file": "../lib/widgets/custom_drawer.dart", "line_number": 182, "category": "text_widgets", "suggested_key": "text_passenger_feedback"}, {"text": "Rake Deficiency Report", "file": "../lib/widgets/custom_drawer.dart", "line_number": 191, "category": "text_widgets", "suggested_key": "text_rake_deficiency_report"}, {"text": "OBHS to MCC Handover", "file": "../lib/widgets/custom_drawer.dart", "line_number": 200, "category": "text_widgets", "suggested_key": "text_obhs_to_mcc"}, {"text": "MCC to OBHS Handover", "file": "../lib/widgets/custom_drawer.dart", "line_number": 210, "category": "text_widgets", "suggested_key": "text_mcc_to_obhs"}, {"text": "Upload data", "file": "../lib/widgets/custom_drawer.dart", "line_number": 220, "category": "text_widgets", "suggested_key": "text_upload_data"}, {"text": "User Management", "file": "../lib/widgets/custom_drawer.dart", "line_number": 230, "category": "text_widgets", "suggested_key": "text_user_management"}, {"text": "Issue Management", "file": "../lib/widgets/custom_drawer.dart", "line_number": 239, "category": "text_widgets", "suggested_key": "text_issue_management"}, {"text": "Rail Sathi Qr", "file": "../lib/widgets/custom_drawer.dart", "line_number": 249, "category": "text_widgets", "suggested_key": "text_rail_sathi_qr"}, {"text": "Customer Care", "file": "../lib/widgets/custom_drawer.dart", "line_number": 259, "category": "text_widgets", "suggested_key": "text_customer_care"}], "app_bar_titles": [], "form_labels": [], "button_labels": [], "snackbar_messages": [], "dialog_content": []}}