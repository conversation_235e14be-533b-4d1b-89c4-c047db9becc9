#!/usr/bin/env dart
import 'dart:convert';
import 'dart:io';
import 'package:csv/csv.dart';

/// <PERSON>ript to inject attendance translations from CSV files into ARB files
/// 
/// This script:
/// 1. Loads the target ARB file
/// 2. Inserts each key/value pair from the reviewed CSV under the "ATTENDANCE – TODO" section
/// 3. Adds corresponding `@` metadata (copies English description & context)
/// 4. Outputs prettified JSON

Future<void> main(List<String> arguments) async {
  if (arguments.isEmpty) {
    print('Usage: dart inject_attendance.dart <locale>');
    print('Example: dart inject_attendance.dart hi');
    exit(1);
  }

  final locale = arguments[0];
  
  try {
    await injectAttendanceTranslations(locale);
    print('✅ Successfully injected attendance translations for locale: $locale');
  } catch (e) {
    print('❌ Error injecting attendance translations for locale $locale: $e');
    exit(1);
  }
}

Future<void> injectAttendanceTranslations(String locale) async {
  // File paths
  final csvPath = 'translations/attendance_$locale.csv';
  final arbPath = 'lib/l10n/app_$locale.arb';
  
  // Check if files exist
  if (!await File(csvPath).exists()) {
    throw Exception('CSV file not found: $csvPath');
  }
  
  if (!await File(arbPath).exists()) {
    throw Exception('ARB file not found: $arbPath');
  }

  // Read CSV file
  final csvContent = await File(csvPath).readAsString();
  final csvData = const CsvToListConverter().convert(csvContent);
  
  if (csvData.isEmpty) {
    throw Exception('CSV file is empty');
  }

  // Parse CSV headers
  final headers = csvData[0].cast<String>();
  final keyIndex = headers.indexOf('key');
  final englishIndex = headers.indexOf('english_text');
  final translatedIndex = headers.indexOf('translated_text');
  final placeholdersIndex = headers.indexOf('placeholders');
  
  if (keyIndex == -1 || englishIndex == -1 || translatedIndex == -1) {
    throw Exception('CSV file missing required columns: key, english_text, translated_text');
  }

  // Extract translations from CSV
  final translations = <String, Map<String, dynamic>>{};
  
  for (int i = 1; i < csvData.length; i++) {
    final row = csvData[i];
    if (row.length <= keyIndex || row.length <= translatedIndex) continue;
    
    final key = row[keyIndex]?.toString().trim() ?? '';
    final englishText = row[englishIndex]?.toString().trim() ?? '';
    final translatedText = row[translatedIndex]?.toString().trim() ?? '';
    final placeholders = placeholdersIndex != -1 && row.length > placeholdersIndex 
        ? row[placeholdersIndex]?.toString().trim() ?? ''
        : '';
    
    if (key.isEmpty || translatedText.isEmpty) continue;
    
    // Create translation entry
    translations[key] = {
      'text': translatedText,
      'englishText': englishText,
      'placeholders': placeholders,
    };
  }

  if (translations.isEmpty) {
    throw Exception('No valid translations found in CSV');
  }

  // Read ARB file
  final arbContent = await File(arbPath).readAsString();
  
  // Remove comments from ARB content before parsing JSON
  final cleanedContent = _removeCommentsFromJson(arbContent);
  final arbData = json.decode(cleanedContent) as Map<String, dynamic>;
  
  // Create new entries for attendance translations
  final newEntries = <String, dynamic>{};
  
  for (final entry in translations.entries) {
    final key = entry.key;
    final translationData = entry.value;
    
    // Add the translation text
    newEntries[key] = translationData['text'];
    
    // Add the metadata
    final metadata = <String, dynamic>{
      'description': 'Attendance: ${translationData['englishText']}',
      'context': 'attendance'
    };
    
    // Add placeholders if they exist
    final placeholders = translationData['placeholders'] as String;
    if (placeholders.isNotEmpty) {
      final placeholdersList = placeholders.split('|').where((p) => p.trim().isNotEmpty).toList();
      if (placeholdersList.isNotEmpty) {
        final placeholdersMap = <String, Map<String, String>>{};
        for (final placeholder in placeholdersList) {
          final cleanPlaceholder = placeholder.trim().replaceAll(RegExp(r'[{}]'), '');
          placeholdersMap[cleanPlaceholder] = {'type': 'text'};
        }
        metadata['placeholders'] = placeholdersMap;
      }
    }
    
    newEntries['@$key'] = metadata;
  }
  
  // Read the original ARB file content to find the ATTENDANCE – TODO section
  final lines = arbContent.split('\n');
  int insertionPoint = -1;
  
  // Look for the ATTENDANCE – TODO comment
  for (int i = 0; i < lines.length; i++) {
    if (lines[i].contains('ATTENDANCE') && lines[i].contains('TODO')) {
      insertionPoint = i;
      break;
    }
  }
  
  // If we found the insertion point, we need to be more careful about JSON structure
  // For now, let's add the attendance translations at the end of the JSON
  final Map<String, dynamic> finalArbData = Map<String, dynamic>.from(arbData);
  
  // Remove any existing attendance translations to avoid duplicates
  final attendanceKeys = newEntries.keys.toList();
  for (final key in attendanceKeys) {
    finalArbData.remove(key);
  }
  
  // Add the new attendance translations
  finalArbData.addAll(newEntries);
  
  // Convert back to JSON with pretty formatting
  final encoder = JsonEncoder.withIndent('  ');
  final prettyJson = encoder.convert(finalArbData);
  
  // Write updated ARB file
  await File(arbPath).writeAsString(prettyJson);
  
  print('📝 Injected ${translations.length} attendance translations into $arbPath');
}

/// Remove JavaScript-style comments from JSON content
String _removeCommentsFromJson(String content) {
  final lines = content.split('\n');
  final cleanedLines = <String>[];
  
  for (final line in lines) {
    // Remove lines that are pure comments
    if (line.trim().startsWith('//')) {
      continue;
    }
    
    // Remove inline comments (but be careful with strings)
    String cleanedLine = line;
    bool inString = false;
    bool escaped = false;
    
    for (int i = 0; i < line.length - 1; i++) {
      if (escaped) {
        escaped = false;
        continue;
      }
      
      if (line[i] == '\\') {
        escaped = true;
        continue;
      }
      
      if (line[i] == '"' && !escaped) {
        inString = !inString;
        continue;
      }
      
      if (!inString && line[i] == '/' && line[i + 1] == '/') {
        cleanedLine = line.substring(0, i).trimRight();
        break;
      }
    }
    
    cleanedLines.add(cleanedLine);
  }
  
  return cleanedLines.join('\n');
}
