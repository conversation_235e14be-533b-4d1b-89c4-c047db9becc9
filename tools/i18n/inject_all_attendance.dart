#!/usr/bin/env dart
import 'dart:io';

/// <PERSON>ch script to inject attendance translations for all locales
Future<void> main() async {
  const locales = ['as', 'bn', 'gu', 'hi', 'kn', 'ml', 'mr', 'pa', 'ta', 'te'];
  
  print('🚀 Starting attendance translation injection for all locales...\n');
  
  int successCount = 0;
  final errors = <String>[];
  
  for (final locale in locales) {
    try {
      print('📝 Processing locale: $locale');
      
      final result = await Process.run(
        'dart',
        ['tools/i18n/inject_attendance.dart', locale],
        workingDirectory: Directory.current.path,
      );
      
      if (result.exitCode == 0) {
        print('✅ Successfully processed $locale');
        successCount++;
      } else {
        print('❌ Failed to process $locale');
        errors.add('$locale: ${result.stderr}');
      }
      
      print('---\n');
    } catch (e) {
      print('❌ Error processing $locale: $e');
      errors.add('$locale: $e');
      print('---\n');
    }
  }
  
  // Summary
  print('📊 SUMMARY:');
  print('✅ Successfully processed: $successCount/${locales.length} locales');
  
  if (errors.isNotEmpty) {
    print('❌ Errors:');
    for (final error in errors) {
      print('   $error');
    }
    exit(1);
  } else {
    print('🎉 All locales processed successfully!');
  }
}
