# Definitive List of Attendance-Related Keys
# Generated from app_en.arb
# Total keys found: 100
# Format: KEY_NAME [placeholders] = "value"

## Main Attendance Keys (attendance_*)
attendance_api_summary = "API Summary Details"
attendance_assigned_coaches = "🚆 Assigned Coaches:"
attendance_available_label [count] = "Available: {count}"
attendance_available_section = "Available"
attendance_berths_count [count] = "{count} berths"
attendance_buffer_time_restriction = "The time between the current time and the train's arrival time is not within the 2-hour buffer.
If you're unable to see the arrival time, please contact the admin or add the arrival time for this train."
attendance_cancel = "Cancel"
attendance_cancelled = "Cancelled"
attendance_chart_not_prepared = "Chart has not been prepared for this station"
attendance_charting_refreshed [time] = "Charting refreshed at: {time}"
attendance_charting_started [time] = "Charting started at: {time}"
attendance_charting_time [time] = "Charting Time: {time}"
attendance_click_more = "click for more..."
attendance_coach_label [coach] = "🚃 Coach: {coach}"
attendance_coach_occupancy = "Coach Occupancy Details"
attendance_coach_type = "Coach Type:"
attendance_coaches [coaches] = "Coaches: {coaches}"
attendance_concise_view = "Concise View"
attendance_count_label [count] = "A: {count}"
attendance_daily = "Daily"
attendance_data_refreshed = "Data refreshed successfully"
attendance_deboarding = "🔴 Deboarding:"
attendance_deboarding_none = "🔴 Deboarding: None"
attendance_detailed_view = "Detailed View"
attendance_ehk_assigned [ehkName] = "EHK Assigned for train:{ehkName}"
attendance_expected_charting [time] = "Expected Charting Time: {time}"
attendance_failed_load_data [error] = "Failed to load detailed data: {error}"
attendance_failed_update_status = "Failed to update train status"
attendance_go = "Go"
attendance_in_route = "In-route"
attendance_inside = "Inside"
attendance_inside_train = "You are now marked as inside the train"
attendance_journey_status_updated [status] = "Journey status updated to {status}"
attendance_last_fetched [time, user] = "Last fetched: {time}
From User: {user}"
attendance_loading = "Loading..."
attendance_location_not_fetched = "Train Location is not fetched yet, please try again later"
attendance_na = "N/A"
attendance_near_stations = "You're near the following station(s):"
attendance_nearby_station_alert = "🛤️ Nearby Station Alert"
attendance_non_sleeper = "Non-Sleeper"
attendance_not_attendance_station [stationCode] = "Attendance cannot be marked for station {stationCode} as it is not an attendance station."
attendance_not_inside_train = "You are not inside the train. Please go inside the train first."
attendance_off_label [count] = "Off: {count}"
attendance_offboarding_section = "Offboarding"
attendance_ok = "OK"
attendance_on_label [count] = "On: {count}"
attendance_onboarding = "🟢 Onboarding:"
attendance_onboarding_none = "🟢 Onboarding: None"
attendance_onboarding_section = "Onboarding"
attendance_other_ca = "Other CA"
attendance_other_ehk = "Other EHK/OBHS"
attendance_outside_train = "You are now marked as outside the train"
attendance_passenger_chart = "Passenger Chart   Atten.."
attendance_refresh_failed [error] = "Refresh failed: {error}"
attendance_screen_title = "Attendance"
attendance_select_date = "Select date"
attendance_select_train_date = "Please select a train number and date."
attendance_select_train_first = "Please select a train first"
attendance_self = "Self"
attendance_sleeper = "Sleeper"
attendance_station_details [stationCode] = "Station Details - {stationCode}"
attendance_stoppages = "Stoppages"
attendance_timings = "Timings"
attendance_today = "Today"
attendance_too_far_from_station [stationCode, nearbyStations] = "You're over 50 KM away from the selected station {stationCode}. Attendance can only be marked when you're within the allowed range.
For Now You can only mark attendance for stations: {nearbyStations}"
attendance_train = "Train"
attendance_train_date [trainNo, date] = "Train {trainNo} - {date}"
attendance_train_depot [depot] = "Train Depot:{depot}"
attendance_train_not_running = "Train Not Running"
attendance_train_not_running_message [runningDays, dayOfWeek, trainNumber] = "Train {trainNumber} is NOT running on {dayOfWeek}
Running Days: {runningDays}"
attendance_update = "Update"
attendance_update_journey_status = "Update Journey Status"
attendance_update_message = "A new version of the app is available. You must update to continue using the app."
attendance_update_now = "Update Now"
attendance_update_required = "Update Required"
attendance_user_location [locationStatus] = "User Location:{locationStatus}"

## Attendance Details Keys (attendance_details_*)
attendance_details_distance [distance] = "Distance: {distance} km"
attendance_details_error [error] = "Error: {error}"
attendance_details_journey_date = "Journey Date:"
attendance_details_latitude [latitude] = "latitude: {latitude}"
attendance_details_longitude [longitude] = "longitude: {longitude}"
attendance_details_match_percentage [percentage] = "Match Percentage: {percentage}"
attendance_details_nearest_station [station] = "nearest Station: {station}"
attendance_details_no_attendance = "No attendance found."
attendance_details_no_data = "No data available."
attendance_details_no_human_detected = "No human detected"
attendance_details_station_code = "Station Code:"
attendance_details_status [status] = "Status: {status}"
attendance_details_status_marked = "Marked"
attendance_details_status_pending = "Pending"
attendance_details_title = "Attendance Details"
attendance_details_train_number = "Train Number:"
attendance_details_updated = "All details updated successfully."
attendance_details_updated_at [updatedAt] = "Updated At: {updatedAt}"
attendance_details_updated_by [updatedBy] = "Updated By: {updatedBy}"
attendance_details_username [username] = "Username: {username}"

## Other Attendance-Related Keys
btn_no_attendance_found = "No attendance found."
text_attendance_already_submitted = "Attendance Already Submitted"
text_attendance_marked_successfully = "Attendance marked successfully!"
text_no_attendance_found = "No attendance found."

## Simple Key List (for quick reference)
attendance_api_summary
attendance_assigned_coaches
attendance_available_label
attendance_available_section
attendance_berths_count
attendance_buffer_time_restriction
attendance_cancel
attendance_cancelled
attendance_chart_not_prepared
attendance_charting_refreshed
attendance_charting_started
attendance_charting_time
attendance_click_more
attendance_coach_label
attendance_coach_occupancy
attendance_coach_type
attendance_coaches
attendance_concise_view
attendance_count_label
attendance_daily
attendance_data_refreshed
attendance_deboarding
attendance_deboarding_none
attendance_detailed_view
attendance_details_distance
attendance_details_error
attendance_details_journey_date
attendance_details_latitude
attendance_details_longitude
attendance_details_match_percentage
attendance_details_nearest_station
attendance_details_no_attendance
attendance_details_no_data
attendance_details_no_human_detected
attendance_details_station_code
attendance_details_status
attendance_details_status_marked
attendance_details_status_pending
attendance_details_title
attendance_details_train_number
attendance_details_updated
attendance_details_updated_at
attendance_details_updated_by
attendance_details_username
attendance_ehk_assigned
attendance_expected_charting
attendance_failed_load_data
attendance_failed_update_status
attendance_go
attendance_in_route
attendance_inside
attendance_inside_train
attendance_journey_status_updated
attendance_last_fetched
attendance_loading
attendance_location_not_fetched
attendance_na
attendance_near_stations
attendance_nearby_station_alert
attendance_non_sleeper
attendance_not_attendance_station
attendance_not_inside_train
attendance_off_label
attendance_offboarding_section
attendance_ok
attendance_on_label
attendance_onboarding
attendance_onboarding_none
attendance_onboarding_section
attendance_other_ca
attendance_other_ehk
attendance_outside_train
attendance_passenger_chart
attendance_refresh_failed
attendance_screen_title
attendance_select_date
attendance_select_train_date
attendance_select_train_first
attendance_self
attendance_sleeper
attendance_station_details
attendance_stoppages
attendance_timings
attendance_today
attendance_too_far_from_station
attendance_train
attendance_train_date
attendance_train_depot
attendance_train_not_running
attendance_train_not_running_message
attendance_update
attendance_update_journey_status
attendance_update_message
attendance_update_now
attendance_update_required
attendance_user_location
btn_no_attendance_found
text_attendance_already_submitted
text_attendance_marked_successfully
text_no_attendance_found
