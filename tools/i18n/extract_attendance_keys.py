#!/usr/bin/env python3
"""
Extract all attendance-related keys from app_en.arb file.
This script finds all keys that begin with 'attendance_' or 'attendanceDetails_',
as well as any other keys containing 'attendance' in their names.
"""

import json
import re
from pathlib import Path

def extract_attendance_keys():
    """Extract all attendance-related keys from the ARB file."""
    arb_file = Path("lib/l10n/app_en.arb")
    
    if not arb_file.exists():
        print(f"Error: {arb_file} not found!")
        return []
    
    # Read and parse the ARB file
    with open(arb_file, 'r', encoding='utf-8') as f:
        try:
            arb_data = json.load(f)
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON: {e}")
            return []
    
    attendance_keys = []
    
    # Extract keys that start with 'attendance_' or 'attendanceDetails_'
    # Also include keys that contain 'attendance' anywhere in the name
    for key, value in arb_data.items():
        # Skip metadata keys (start with @)
        if key.startswith('@'):
            continue
            
        # Check if key starts with our target prefixes or contains 'attendance'
        if (key.startswith('attendance_') or 
            key.startswith('attendanceDetails_') or 
            'attendance' in key.lower()):
            
            # Extract placeholders if this is a text value
            placeholders = []
            if isinstance(value, str):
                # Find placeholders in format {placeholder}
                placeholder_matches = re.findall(r'\{([^}]+)\}', value)
                placeholders = placeholder_matches
            
            # Check if there are nested placeholders in metadata
            metadata_key = f"@{key}"
            if metadata_key in arb_data:
                metadata = arb_data[metadata_key]
                if isinstance(metadata, dict) and 'placeholders' in metadata:
                    nested_placeholders = list(metadata['placeholders'].keys())
                    # Combine and deduplicate placeholders
                    all_placeholders = list(set(placeholders + nested_placeholders))
                    placeholders = all_placeholders
            
            attendance_keys.append({
                'key': key,
                'value': value,
                'placeholders': placeholders
            })
    
    # Sort keys alphabetically
    attendance_keys.sort(key=lambda x: x['key'])
    
    return attendance_keys

def save_attendance_keys(keys):
    """Save the attendance keys to the output file."""
    output_file = Path("tools/i18n/attendance_keys.txt")
    
    # Ensure the directory exists
    output_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("# Definitive List of Attendance-Related Keys\n")
        f.write("# Generated from app_en.arb\n")
        f.write(f"# Total keys found: {len(keys)}\n")
        f.write("# Format: KEY_NAME [placeholders] = \"value\"\n\n")
        
        # Group keys by prefix for better organization
        attendance_main = []
        attendance_details = []
        other_attendance = []
        
        for key_info in keys:
            key = key_info['key']
            if key.startswith('attendance_details_'):
                attendance_details.append(key_info)
            elif key.startswith('attendance_'):
                attendance_main.append(key_info)
            else:
                other_attendance.append(key_info)
        
        # Write main attendance keys
        if attendance_main:
            f.write("## Main Attendance Keys (attendance_*)\n")
            for key_info in attendance_main:
                placeholders_str = f" [{', '.join(key_info['placeholders'])}]" if key_info['placeholders'] else ""
                f.write(f"{key_info['key']}{placeholders_str} = \"{key_info['value']}\"\n")
            f.write("\n")
        
        # Write attendance details keys
        if attendance_details:
            f.write("## Attendance Details Keys (attendance_details_*)\n")
            for key_info in attendance_details:
                placeholders_str = f" [{', '.join(key_info['placeholders'])}]" if key_info['placeholders'] else ""
                f.write(f"{key_info['key']}{placeholders_str} = \"{key_info['value']}\"\n")
            f.write("\n")
        
        # Write other attendance-related keys
        if other_attendance:
            f.write("## Other Attendance-Related Keys\n")
            for key_info in other_attendance:
                placeholders_str = f" [{', '.join(key_info['placeholders'])}]" if key_info['placeholders'] else ""
                f.write(f"{key_info['key']}{placeholders_str} = \"{key_info['value']}\"\n")
            f.write("\n")
        
        # Write a simple list at the end for easy reference
        f.write("## Simple Key List (for quick reference)\n")
        for key_info in keys:
            f.write(f"{key_info['key']}\n")

def main():
    """Main function to extract and save attendance keys."""
    print("Extracting attendance-related keys from app_en.arb...")
    
    keys = extract_attendance_keys()
    
    if not keys:
        print("No attendance keys found!")
        return
    
    print(f"Found {len(keys)} attendance-related keys")
    
    save_attendance_keys(keys)
    
    print("Attendance keys saved to tools/i18n/attendance_keys.txt")
    
    # Print summary
    main_keys = [k for k in keys if k['key'].startswith('attendance_') and not k['key'].startswith('attendance_details_')]
    details_keys = [k for k in keys if k['key'].startswith('attendance_details_')]
    other_keys = [k for k in keys if not k['key'].startswith('attendance_')]
    
    print(f"\nSummary:")
    print(f"- Main attendance keys (attendance_*): {len(main_keys)}")
    print(f"- Attendance details keys (attendance_details_*): {len(details_keys)}")
    print(f"- Other attendance-related keys: {len(other_keys)}")

if __name__ == "__main__":
    main()
