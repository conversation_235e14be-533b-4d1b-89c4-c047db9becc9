#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive Attendance Keys Translation Script

This script translates attendance-related keys from English to multiple Indian languages
following railway domain conventions and preserving all placeholders.
"""

import csv
import re
import os
from typing import Dict, List, Tuple

# Supported locales
LOCALES = ['hi', 'bn', 'gu', 'as', 'kn', 'ml', 'mr', 'pa', 'ta', 'te']

# Railway domain terms that should be transliterated (not translated)
RAILWAY_TERMS = {
    'train': {'hi': 'ट्रेन', 'bn': 'ট্রেন', 'gu': 'ટ્રેન', 'as': 'ট্ৰেইন', 'kn': 'ಟ್ರೈನ್', 'ml': 'ട്രെയിൻ', 'mr': 'ट्रेन', 'pa': 'ਟਰੇਨ', 'ta': 'ரயில்', 'te': 'రైలు'},
    'coach': {'hi': 'कोच', 'bn': 'কোচ', 'gu': 'કોચ', 'as': 'কোচ', 'kn': 'ಕೋಚ್', 'ml': 'കോച്ച്', 'mr': 'कोच', 'pa': 'ਕੋਚ', 'ta': 'பெட்டி', 'te': 'కోచ్'},
    'berth': {'hi': 'बर्थ', 'bn': 'বার্থ', 'gu': 'બર્થ', 'as': 'বাৰ্থ', 'kn': 'ಬರ್ತ್', 'ml': 'ബര്‍ത്ത്', 'mr': 'बर्थ', 'pa': 'ਬਰਥ', 'ta': 'படுக்கை', 'te': 'బెర్త్'},
    'chart': {'hi': 'चार्ट', 'bn': 'চার্ট', 'gu': 'ચાર્ટ', 'as': 'চার্ট', 'kn': 'ಚಾರ್ಟ್', 'ml': 'ചാര്‍ട്ട്', 'mr': 'चार्ट', 'pa': 'ਚਾਰਟ', 'ta': 'பட்டியல்', 'te': 'చార్ట్'},
    'charting': {'hi': 'चार्टिंग', 'bn': 'চার্টিং', 'gu': 'ચાર્ટિંગ', 'as': 'চার্টিং', 'kn': 'ಚಾರ್ಟಿಂಗ್', 'ml': 'ചാര്‍ട്ടിംഗ്', 'mr': 'चार्टिंग', 'pa': 'ਚਾਰਟਿੰਗ', 'ta': 'பட்டியலிடல்', 'te': 'చార్టింగ్'},
    'station': {'hi': 'स्टेशन', 'bn': 'স্টেশন', 'gu': 'સ્ટેશન', 'as': 'ষ্টেচন', 'kn': 'ನಿಲ್ದಾಣ', 'ml': 'സ്റ്റേഷൻ', 'mr': 'स्टेशन', 'pa': 'ਸਟੇਸ਼ਨ', 'ta': 'நிலையம்', 'te': 'స్టేషన్'},
    'depot': {'hi': 'डिपो', 'bn': 'ডিপো', 'gu': 'ડિપો', 'as': 'ডিপো', 'kn': 'ಡಿಪೋ', 'ml': 'ഡിപ്പോ', 'mr': 'डिपो', 'pa': 'ਡਿਪੋ', 'ta': 'கிடங்கு', 'te': 'డిపో'}
}

# Common UI translations
UI_TRANSLATIONS = {
    'cancel': {'hi': 'रद्द करें', 'bn': 'বাতিল', 'gu': 'રદ કરો', 'as': 'বাতিল কৰক', 'kn': 'ರದ್ದುಮಾಡಿ', 'ml': 'റദ്ദാക്കുക', 'mr': 'रद्द करा', 'pa': 'ਰੱਦ ਕਰੋ', 'ta': 'ரத்துசெய்', 'te': 'రద్దుచేయి'},
    'update': {'hi': 'अपडेट करें', 'bn': 'আপডেট', 'gu': 'અપડેટ કરો', 'as': 'আপডেট কৰক', 'kn': 'ನವೀಕರಿಸಿ', 'ml': 'അപ്ഡേറ്റ് ചെയ്യുക', 'mr': 'अपडेट करा', 'pa': 'ਅਪਡੇਟ ਕਰੋ', 'ta': 'புதுப்பிக்கவும்', 'te': 'నవీకరించు'},
    'loading': {'hi': 'लोड हो रहा है', 'bn': 'লোড হচ্ছে', 'gu': 'લોડ થઈ રહ્યું છે', 'as': 'লোড হৈ আছে', 'kn': 'ಲೋಡ್ ಆಗುತ್ತಿದೆ', 'ml': 'ലോഡ് ചെയ്യുന്നു', 'mr': 'लोड होत आहे', 'pa': 'ਲੋਡ ਹੋ ਰਿਹਾ ਹੈ', 'ta': 'ஏற்றப்படுகிறது', 'te': 'లోడ్ అవుతోంది'},
    'error': {'hi': 'त्रुटि', 'bn': 'ত্রুটি', 'gu': 'ભૂલ', 'as': 'ভুল', 'kn': 'ದೋಷ', 'ml': 'പിശക്', 'mr': 'त्रुटी', 'pa': 'ਗਲਤੀ', 'ta': 'பிழை', 'te': 'దోషం'},
    'status': {'hi': 'स्थिति', 'bn': 'অবস্থা', 'gu': 'સ્થિતિ', 'as': 'অৱস্থা', 'kn': 'ಸ್ಥಿತಿ', 'ml': 'നില', 'mr': 'स्थिती', 'pa': 'ਸਥਿਤੀ', 'ta': 'நிலை', 'te': 'స్థితి'},
    'time': {'hi': 'समय', 'bn': 'সময়', 'gu': 'સમય', 'as': 'সময়', 'kn': 'ಸಮಯ', 'ml': 'സമയം', 'mr': 'वेळ', 'pa': 'ਸਮਾਂ', 'ta': 'நேரம்', 'te': 'సమయం'},
    'date': {'hi': 'तारीख', 'bn': 'তারিখ', 'gu': 'તારીખ', 'as': 'তাৰিখ', 'kn': 'ದಿನಾಂಕ', 'ml': 'തീയതി', 'mr': 'तारीख', 'pa': 'ਤਾਰੀਖ', 'ta': 'தேதி', 'te': 'తేదీ'},
    'ok': {'hi': 'ठीक', 'bn': 'ঠিক আছে', 'gu': 'બરાબર', 'as': 'ঠিক আছে', 'kn': 'ಸರಿ', 'ml': 'ശരി', 'mr': 'ठीक', 'pa': 'ਠੀਕ', 'ta': 'சரி', 'te': 'సరే'},
    'go': {'hi': 'जाएं', 'bn': 'যান', 'gu': 'જાઓ', 'as': 'যাওক', 'kn': 'ಹೋಗಿ', 'ml': 'പോകുക', 'mr': 'जा', 'pa': 'ਜਾਓ', 'ta': 'செல்லவும்', 'te': 'వెళ్లు'},
    'refresh': {'hi': 'रिफ्रेश', 'bn': 'রিফ্রেশ', 'gu': 'રિફ્રેશ', 'as': 'ৰিফ্ৰেছ', 'kn': 'ರಿಫ್ರೆಶ್', 'ml': 'റിഫ്രഷ്', 'mr': 'रिफ्रेश', 'pa': 'ਰਿਫਰੈਸ਼', 'ta': 'புதுப்பிப்பு', 'te': 'రిఫ్రెష్'},
    'failed': {'hi': 'असफल', 'bn': 'ব্যর্থ', 'gu': 'નિષ્ફળ', 'as': 'বিফল', 'kn': 'ವಿಫಲ', 'ml': 'പരാജയപ്പെട്ടു', 'mr': 'अयशस्वी', 'pa': 'ਅਸਫਲ', 'ta': 'தோல்வி', 'te': 'విఫలమైంది'},
    'successfully': {'hi': 'सफलतापूर्वक', 'bn': 'সফলভাবে', 'gu': 'સફળતાપૂર્વક', 'as': 'সফলভাবে', 'kn': 'ಯಶಸ್ವಿಯಾಗಿ', 'ml': 'വിജയകരമായി', 'mr': 'यशस्वीरित्या', 'pa': 'ਸਫਲਤਾਪੂਰਵਕ', 'ta': 'வெற்றிகரமாக', 'te': 'విజయవంతంగా'}
}

def parse_attendance_key_line(line: str) -> Tuple[str, str, List[str]]:
    """Parse a line from attendance_keys.txt and extract key, placeholders, and English text."""
    line = line.strip()
    if not line or line.startswith('#') or '=' not in line:
        return None, None, []
    
    # Split on the first '=' to handle values with '=' in them
    parts = line.split('=', 1)
    left_part = parts[0].strip()
    english_text = parts[1].strip().strip('"')
    
    # Extract key and placeholders
    if '[' in left_part and ']' in left_part:
        key = left_part.split('[')[0].strip()
        placeholder_part = left_part.split('[')[1].split(']')[0]
        placeholders = [p.strip() for p in placeholder_part.split(',')]
    else:
        key = left_part
        placeholders = []
    
    # Find placeholders in the text using regex
    text_placeholders = re.findall(r'\{[^}]+\}|\$[a-zA-Z_][a-zA-Z0-9_]*', english_text)
    
    return key, english_text, text_placeholders

def translate_text(text: str, locale: str) -> str:
    """Translate English text to target locale following railway domain conventions."""
    if not text:
        return text
    
    # Preserve placeholders - keep them exactly as they are
    placeholders = re.findall(r'\{[^}]+\}|\$[a-zA-Z_][a-zA-Z0-9_]*', text)
    
    # Replace placeholders temporarily with unique markers
    temp_text = text
    placeholder_map = {}
    for i, placeholder in enumerate(placeholders):
        temp_key = f"PLACEHOLDER_MARKER_{i}_MARKER"
        placeholder_map[temp_key] = placeholder
        temp_text = temp_text.replace(placeholder, temp_key)
    
    # Start with original case preservation for better translation
    translated_text = temp_text
    
    # Railway terms (transliteration) - preserve case context
    for term, translations in RAILWAY_TERMS.items():
        if locale in translations:
            # Case-sensitive replacement to maintain context
            translated_text = re.sub(r'\b' + re.escape(term.title()) + r'\b', translations[locale], translated_text)
            translated_text = re.sub(r'\b' + re.escape(term.lower()) + r'\b', translations[locale], translated_text)
            translated_text = re.sub(r'\b' + re.escape(term.upper()) + r'\b', translations[locale], translated_text)
    
    # UI terms (translation) - preserve case context
    for term, translations in UI_TRANSLATIONS.items():
        if locale in translations:
            translated_text = re.sub(r'\b' + re.escape(term.title()) + r'\b', translations[locale], translated_text)
            translated_text = re.sub(r'\b' + re.escape(term.lower()) + r'\b', translations[locale], translated_text)
            translated_text = re.sub(r'\b' + re.escape(term.upper()) + r'\b', translations[locale], translated_text)
    
    # Handle specific attendance patterns
    attendance_patterns = {
        'attendance': {
            'hi': 'उपस्थिति', 'bn': 'উপস্থিতি', 'gu': 'હાજરી', 'as': 'উপস্থিতি',
            'kn': 'ಹಾಜರಾತಿ', 'ml': 'ഹാജർ', 'mr': 'उपस्थिति', 'pa': 'ਹਾਜ਼ਰੀ',
            'ta': 'வருகை', 'te': 'హాజరు'
        },
        'onboarding': {
            'hi': 'चढ़ना', 'bn': 'উঠা', 'gu': 'ચઢવું', 'as': 'উঠা',
            'kn': 'ಹತ್ತುವುದು', 'ml': 'കയറുക', 'mr': 'चढणे', 'pa': 'ਚੜ੍ਹਨਾ',
            'ta': 'ஏறுதல்', 'te': 'ఎక్కడం'
        },
        'deboarding': {
            'hi': 'उतरना', 'bn': 'নামা', 'gu': 'ઉતરવું', 'as': 'নমা',
            'kn': 'ಇಳಿಯುವುದು', 'ml': 'ഇറങ്ങുക', 'mr': 'उतरणे', 'pa': 'ਉਤਰਨਾ',
            'ta': 'இறங்குதல்', 'te': 'దిగడం'
        },
        'inside': {
            'hi': 'अंदर', 'bn': 'ভিতরে', 'gu': 'અંદર', 'as': 'ভিতৰত',
            'kn': 'ಒಳಗೆ', 'ml': 'അകത്ത്', 'mr': 'आत', 'pa': 'ਅੰਦਰ',
            'ta': 'உள்ளே', 'te': 'లోపల'
        },
        'outside': {
            'hi': 'बाहर', 'bn': 'বাইরে', 'gu': 'બહાર', 'as': 'বাহিৰত',
            'kn': 'ಹೊರಗೆ', 'ml': 'പുറത്ത്', 'mr': 'बाहेर', 'pa': 'ਬਾਹਰ',
            'ta': 'வெளியே', 'te': 'బయట'
        },
        'journey': {
            'hi': 'यात्रा', 'bn': 'যাত্রা', 'gu': 'પ્રવાસ', 'as': 'যাত্ৰা',
            'kn': 'ಪ್ರಯಾಣ', 'ml': 'യാത്ര', 'mr': 'प्रवास', 'pa': 'ਸਫ਼ਰ',
            'ta': 'பயணம்', 'te': 'ప్రయాణం'
        },
        'passenger': {
            'hi': 'यात्री', 'bn': 'যাত্রী', 'gu': 'મુસાફર', 'as': 'যাত্ৰী',
            'kn': 'ಪ್ರಯಾಣಿಕ', 'ml': 'യാത്രക്കാരൻ', 'mr': 'प्रवासी', 'pa': 'ਮੁਸਾਫਿਰ',
            'ta': 'பயணி', 'te': 'ప్రయాణికుడు'
        },
        'details': {
            'hi': 'विवरण', 'bn': 'বিস্তারিত', 'gu': 'વિગતો', 'as': 'বিৱৰণ',
            'kn': 'ವಿವರಗಳು', 'ml': 'വിശദാംശങ്ങൾ', 'mr': 'तपशील', 'pa': 'ਵੇਰਵੇ',
            'ta': 'விவரங்கள்', 'te': 'వివరాలు'
        },
        'location': {
            'hi': 'स्थान', 'bn': 'অবস্থান', 'gu': 'સ્થાન', 'as': 'অৱস্থান',
            'kn': 'ಸ್ಥಳ', 'ml': 'സ്ഥലം', 'mr': 'ठिकाण', 'pa': 'ਸਥਾਨ',
            'ta': 'இடம்', 'te': 'స్థానం'
        },
        'data': {
            'hi': 'डेटा', 'bn': 'ডেটা', 'gu': 'ડેટા', 'as': 'ডেটা',
            'kn': 'ಡೇಟಾ', 'ml': 'ഡാറ്റ', 'mr': 'डेटा', 'pa': 'ਡਾਟਾ',
            'ta': 'தரவு', 'te': 'డేటా'
        },
        'alert': {
            'hi': 'चेतावनी', 'bn': 'সতর্কতা', 'gu': 'ચેતવણી', 'as': 'সতৰ্কবাণী',
            'kn': 'ಎಚ್ಚರಿಕೆ', 'ml': 'മുന്നറിയിപ്പ്', 'mr': 'सावधान', 'pa': 'ਚੇਤਾਵਨੀ',
            'ta': 'எச்சரிக்கை', 'te': 'హెచ్చరిక'
        },
        'user': {
            'hi': 'उपयोगकर्ता', 'bn': 'ব্যবহারকারী', 'gu': 'વપરાશકર્તા', 'as': 'ব্যৱহাৰকাৰী',
            'kn': 'ಬಳಕೆದಾರ', 'ml': 'ഉപയോക്താവ്', 'mr': 'वापरकर्ता', 'pa': 'ਵਰਤੋਂਕਾਰ',
            'ta': 'பயனர்', 'te': 'వినియోగదారు'
        },
        'view': {
            'hi': 'दृश्य', 'bn': 'দেখুন', 'gu': 'જુઓ', 'as': 'দৃশ্য',
            'kn': 'ವೀಕ್ಷಣೆ', 'ml': 'കാണുക', 'mr': 'पहा', 'pa': 'ਵੇਖੋ',
            'ta': 'காண்க', 'te': 'చూడండి'
        },
        'select': {
            'hi': 'चुनें', 'bn': 'নির্বাচন করুন', 'gu': 'પસંદ કરો', 'as': 'নিৰ্বাচন কৰক',
            'kn': 'ಆಯ್ಕೆಮಾಡಿ', 'ml': 'തിരഞ്ഞെടുക്കുക', 'mr': 'निवडा', 'pa': 'ਚੁਣੋ',
            'ta': 'தேர்ந்தெடுக்கவும்', 'te': 'ఎంచుకోండి'
        },
        'assigned': {
            'hi': 'आवंटित', 'bn': 'বরাদ্দ', 'gu': 'સોંપેલ', 'as': 'বৰাদ্দ',
            'kn': 'ನಿಯೋಜಿಸಲಾಗಿದೆ', 'ml': 'അസൈൻ ചെയ്തു', 'mr': 'नियुक्त', 'pa': 'ਨਿਰਧਾਰਿਤ',
            'ta': 'ஒதுக்கப்பட்டது', 'te': 'కేటాయించబడింది'
        },
        'marked': {
            'hi': 'चिह्नित', 'bn': 'চিহ্নিত', 'gu': 'ચિહ્નિત', 'as': 'চিহ্নিত',
            'kn': 'ಗುರುತಿಸಲಾಗಿದೆ', 'ml': 'അടയാളപ്പെടുത്തി', 'mr': 'चिन्हांकित', 'pa': 'ਮਾਰਕ ਕੀਤਾ',
            'ta': 'குறிக்கப்பட்டது', 'te': 'గుర్తించబడింది'
        },
        'nearby': {
            'hi': 'नजदीकी', 'bn': 'কাছাকাছি', 'gu': 'નજીકના', 'as': 'ওচৰৰ',
            'kn': 'ಹತ್ತಿರದ', 'ml': 'അടുത്തുള്ള', 'mr': 'जवळचे', 'pa': 'ਨੇੜਲੇ',
            'ta': 'அருகிலுள்ள', 'te': 'సమీపంలోని'
        },
        'summary': {
            'hi': 'सारांश', 'bn': 'সারসংক্ষেপ', 'gu': 'સારાંશ', 'as': 'সাৰাংশ',
            'kn': 'ಸಾರಾಂಶ', 'ml': 'സംഗ്രഹം', 'mr': 'सारांश', 'pa': 'ਸਾਰ',
            'ta': 'சுருக்கம்', 'te': 'సారాంశం'
        },
        'available': {
            'hi': 'उपलब्ध', 'bn': 'উপলব্ধ', 'gu': 'ઉપલબ્ધ', 'as': 'উপলব্ধ',
            'kn': 'ಲಭ್ಯ', 'ml': 'ലഭ്യമാണ്', 'mr': 'उपलब्ध', 'pa': 'ਉਪਲਬਧ',
            'ta': 'கிடைக்கிறது', 'te': 'అందుబాటులో'
        }
    }
    
    for pattern, translations in attendance_patterns.items():
        if locale in translations:
            # Case-sensitive replacement
            translated_text = re.sub(r'\b' + re.escape(pattern.title()) + r'\b', translations[locale], translated_text)
            translated_text = re.sub(r'\b' + re.escape(pattern.lower()) + r'\b', translations[locale], translated_text)
            translated_text = re.sub(r'\b' + re.escape(pattern.upper()) + r'\b', translations[locale], translated_text)
    
    # Additional specific patterns for better context
    specific_patterns = {
        'please': {
            'hi': 'कृपया', 'bn': 'অনুগ্রহ করে', 'gu': 'કૃપા કરીને', 'as': 'অনুগ্ৰহ কৰি',
            'kn': 'ದಯವಿಟ್ಟು', 'ml': 'ദയവായി', 'mr': 'कृपया', 'pa': 'ਮਿਹਰਬਾਨੀ ਕਰਕੇ',
            'ta': 'தயவுசெய்து', 'te': 'దయచేసి'
        },
        'required': {
            'hi': 'आवश्यक', 'bn': 'প্রয়োজনীয়', 'gu': 'જરૂરી', 'as': 'প্ৰয়োজনীয়',
            'kn': 'ಅಗತ್ಯವಿದೆ', 'ml': 'ആവശ്യമാണ്', 'mr': 'आवश्यक', 'pa': 'ਲੋੜੀਂਦਾ',
            'ta': 'தேவை', 'te': 'అవసరం'
        },
        'message': {
            'hi': 'संदेश', 'bn': 'বার্তা', 'gu': 'સંદેશ', 'as': 'বাৰ্তা',
            'kn': 'ಸಂದೇಶ', 'ml': 'സന്ദേശം', 'mr': 'संदेश', 'pa': 'ਸੁਨੇਹਾ',
            'ta': 'செய்தி', 'te': 'సందేశం'
        },
        'version': {
            'hi': 'संस्करण', 'bn': 'সংস্করণ', 'gu': 'આવૃત્તિ', 'as': 'সংস্কৰণ',
            'kn': 'ಆವೃತ್ತಿ', 'ml': 'പതിപ്പ്', 'mr': 'आवृत्ती', 'pa': 'ਵਰਜ਼ਨ',
            'ta': 'பதிப்பு', 'te': 'వెర్షన్'
        },
        'number': {
            'hi': 'संख्या', 'bn': 'নম্বর', 'gu': 'નંબર', 'as': 'নম্বৰ',
            'kn': 'ಸಂಖ್ಯೆ', 'ml': 'നമ്പർ', 'mr': 'क्रमांक', 'pa': 'ਨੰਬਰ',
            'ta': 'எண்', 'te': 'సంఖ్య'
        },
        'percentage': {
            'hi': 'प्रतिशत', 'bn': 'শতাংশ', 'gu': 'ટકાવારી', 'as': 'শতাংশ',
            'kn': 'ಶೇಕಡಾ', 'ml': 'ശതമാനം', 'mr': 'टक्केवारी', 'pa': 'ਪ੍ਰਤੀਸ਼ਤ',
            'ta': 'சதவீதம்', 'te': 'శాతం'
        },
        'distance': {
            'hi': 'दूरी', 'bn': 'দূরত্ব', 'gu': 'અંતર', 'as': 'দূৰত্ব',
            'kn': 'ಅಂತರ', 'ml': 'ദൂരം', 'mr': 'अंतर', 'pa': 'ਦੂਰੀ',
            'ta': 'தூரம்', 'te': 'దూరం'
        }
    }
    
    for pattern, translations in specific_patterns.items():
        if locale in translations:
            translated_text = re.sub(r'\b' + re.escape(pattern.title()) + r'\b', translations[locale], translated_text)
            translated_text = re.sub(r'\b' + re.escape(pattern.lower()) + r'\b', translations[locale], translated_text)
            translated_text = re.sub(r'\b' + re.escape(pattern.upper()) + r'\b', translations[locale], translated_text)
    
    # Restore placeholders exactly as they were
    for temp_key, original_placeholder in placeholder_map.items():
        translated_text = translated_text.replace(temp_key, original_placeholder)
    
    return translated_text

def generate_attendance_translations():
    """Generate high-quality translations for all attendance keys across all supported locales."""
    
    # Ensure translations directory exists
    os.makedirs('translations', exist_ok=True)
    
    # Read attendance keys
    attendance_keys = []
    
    with open('tools/i18n/attendance_keys.txt', 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            key, english_text, placeholders = parse_attendance_key_line(line)
            if key and english_text:
                attendance_keys.append({
                    'line_num': line_num,
                    'key': key,
                    'english_text': english_text,
                    'placeholders': placeholders
                })
    
    print(f"Parsed {len(attendance_keys)} attendance keys")
    
    # Generate translations for each locale
    for locale in LOCALES:
        csv_file = f'translations/attendance_{locale}.csv'
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['key', 'english_text', 'translated_text', 'placeholders'])
            
            for entry in attendance_keys:
                key = entry['key']
                english_text = entry['english_text']
                placeholders = entry['placeholders']
                
                # Generate translation
                translated_text = translate_text(english_text, locale)
                
                # Write to CSV
                writer.writerow([
                    key,
                    english_text,
                    translated_text,
                    '|'.join(placeholders) if placeholders else ''
                ])
        
        print(f"Generated translations for {locale}: {csv_file}")
    
    print(f"\nTranslation complete! Generated CSV files for {len(LOCALES)} locales.")
    print("Files created in translations/ directory:")
    for locale in LOCALES:
        print(f"  - attendance_{locale}.csv")

if __name__ == "__main__":
    generate_attendance_translations()
