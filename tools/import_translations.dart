#!/usr/bin/env dart

import 'dart:convert';
import 'dart:io';
import 'package:csv/csv.dart';
import 'package:excel/excel.dart';
import 'package:path/path.dart' as path;

/// ANSI color codes for console output
class Colors {
  static const String reset = '\x1B[0m';
  static const String red = '\x1B[31m';
  static const String green = '\x1B[32m';
  static const String yellow = '\x1B[33m';
  static const String blue = '\x1B[34m';
  static const String magenta = '\x1B[35m';
  static const String cyan = '\x1B[36m';
  static const String bold = '\x1B[1m';
  static const String dim = '\x1B[2m';
}

/// Configuration for the import operation
class ImportConfig {
  final String inputFile;
  final String outputDir;
  final bool dryRun;
  final bool backup;
  final bool verbose;

  ImportConfig({
    required this.inputFile,
    this.outputDir = 'lib/l10n',
    this.dryRun = false,
    this.backup = false,
    this.verbose = false,
  });
}

/// Represents a translation entry with metadata
class TranslationEntry {
  final String key;
  final String value;
  final String? context;
  final String? description;

  TranslationEntry({
    required this.key,
    required this.value,
    this.context,
    this.description,
  });

  @override
  String toString() {
    return 'TranslationEntry(key: $key, value: $value, context: $context)';
  }
}

/// Manages ARB file operations
class ArbFile {
  final String locale;
  final String filePath;
  final Map<String, TranslationEntry> entries = {};
  final Map<String, dynamic> _originalContent = {};
  bool _exists = false;

  ArbFile({required this.locale, required this.filePath});

  /// Load existing ARB file if it exists
  Future<void> load() async {
    final file = File(filePath);
    if (await file.exists()) {
      _exists = true;
      try {
        final content = await file.readAsString();
        _originalContent.clear();
        _originalContent.addAll(json.decode(content) as Map<String, dynamic>);
        
        // Extract existing translations
        for (final entry in _originalContent.entries) {
          final key = entry.key;
          if (key == '@@locale' || key.startsWith('@')) continue;
          
          final value = entry.value;
          if (value is! String) continue;
          
          // Get metadata
          final metadataKey = '@$key';
          final metadata = _originalContent[metadataKey] as Map<String, dynamic>?;
          
          entries[key] = TranslationEntry(
            key: key,
            value: value,
            context: metadata?['context'] as String?,
            description: metadata?['description'] as String?,
          );
        }
      } catch (e) {
        throw Exception('Failed to parse existing ARB file $filePath: $e');
      }
    }
  }

  /// Add or update a translation entry
  void addEntry(TranslationEntry entry) {
    entries[entry.key] = entry;
  }

  /// Generate ARB file content
  Map<String, dynamic> generateContent() {
    final content = <String, dynamic>{};
    
    // Always start with @@locale
    content['@@locale'] = locale;
    
    // Add translations in order (existing first, then new)
    final sortedKeys = entries.keys.toList();
    
    for (final key in sortedKeys) {
      final entry = entries[key]!;
      content[key] = entry.value;
      
      // Add metadata
      final metadataKey = '@$key';
      final metadata = <String, dynamic>{};
      
      if (entry.description != null && entry.description!.isNotEmpty) {
        metadata['description'] = entry.description!;
      }
      
      if (entry.context != null && entry.context!.isNotEmpty) {
        metadata['context'] = entry.context!;
      }
      
      if (metadata.isNotEmpty) {
        content[metadataKey] = metadata;
      }
    }
    
    return content;
  }

  /// Save ARB file
  Future<void> save() async {
    final content = generateContent();
    final jsonString = _formatJson(content);
    
    final file = File(filePath);
    await file.parent.create(recursive: true);
    await file.writeAsString(jsonString, encoding: utf8);
  }

  /// Format JSON content with proper indentation
  String _formatJson(Map<String, dynamic> content) {
    const encoder = JsonEncoder.withIndent('  ');
    return encoder.convert(content);
  }

  /// Generate a preview string for dry-run mode
  String generatePreview() {
    if (!_exists) {
      return '${Colors.green}[NEW FILE]${Colors.reset} $filePath\n'
             '  ${Colors.cyan}@@locale${Colors.reset}: "$locale"\n'
             '  ${Colors.green}+${Colors.reset} ${entries.length} translation(s)';
    }
    
    final existingKeys = _originalContent.keys
        .where((k) => !k.startsWith('@') && k != '@@locale')
        .toSet();
    final newKeys = entries.keys.toSet();
    
    final added = newKeys.difference(existingKeys);
    final updated = newKeys.intersection(existingKeys).where((key) {
      final oldValue = _originalContent[key] as String?;
      final newValue = entries[key]?.value;
      return oldValue != newValue;
    }).toSet();
    final preserved = existingKeys.difference(newKeys);
    
    final result = StringBuffer();
    result.writeln('${Colors.yellow}[UPDATE]${Colors.reset} $filePath');
    
    if (added.isNotEmpty) {
      result.writeln('  ${Colors.green}+${Colors.reset} ${added.length} new translation(s)');
    }
    
    if (updated.isNotEmpty) {
      result.writeln('  ${Colors.blue}~${Colors.reset} ${updated.length} updated translation(s)');
    }
    
    if (preserved.isNotEmpty) {
      result.writeln('  ${Colors.dim}=${Colors.reset} ${preserved.length} preserved translation(s)');
    }
    
    return result.toString().trimRight();
  }

  /// Create backup of existing file
  Future<void> createBackup() async {
    if (!_exists) return;
    
    final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-').split('.')[0];
    final backupPath = '${filePath}_backup_$timestamp';
    
    await File(filePath).copy(backupPath);
  }

  bool get exists => _exists;
  int get entryCount => entries.length;
}

/// Parses input files (CSV/XLSX)
class InputParser {
  /// Parse CSV or XLSX file and return structured data
  static Future<List<Map<String, String>>> parseFile(String filePath) async {
    final file = File(filePath);
    if (!await file.exists()) {
      throw Exception('Input file not found: $filePath');
    }

    final extension = path.extension(filePath).toLowerCase();
    
    switch (extension) {
      case '.csv':
        return _parseCsv(file);
      case '.xlsx':
        return _parseXlsx(file);
      default:
        throw Exception('Unsupported file format: $extension. Only .csv and .xlsx are supported.');
    }
  }

  /// Parse CSV file
  static Future<List<Map<String, String>>> _parseCsv(File file) async {
    final content = await file.readAsString();
    
    // Configure CSV parser with proper settings
    final converter = CsvToListConverter(
      fieldDelimiter: ',',
      textDelimiter: '"',
      textEndDelimiter: '"',
      eol: '\n',
    );
    
    final rows = converter.convert(content);
    
    if (rows.isEmpty) {
      throw Exception('CSV file is empty');
    }
    
    return _processRows(rows);
  }

  /// Parse XLSX file
  static Future<List<Map<String, String>>> _parseXlsx(File file) async {
    final bytes = await file.readAsBytes();
    final excel = Excel.decodeBytes(bytes);
    
    if (excel.tables.isEmpty) {
      throw Exception('XLSX file contains no sheets');
    }
    
    final sheet = excel.tables[excel.tables.keys.first]!;
    final rows = <List<dynamic>>[];
    
    for (final row in sheet.rows) {
      rows.add(row.map((cell) => cell?.value?.toString() ?? '').toList());
    }
    
    if (rows.isEmpty) {
      throw Exception('XLSX sheet is empty');
    }
    
    return _processRows(rows);
  }

  /// Process rows into structured data
  static List<Map<String, String>> _processRows(List<List<dynamic>> rows) {
    if (rows.isEmpty) return [];
    
    // Extract headers
    final headers = rows[0].map((cell) => cell.toString().toLowerCase().trim()).toList();
    
    // Validate required columns
    if (!headers.contains('key')) {
      throw Exception('Missing required column: key');
    }
    
    final result = <Map<String, String>>[];
    
    // Process data rows
    for (int i = 1; i < rows.length; i++) {
      final row = rows[i];
      final rowData = <String, String>{};
      
      for (int j = 0; j < headers.length && j < row.length; j++) {
        final value = row[j]?.toString().trim() ?? '';
        rowData[headers[j]] = value;
      }
      
      // Skip rows with empty keys or @@locale
      final key = rowData['key'] ?? '';
      if (key.isEmpty || key == '@@locale') continue;
      
      result.add(rowData);
    }
    
    return result;
  }
}

/// Main translation importer class
class TranslationImporter {
  final ImportConfig config;
  final Map<String, ArbFile> _arbFiles = {};
  
  // Language code mappings
  static const Map<String, String> _languageMappings = {
    'hi': 'hi',  // Hindi
    'bn': 'bn',  // Bengali  
    'as': 'as',  // Assamese
    'pa': 'pa',  // Punjabi
    'mr': 'mr',  // Marathi
    'kn': 'kn',  // Kannada
    'ta': 'ta',  // Tamil
    'te': 'te',  // Telugu
    'ml': 'ml',  // Malayalam
  };

  TranslationImporter(this.config);

  /// Import translations from file
  Future<void> import() async {
    try {
      _printHeader();
      
      // Parse input file
      final data = await _parseInput();
      
      // Process languages
      await _processLanguages(data);
      
      // Generate/update ARB files
      await _generateArbFiles();
      
      _printSummary();
      
    } catch (e) {
      print('${Colors.red}${Colors.bold}Error:${Colors.reset} $e');
      exit(1);
    }
  }

  /// Parse input file
  Future<List<Map<String, String>>> _parseInput() async {
    print('📖 Parsing input file: ${config.inputFile}');
    
    final data = await InputParser.parseFile(config.inputFile);
    
    if (data.isEmpty) {
      throw Exception('No translation data found in input file');
    }
    
    print('${Colors.green}✅${Colors.reset} Found ${data.length} translation entries');
    return data;
  }

  /// Process languages and create ARB files
  Future<void> _processLanguages(List<Map<String, String>> data) async {
    print('\n🌐 Processing languages...');
    
    // Get available language columns from first row
    final availableLanguages = data.isNotEmpty 
        ? data[0].keys.where((key) => _languageMappings.containsKey(key)).toList()
        : <String>[];
    
    if (availableLanguages.isEmpty) {
      throw Exception('No supported language columns found. Expected: ${_languageMappings.keys.join(', ')}');
    }
    
    // Initialize ARB files for each language
    for (final lang in availableLanguages) {
      final locale = _languageMappings[lang]!;
      final fileName = 'app_$locale.arb';
      final filePath = path.join(config.outputDir, fileName);
      
      final arbFile = ArbFile(locale: locale, filePath: filePath);
      await arbFile.load();
      _arbFiles[lang] = arbFile;
      
      if (config.verbose) {
        final status = arbFile.exists ? 'loaded' : 'new';
        print('  ${Colors.cyan}$lang${Colors.reset} ($locale) → $fileName ($status)');
      }
    }
    
    // Process each translation entry
    var processedCount = 0;
    var skippedCount = 0;
    
    for (final row in data) {
      final key = row['key'] ?? '';
      if (key.isEmpty) continue;
      
      final context = row['context'];
      final description = row['description'];
      
      var hasTranslations = false;
      
      // Add translations to each language
      for (final lang in availableLanguages) {
        final value = row[lang] ?? '';
        if (value.isNotEmpty) {
          final entry = TranslationEntry(
            key: key,
            value: value,
            context: context,
            description: description,
          );
          
          _arbFiles[lang]!.addEntry(entry);
          hasTranslations = true;
        }
      }
      
      if (hasTranslations) {
        processedCount++;
      } else {
        skippedCount++;
        if (config.verbose) {
          print('  ${Colors.yellow}Warning:${Colors.reset} No translations found for key: $key');
        }
      }
    }
    
    print('${Colors.green}✅${Colors.reset} Processed $processedCount entries, skipped $skippedCount');
  }

  /// Generate or update ARB files
  Future<void> _generateArbFiles() async {
    print('\n📝 Generating ARB files...');
    
    if (config.dryRun) {
      print('${Colors.cyan}${Colors.bold}DRY RUN MODE${Colors.reset} - No files will be modified\n');
    }
    
    for (final entry in _arbFiles.entries) {
      final lang = entry.key;
      final arbFile = entry.value;
      
      if (arbFile.entryCount == 0) {
        if (config.verbose) {
          print('  ${Colors.dim}Skipping $lang - no translations${Colors.reset}');
        }
        continue;
      }
      
      if (config.dryRun) {
        print(arbFile.generatePreview());
        print('');
      } else {
        // Create backup if requested and file exists
        if (config.backup && arbFile.exists) {
          await arbFile.createBackup();
        }
        
        // Save the file
        await arbFile.save();
        
        final status = arbFile.exists ? 'Updated' : 'Created';
        print('  ${Colors.green}✅${Colors.reset} $status ${path.basename(arbFile.filePath)} (${arbFile.entryCount} translations)');
      }
    }
  }

  /// Print header information
  void _printHeader() {
    print('${Colors.bold}🚀 Translation Importer${Colors.reset}');
    print('${Colors.dim}Reading from: ${config.inputFile}${Colors.reset}');
    print('${Colors.dim}Output directory: ${config.outputDir}${Colors.reset}');
    
    if (config.dryRun) {
      print('${Colors.cyan}${Colors.bold}🔍 DRY RUN MODE${Colors.reset} - No files will be modified');
    }
    
    if (config.backup) {
      print('${Colors.yellow}📋 Backup mode enabled${Colors.reset}');
    }
    
    print('');
  }

  /// Print summary
  void _printSummary() {
    final filesProcessed = _arbFiles.values.where((f) => f.entryCount > 0).length;
    final totalTranslations = _arbFiles.values.map((f) => f.entryCount).fold(0, (a, b) => a + b);
    
    print('\n${Colors.blue}${Colors.bold}Summary:${Colors.reset}');
    print('  Files processed: $filesProcessed');
    print('  Total translations: $totalTranslations');
    
    if (config.dryRun) {
      print('\n${Colors.cyan}${Colors.bold}ℹ️  Run without --dry-run to apply changes${Colors.reset}');
    } else {
      print('\n${Colors.green}${Colors.bold}🎉 Translation import completed successfully!${Colors.reset}');
    }
  }
}

/// Command line argument parser
class ArgumentParser {
  bool dryRun = false;
  bool backup = false;
  bool verbose = false;
  bool help = false;
  String outputDir = 'lib/l10n';
  String? inputFile;

  ArgumentParser(List<String> args) {
    for (int i = 0; i < args.length; i++) {
      switch (args[i]) {
        case '--dry-run':
          dryRun = true;
          break;
        case '--backup':
          backup = true;
          break;
        case '--verbose':
        case '-v':
          verbose = true;
          break;
        case '--help':
        case '-h':
          help = true;
          break;
        case '--output-dir':
          if (i + 1 < args.length) {
            outputDir = args[++i];
          } else {
            throw ArgumentError('--output-dir requires a value');
          }
          break;
        default:
          if (args[i].startsWith('--output-dir=')) {
            outputDir = args[i].substring('--output-dir='.length);
          } else if (args[i].startsWith('-')) {
            throw ArgumentError('Unknown option: ${args[i]}');
          } else {
            inputFile ??= args[i];
          }
      }
    }
  }

  void printUsage() {
    print('''
${Colors.bold}Translation Importer${Colors.reset}

Imports translations from CSV/XLSX files and updates ARB files.

${Colors.bold}Usage:${Colors.reset}
  dart import_translations.dart [options] <input-file>

${Colors.bold}Arguments:${Colors.reset}
  input-file            CSV or XLSX file containing translations

${Colors.bold}Options:${Colors.reset}
  --dry-run             Preview changes without writing files
  --output-dir=<path>   Output directory for ARB files (default: lib/l10n)
  --backup              Create backup of existing ARB files
  --verbose, -v         Show detailed progress information
  --help, -h            Show this help message

${Colors.bold}Supported Languages:${Colors.reset}
  hi (Hindi), bn (Bengali), as (Assamese), pa (Punjabi)
  mr (Marathi), kn (Kannada), ta (Tamil), te (Telugu), ml (Malayalam)

${Colors.bold}Input File Format:${Colors.reset}
  CSV/XLSX with columns: key, english, context, description, [language codes]
  
${Colors.bold}Examples:${Colors.reset}
  dart import_translations.dart translations.csv
  dart import_translations.dart --dry-run --verbose translations.xlsx
  dart import_translations.dart --backup --output-dir=assets/l10n data.csv

${Colors.bold}Features:${Colors.reset}
  • Preserves existing translations and metadata
  • Adds missing @key metadata for new translations  
  • Automatically sets @@locale for each language
  • Supports both CSV and XLSX input formats
  • Safe dry-run mode for previewing changes
  • Optional backup of existing files
''');
  }
}

/// Main entry point
Future<void> main(List<String> arguments) async {
  try {
    final args = ArgumentParser(arguments);
    
    if (args.help) {
      args.printUsage();
      exit(0);
    }
    
    if (args.inputFile == null) {
      print('${Colors.red}Error: Input file is required${Colors.reset}');
      print('Use --help for usage information.');
      exit(1);
    }
    
    final config = ImportConfig(
      inputFile: args.inputFile!,
      outputDir: args.outputDir,
      dryRun: args.dryRun,
      backup: args.backup,
      verbose: args.verbose,
    );
    
    final importer = TranslationImporter(config);
    await importer.import();
    
  } catch (e) {
    print('${Colors.red}${Colors.bold}Error:${Colors.reset} $e');
    print('\nUse --help for usage information.');
    exit(1);
  }
}
