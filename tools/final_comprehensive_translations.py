#!/usr/bin/env python3
"""
Final Comprehensive Translation Update
Adds the most critical missing translations for all languages
"""

import json
import re
import os
from datetime import datetime
import shutil

# Most critical missing translations based on actual app usage
CRITICAL_MISSING_TRANSLATIONS = {
    'hi': {
        # Button translations
        "btn_other": "अन्य",
        "btn_other_ca": "अन्य CA",
        "btn_other_ehkobhs": "अन्य EHK/OBHS",
        "btn_pending": "लंबित",
        "btn_rake_deficiency_report": "रेक कमी रिपोर्ट",
        "btn_save_selection": "चयन सहेजें",
        "btn_select_charting_day": "चार्टिंग दिन चुनें",
        "btn_self": "स्वयं",
        "btn_upload_json_data": "JSON डेटा अपलोड करें",
        "btn_your_current_location": "आपका वर्तमान स्थान",
        
        # Form translations
        "form_add_stoppage": "स्टॉपेज जोड़ें",
        "form_add_your_comments": "अपनी टिप्पणी जोड़ें",
        "form_add_your_feedback": "अपना फीडबैक जोड़ें",
        "form_coach_number": "कोच संख्या",
        "form_departure_station": "प्रस्थान स्टेशन",
        "form_destination_station": "गंतव्य स्टेशन",
        "form_enter_coach_number": "कोच संख्या दर्ज करें",
        "form_enter_train_number": "ट्रेन संख्या दर्ज करें",
        "form_journey_date": "यात्रा तारीख",
        "form_select_date": "तारीख चुनें",
        "form_select_station": "स्टेशन चुनें",
        "form_select_train": "ट्रेन चुनें",
        
        # Text messages
        "text_attendance": "उपस्थिति",
        "text_chart_prepared": "चार्ट तैयार",
        "text_coach_assignment": "कोच असाइनमेंट",
        "text_current_status": "वर्तमान स्थिति",
        "text_data_sync": "डेटा सिंक",
        "text_download_chart": "चार्ट डाउनलोड करें",
        "text_journey_details": "यात्रा विवरण",
        "text_location_update": "स्थान अपडेट",
        "text_passenger_count": "यात्री संख्या",
        "text_platform_info": "प्लेटफॉर्म जानकारी",
        "text_rake_composition": "रेक संरचना",
        "text_station_code": "स्टेशन कोड",
        "text_train_composition": "ट्रेन संरचना",
        "text_upload_status": "अपलोड स्थिति",
        
        # Error and status messages
        "error_connection_timeout": "कनेक्शन समय समाप्त",
        "error_data_not_found": "डेटा नहीं मिला",
        "error_invalid_input": "अमान्य इनपुट",
        "error_network_unavailable": "नेटवर्क अनुपलब्ध",
        "error_permission_denied": "अनुमति अस्वीकृत",
        "error_server_error": "सर्वर त्रुटि",
        "error_upload_failed": "अपलोड विफल",
        
        "success_data_saved": "डेटा सहेजा गया",
        "success_upload_complete": "अपलोड पूर्ण",
        "success_sync_complete": "सिंक पूर्ण",
        
        "msg_loading_data": "डेटा लोड हो रहा है",
        "msg_processing": "प्रसंस्करण",
        "msg_please_wait": "कृपया प्रतीक्षा करें",
        "msg_no_data_available": "कोई डेटा उपलब्ध नहीं",
        "msg_select_option": "विकल्प चुनें",
        
        # Navigation and menu
        "nav_dashboard": "डैशबोर्ड",
        "nav_reports": "रिपोर्ट्स",
        "nav_notifications": "सूचनाएं",
        "nav_profile": "प्रोफाइल",
        "nav_help": "सहायता",
        "nav_about": "के बारे में",
        
        # Time and date
        "time_now": "अभी",
        "time_today": "आज",
        "time_yesterday": "कल",
        "time_tomorrow": "कल",
        "date_format": "दिनांक प्रारूप",
        "time_format": "समय प्रारूप"
    },
    'bn': {
        # Button translations
        "btn_other": "অন্য",
        "btn_other_ca": "অন্য CA",
        "btn_other_ehkobhs": "অন্য EHK/OBHS",
        "btn_pending": "অপেক্ষমাণ",
        "btn_rake_deficiency_report": "রেক ঘাটতি রিপোর্ট",
        "btn_save_selection": "নির্বাচন সংরক্ষণ",
        "btn_select_charting_day": "চার্টিং দিন নির্বাচন",
        "btn_self": "নিজে",
        "btn_upload_json_data": "JSON ডেটা আপলোড",
        "btn_your_current_location": "আপনার বর্তমান অবস্থান",
        
        # Form translations
        "form_add_stoppage": "স্টপেজ যোগ করুন",
        "form_add_your_comments": "আপনার মন্তব্য যোগ করুন",
        "form_add_your_feedback": "আপনার প্রতিক্রিয়া যোগ করুন",
        "form_coach_number": "কোচ নম্বর",
        "form_departure_station": "প্রস্থান স্টেশন",
        "form_destination_station": "গন্তব্য স্টেশন",
        "form_enter_coach_number": "কোচ নম্বর প্রবেশ করুন",
        "form_enter_train_number": "ট্রেন নম্বর প্রবেশ করুন",
        "form_journey_date": "যাত্রার তারিখ",
        "form_select_date": "তারিখ নির্বাচন করুন",
        "form_select_station": "স্টেশন নির্বাচন করুন",
        "form_select_train": "ট্রেন নির্বাচন করুন",
        
        # Text messages
        "text_attendance": "উপস্থিতি",
        "text_chart_prepared": "চার্ট প্রস্তুত",
        "text_coach_assignment": "কোচ নিয়োগ",
        "text_current_status": "বর্তমান অবস্থা",
        "text_data_sync": "ডেটা সিঙ্ক",
        "text_download_chart": "চার্ট ডাউনলোড",
        "text_journey_details": "যাত্রার বিবরণ",
        "text_location_update": "অবস্থান আপডেট",
        "text_passenger_count": "যাত্রী সংখ্যা",
        "text_platform_info": "প্ল্যাটফর্ম তথ্য",
        "text_rake_composition": "রেক গঠন",
        "text_station_code": "স্টেশন কোড",
        "text_train_composition": "ট্রেন গঠন",
        "text_upload_status": "আপলোড অবস্থা",
        
        # Error and status messages
        "error_connection_timeout": "সংযোগ সময় শেষ",
        "error_data_not_found": "ডেটা পাওয়া যায়নি",
        "error_invalid_input": "অবৈধ ইনপুট",
        "error_network_unavailable": "নেটওয়ার্ক অনুপলব্ধ",
        "error_permission_denied": "অনুমতি প্রত্যাখ্যাত",
        "error_server_error": "সার্ভার ত্রুটি",
        "error_upload_failed": "আপলোড ব্যর্থ",
        
        "success_data_saved": "ডেটা সংরক্ষিত",
        "success_upload_complete": "আপলোড সম্পূর্ণ",
        "success_sync_complete": "সিঙ্ক সম্পূর্ণ",
        
        "msg_loading_data": "ডেটা লোড হচ্ছে",
        "msg_processing": "প্রক্রিয়াকরণ",
        "msg_please_wait": "অনুগ্রহ করে অপেক্ষা করুন",
        "msg_no_data_available": "কোন ডেটা উপলব্ধ নেই",
        "msg_select_option": "বিকল্প নির্বাচন করুন",
        
        # Navigation and menu
        "nav_dashboard": "ড্যাশবোর্ড",
        "nav_reports": "রিপোর্ট",
        "nav_notifications": "বিজ্ঞপ্তি",
        "nav_profile": "প্রোফাইল",
        "nav_help": "সাহায্য",
        "nav_about": "সম্পর্কে"
    }
}

def add_comprehensive_translations(language_code):
    """Add comprehensive translations to ARB file"""
    arb_file = f'lib/l10n/app_{language_code}.arb'
    
    if not os.path.exists(arb_file):
        print(f"ARB file not found: {arb_file}")
        return False
    
    # Create backup
    backup_file = f'{arb_file}_backup_final_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
    shutil.copy2(arb_file, backup_file)
    print(f"Backup created: {backup_file}")
    
    # Read existing content
    with open(arb_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Parse existing translations
    existing_keys = set()
    for line in content.split('\n'):
        if re.match(r'^  "[^@].*": ', line):
            match = re.match(r'^  "([^"]+)": ', line)
            if match:
                existing_keys.add(match.group(1))
    
    # Get translations for this language
    translations = CRITICAL_MISSING_TRANSLATIONS.get(language_code, {})
    new_translations = {k: v for k, v in translations.items() if k not in existing_keys}
    
    if not new_translations:
        print(f"No new comprehensive translations to add for {language_code}")
        return True
    
    # Find insertion point (before closing brace)
    lines = content.split('\n')
    insert_index = -1
    for i in range(len(lines) - 1, -1, -1):
        if lines[i].strip() and not lines[i].strip() == '}':
            insert_index = i
            break
    
    if insert_index == -1:
        print(f"Could not find insertion point in {arb_file}")
        return False
    
    # Add comma to last existing entry if needed
    if not lines[insert_index].endswith(','):
        lines[insert_index] = lines[insert_index] + ','
    
    # Prepare new entries
    new_entries = []
    for key, value in new_translations.items():
        new_entries.append(f'  "{key}": "{value}",')
        new_entries.append(f'  "@{key}": {{')
        new_entries.append(f'    "description": "Comprehensive translation for: {key}",')
        new_entries.append(f'    "context": "comprehensive_phase1_phase2"')
        new_entries.append(f'  }},')
    
    # Remove comma from the last new entry
    if new_entries:
        new_entries[-1] = new_entries[-1].rstrip(',')
    
    # Insert new entries
    for i, entry in enumerate(new_entries):
        lines.insert(insert_index + 1 + i, entry)
    
    # Write back to file
    with open(arb_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(lines))
    
    print(f"Added {len(new_translations)} comprehensive translations to {arb_file}")
    return True

if __name__ == "__main__":
    # Update Hindi and Bengali with comprehensive translations
    languages = ['hi', 'bn']  # Start with these two most important languages
    
    print("Adding comprehensive translations for Phase 1 & 2...")
    
    for lang in languages:
        print(f"\nProcessing {lang.upper()}...")
        if add_comprehensive_translations(lang):
            print(f"Successfully updated {lang.upper()} ARB file")
        else:
            print(f"Failed to update {lang.upper()} ARB file")
    
    print(f"\nCompleted updating {len(languages)} languages with comprehensive translations")
    print("\nNext steps:")
    print("1. Run 'flutter gen-l10n' to regenerate localization files")
    print("2. Test the app with Hindi and Bengali languages")
    print("3. Verify that Phase 1 and Phase 2 features work correctly")
