#!/usr/bin/env python3
"""
Mass Translation Generator for RailOps Flutter App
Generates comprehensive translations for all 9 Indian languages
"""

import json
import re
import os

# Translation mappings for all 9 languages
TRANSLATIONS = {
    'hi': {  # Hindi
        'common_words': {
            'train': 'ट्रेन', 'station': 'स्टेशन', 'passenger': 'यात्री', 'ticket': 'टिकट',
            'coach': 'कोच', 'seat': 'सीट', 'platform': 'प्लेटफॉर्म', 'time': 'समय',
            'date': 'तारीख', 'number': 'संख्या', 'name': 'नाम', 'details': 'विवरण',
            'save': 'सहेजें', 'cancel': 'रद्द करें', 'delete': 'हटाएं', 'edit': 'संपादित करें',
            'add': 'जोड़ें', 'update': 'अपडेट करें', 'confirm': 'पुष्टि करें', 'submit': 'जमा करें',
            'login': 'लॉगिन', 'logout': 'लॉगआउट', 'password': 'पासवर्ड', 'username': 'उपयोगकर्ता नाम',
            'email': 'ईमेल', 'phone': 'फोन', 'address': 'पता', 'profile': 'प्रोफाइल',
            'settings': 'सेटिंग्स', 'help': 'सहायता', 'about': 'के बारे में', 'contact': 'संपर्क',
            'search': 'खोजें', 'filter': 'फिल्टर', 'sort': 'क्रमबद्ध करें', 'refresh': 'रीफ्रेश',
            'loading': 'लोड हो रहा है', 'error': 'त्रुटि', 'success': 'सफलता', 'warning': 'चेतावनी',
            'info': 'जानकारी', 'notification': 'सूचना', 'message': 'संदेश', 'alert': 'अलर्ट',
            'yes': 'हां', 'no': 'नहीं', 'ok': 'ठीक है', 'close': 'बंद करें',
            'open': 'खोलें', 'download': 'डाउनलोड', 'upload': 'अपलोड', 'share': 'साझा करें',
            'print': 'प्रिंट', 'export': 'निर्यात', 'import': 'आयात', 'backup': 'बैकअप',
            'restore': 'पुनर्स्थापना', 'sync': 'सिंक', 'connect': 'कनेक्ट', 'disconnect': 'डिस्कनेक्ट',
            'online': 'ऑनलाइन', 'offline': 'ऑफलाइन', 'available': 'उपलब्ध', 'unavailable': 'अनुपलब्ध',
            'active': 'सक्रिय', 'inactive': 'निष्क्रिय', 'enabled': 'सक्षम', 'disabled': 'अक्षम',
            'public': 'सार्वजनिक', 'private': 'निजी', 'secure': 'सुरक्षित', 'unsecure': 'असुरक्षित'
        }
    },
    'bn': {  # Bengali
        'common_words': {
            'train': 'ট্রেন', 'station': 'স্টেশন', 'passenger': 'যাত্রী', 'ticket': 'টিকিট',
            'coach': 'কোচ', 'seat': 'আসন', 'platform': 'প্ল্যাটফর্ম', 'time': 'সময়',
            'date': 'তারিখ', 'number': 'নম্বর', 'name': 'নাম', 'details': 'বিবরণ',
            'save': 'সংরক্ষণ', 'cancel': 'বাতিল', 'delete': 'মুছুন', 'edit': 'সম্পাদনা',
            'add': 'যোগ করুন', 'update': 'আপডেট', 'confirm': 'নিশ্চিত', 'submit': 'জমা দিন',
            'login': 'লগইন', 'logout': 'লগআউট', 'password': 'পাসওয়ার্ড', 'username': 'ব্যবহারকারীর নাম',
            'email': 'ইমেইল', 'phone': 'ফোন', 'address': 'ঠিকানা', 'profile': 'প্রোফাইল',
            'settings': 'সেটিংস', 'help': 'সাহায্য', 'about': 'সম্পর্কে', 'contact': 'যোগাযোগ',
            'search': 'অনুসন্ধান', 'filter': 'ফিল্টার', 'sort': 'সাজান', 'refresh': 'রিফ্রেশ',
            'loading': 'লোড হচ্ছে', 'error': 'ত্রুটি', 'success': 'সফল', 'warning': 'সতর্কতা',
            'info': 'তথ্য', 'notification': 'বিজ্ঞপ্তি', 'message': 'বার্তা', 'alert': 'সতর্কতা',
            'yes': 'হ্যাঁ', 'no': 'না', 'ok': 'ঠিক আছে', 'close': 'বন্ধ করুন'
        }
    },
    'as': {  # Assamese
        'common_words': {
            'train': 'ৰেল', 'station': 'ষ্টেচন', 'passenger': 'যাত্ৰী', 'ticket': 'টিকট',
            'coach': 'কোচ', 'seat': 'আসন', 'platform': 'প্লেটফৰ্ম', 'time': 'সময়',
            'date': 'তাৰিখ', 'number': 'নম্বৰ', 'name': 'নাম', 'details': 'বিৱৰণ',
            'save': 'সংৰক্ষণ', 'cancel': 'বাতিল', 'delete': 'মচক', 'edit': 'সম্পাদনা',
            'add': 'যোগ কৰক', 'update': 'আপডেট', 'confirm': 'নিশ্চিত', 'submit': 'জমা দিয়ক',
            'login': 'লগইন', 'logout': 'লগআউট', 'password': 'পাছৱৰ্ড', 'username': 'ব্যৱহাৰকাৰীৰ নাম',
            'email': 'ইমেইল', 'phone': 'ফোন', 'address': 'ঠিকনা', 'profile': 'প্ৰফাইল',
            'settings': 'ছেটিংছ', 'help': 'সহায়', 'about': 'সম্পৰ্কে', 'contact': 'যোগাযোগ',
            'search': 'অনুসন্ধান', 'filter': 'ফিল্টাৰ', 'sort': 'সজাওক', 'refresh': 'ৰিফ্ৰেছ',
            'loading': 'লোড হৈ আছে', 'error': 'ত্ৰুটি', 'success': 'সফল', 'warning': 'সতৰ্কতা',
            'info': 'তথ্য', 'notification': 'জাননী', 'message': 'বাৰ্তা', 'alert': 'সতৰ্কতা',
            'yes': 'হয়', 'no': 'নহয়', 'ok': 'ঠিক আছে', 'close': 'বন্ধ কৰক'
        }
    },
    'pa': {  # Punjabi
        'common_words': {
            'train': 'ਰੇਲਗੱਡੀ', 'station': 'ਸਟੇਸ਼ਨ', 'passenger': 'ਯਾਤਰੀ', 'ticket': 'ਟਿਕਟ',
            'coach': 'ਕੋਚ', 'seat': 'ਸੀਟ', 'platform': 'ਪਲੇਟਫਾਰਮ', 'time': 'ਸਮਾਂ',
            'date': 'ਤਾਰੀਖ', 'number': 'ਨੰਬਰ', 'name': 'ਨਾਮ', 'details': 'ਵੇਰਵੇ',
            'save': 'ਸੇਵ ਕਰੋ', 'cancel': 'ਰੱਦ ਕਰੋ', 'delete': 'ਮਿਟਾਓ', 'edit': 'ਸੰਪਾਦਨ',
            'add': 'ਸ਼ਾਮਲ ਕਰੋ', 'update': 'ਅਪਡੇਟ', 'confirm': 'ਪੁਸ਼ਟੀ', 'submit': 'ਜਮ੍ਹਾਂ ਕਰੋ',
            'login': 'ਲਾਗਇਨ', 'logout': 'ਲਾਗਆਉਟ', 'password': 'ਪਾਸਵਰਡ', 'username': 'ਯੂਜ਼ਰਨੇਮ',
            'email': 'ਈਮੇਲ', 'phone': 'ਫੋਨ', 'address': 'ਪਤਾ', 'profile': 'ਪ੍ਰੋਫਾਈਲ',
            'settings': 'ਸੈਟਿੰਗਜ਼', 'help': 'ਮਦਦ', 'about': 'ਬਾਰੇ', 'contact': 'ਸੰਪਰਕ',
            'search': 'ਖੋਜ', 'filter': 'ਫਿਲਟਰ', 'sort': 'ਕ੍ਰਮਬੱਧ', 'refresh': 'ਰਿਫਰੈਸ਼',
            'loading': 'ਲੋਡ ਹੋ ਰਿਹਾ', 'error': 'ਗਲਤੀ', 'success': 'ਸਫਲਤਾ', 'warning': 'ਚੇਤਾਵਨੀ',
            'info': 'ਜਾਣਕਾਰੀ', 'notification': 'ਸੂਚਨਾ', 'message': 'ਸੰਦੇਸ਼', 'alert': 'ਅਲਰਟ',
            'yes': 'ਹਾਂ', 'no': 'ਨਹੀਂ', 'ok': 'ਠੀਕ ਹੈ', 'close': 'ਬੰਦ ਕਰੋ'
        }
    },
    'mr': {  # Marathi
        'common_words': {
            'train': 'ट्रेन', 'station': 'स्टेशन', 'passenger': 'प्रवासी', 'ticket': 'तिकीट',
            'coach': 'कोच', 'seat': 'जागा', 'platform': 'प्लॅटफॉर्म', 'time': 'वेळ',
            'date': 'तारीख', 'number': 'क्रमांक', 'name': 'नाव', 'details': 'तपशील',
            'save': 'जतन करा', 'cancel': 'रद्द करा', 'delete': 'हटवा', 'edit': 'संपादन',
            'add': 'जोडा', 'update': 'अपडेट', 'confirm': 'पुष्टी', 'submit': 'सबमिट',
            'login': 'लॉगिन', 'logout': 'लॉगआउट', 'password': 'पासवर्ड', 'username': 'वापरकर्तानाव',
            'email': 'ईमेल', 'phone': 'फोन', 'address': 'पत्ता', 'profile': 'प्रोफाइल',
            'settings': 'सेटिंग्ज', 'help': 'मदत', 'about': 'बद्दल', 'contact': 'संपर्क',
            'search': 'शोध', 'filter': 'फिल्टर', 'sort': 'क्रमवारी', 'refresh': 'रिफ्रेश',
            'loading': 'लोड होत आहे', 'error': 'त्रुटी', 'success': 'यश', 'warning': 'चेतावणी',
            'info': 'माहिती', 'notification': 'सूचना', 'message': 'संदेश', 'alert': 'सावधान',
            'yes': 'होय', 'no': 'नाही', 'ok': 'ठीक आहे', 'close': 'बंद करा'
        }
    },
    'kn': {  # Kannada
        'common_words': {
            'train': 'ರೈಲು', 'station': 'ನಿಲ್ದಾಣ', 'passenger': 'ಪ್ರಯಾಣಿಕ', 'ticket': 'ಟಿಕೆಟ್',
            'coach': 'ಕೋಚ್', 'seat': 'ಆಸನ', 'platform': 'ಪ್ಲಾಟ್‌ಫಾರ್ಮ್', 'time': 'ಸಮಯ',
            'date': 'ದಿನಾಂಕ', 'number': 'ಸಂಖ್ಯೆ', 'name': 'ಹೆಸರು', 'details': 'ವಿವರಗಳು',
            'save': 'ಉಳಿಸಿ', 'cancel': 'ರದ್ದುಗೊಳಿಸಿ', 'delete': 'ಅಳಿಸಿ', 'edit': 'ಸಂಪಾದಿಸಿ',
            'add': 'ಸೇರಿಸಿ', 'update': 'ನವೀಕರಿಸಿ', 'confirm': 'ದೃಢೀಕರಿಸಿ', 'submit': 'ಸಲ್ಲಿಸಿ',
            'login': 'ಲಾಗಿನ್', 'logout': 'ಲಾಗ್ಔಟ್', 'password': 'ಪಾಸ್‌ವರ್ಡ್', 'username': 'ಬಳಕೆದಾರ ಹೆಸರು',
            'email': 'ಇಮೇಲ್', 'phone': 'ಫೋನ್', 'address': 'ವಿಳಾಸ', 'profile': 'ಪ್ರೊಫೈಲ್',
            'settings': 'ಸೆಟ್ಟಿಂಗ್‌ಗಳು', 'help': 'ಸಹಾಯ', 'about': 'ಬಗ್ಗೆ', 'contact': 'ಸಂಪರ್ಕ',
            'search': 'ಹುಡುಕಿ', 'filter': 'ಫಿಲ್ಟರ್', 'sort': 'ವಿಂಗಡಿಸಿ', 'refresh': 'ರಿಫ್ರೆಶ್',
            'loading': 'ಲೋಡ್ ಆಗುತ್ತಿದೆ', 'error': 'ದೋಷ', 'success': 'ಯಶಸ್ಸು', 'warning': 'ಎಚ್ಚರಿಕೆ',
            'info': 'ಮಾಹಿತಿ', 'notification': 'ಅಧಿಸೂಚನೆ', 'message': 'ಸಂದೇಶ', 'alert': 'ಎಚ್ಚರಿಕೆ',
            'yes': 'ಹೌದು', 'no': 'ಇಲ್ಲ', 'ok': 'ಸರಿ', 'close': 'ಮುಚ್ಚಿ'
        }
    },
    'ta': {  # Tamil
        'common_words': {
            'train': 'ரயில்', 'station': 'நிலையம்', 'passenger': 'பயணி', 'ticket': 'டிக்கெட்',
            'coach': 'கோச்', 'seat': 'இருக்கை', 'platform': 'பிளாட்பார்ம்', 'time': 'நேரம்',
            'date': 'தேதி', 'number': 'எண்', 'name': 'பெயர்', 'details': 'விவரங்கள்',
            'save': 'சேமி', 'cancel': 'ரத்து', 'delete': 'நீக்கு', 'edit': 'திருத்து',
            'add': 'சேர்', 'update': 'புதுப்பி', 'confirm': 'உறுதி', 'submit': 'சமர்ப்பி',
            'login': 'உள்நுழை', 'logout': 'வெளியேறு', 'password': 'கடவுச்சொல்', 'username': 'பயனர்பெயர்',
            'email': 'மின்னஞ்சல்', 'phone': 'தொலைபேசி', 'address': 'முகவரி', 'profile': 'சுயவிவரம்',
            'settings': 'அமைப்புகள்', 'help': 'உதவி', 'about': 'பற்றி', 'contact': 'தொடர்பு',
            'search': 'தேடு', 'filter': 'வடிகட்டி', 'sort': 'வரிசைப்படுத்து', 'refresh': 'புதுப்பி',
            'loading': 'ஏற்றுகிறது', 'error': 'பிழை', 'success': 'வெற்றி', 'warning': 'எச்சரிக்கை',
            'info': 'தகவல்', 'notification': 'அறிவிப்பு', 'message': 'செய்தி', 'alert': 'எச்சரிக்கை',
            'yes': 'ஆம்', 'no': 'இல்லை', 'ok': 'சரி', 'close': 'மூடு'
        }
    },
    'te': {  # Telugu
        'common_words': {
            'train': 'రైలు', 'station': 'స్టేషన్', 'passenger': 'ప్రయాణికుడు', 'ticket': 'టిక్కెట్',
            'coach': 'కోచ్', 'seat': 'సీటు', 'platform': 'ప్లాట్‌ఫాం', 'time': 'సమయం',
            'date': 'తేదీ', 'number': 'సంఖ్య', 'name': 'పేరు', 'details': 'వివరాలు',
            'save': 'సేవ్', 'cancel': 'రద్దు', 'delete': 'తొలగించు', 'edit': 'సవరించు',
            'add': 'జోడించు', 'update': 'అప్‌డేట్', 'confirm': 'ధృవీకరించు', 'submit': 'సమర్పించు',
            'login': 'లాగిన్', 'logout': 'లాగ్‌అవుట్', 'password': 'పాస్‌వర్డ్', 'username': 'వినియోగదారు పేరు',
            'email': 'ఇమెయిల్', 'phone': 'ఫోన్', 'address': 'చిరునామా', 'profile': 'ప్రొఫైల్',
            'settings': 'సెట్టింగ్‌లు', 'help': 'సహాయం', 'about': 'గురించి', 'contact': 'సంప్రదించు',
            'search': 'వెతకు', 'filter': 'ఫిల్టర్', 'sort': 'క్రమబద్ధీకరించు', 'refresh': 'రిఫ్రెష్',
            'loading': 'లోడవుతోంది', 'error': 'లోపం', 'success': 'విజయం', 'warning': 'హెచ్చరిక',
            'info': 'సమాచారం', 'notification': 'నోటిఫికేషన్', 'message': 'సందేశం', 'alert': 'హెచ్చరిక',
            'yes': 'అవును', 'no': 'లేదు', 'ok': 'సరే', 'close': 'మూసివేయి'
        }
    },
    'ml': {  # Malayalam
        'common_words': {
            'train': 'ട്രെയിൻ', 'station': 'സ്റ്റേഷൻ', 'passenger': 'യാത്രക്കാരൻ', 'ticket': 'ടിക്കറ്റ്',
            'coach': 'കോച്ച്', 'seat': 'സീറ്റ്', 'platform': 'പ്ലാറ്റ്ഫോം', 'time': 'സമയം',
            'date': 'തീയതി', 'number': 'നമ്പർ', 'name': 'പേര്', 'details': 'വിശദാംശങ്ങൾ',
            'save': 'സേവ് ചെയ്യുക', 'cancel': 'റദ്ദാക്കുക', 'delete': 'ഇല്ലാതാക്കുക', 'edit': 'എഡിറ്റ് ചെയ്യുക',
            'add': 'ചേർക്കുക', 'update': 'അപ്ഡേറ്റ്', 'confirm': 'സ്ഥിരീകരിക്കുക', 'submit': 'സമർപ്പിക്കുക',
            'login': 'ലോഗിൻ', 'logout': 'ലോഗൗട്ട്', 'password': 'പാസ്‌വേഡ്', 'username': 'ഉപയോക്തൃനാമം',
            'email': 'ഇമെയിൽ', 'phone': 'ഫോൺ', 'address': 'വിലാസം', 'profile': 'പ്രൊഫൈൽ',
            'settings': 'ക്രമീകരണങ്ങൾ', 'help': 'സഹായം', 'about': 'കുറിച്ച്', 'contact': 'ബന്ധപ്പെടുക',
            'search': 'തിരയുക', 'filter': 'ഫിൽട്ടർ', 'sort': 'ക്രമീകരിക്കുക', 'refresh': 'പുതുക്കുക',
            'loading': 'ലോഡ് ചെയ്യുന്നു', 'error': 'പിശക്', 'success': 'വിജയം', 'warning': 'മുന്നറിയിപ്പ്',
            'info': 'വിവരം', 'notification': 'അറിയിപ്പ്', 'message': 'സന്ദേശം', 'alert': 'മുന്നറിയിപ്പ്',
            'yes': 'അതെ', 'no': 'ഇല്ല', 'ok': 'ശരി', 'close': 'അടയ്ക്കുക'
        }
    }
}

def extract_english_keys(arb_file_path):
    """Extract all translation keys and values from English ARB file"""
    with open(arb_file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    keys = []
    lines = content.split('\n')
    for i, line in enumerate(lines):
        if re.match(r'^  "[^@].*": ', line):
            match = re.match(r'^  "([^"]+)": "(.*)",$', line)
            if match:
                key, value = match.groups()
                keys.append({'key': key, 'value': value, 'line': i+1})
    
    return keys

def get_existing_translations(arb_file_path):
    """Get existing translations from a language ARB file"""
    if not os.path.exists(arb_file_path):
        return {}
    
    with open(arb_file_path, 'r', encoding='utf-8') as f:
        try:
            data = json.load(f)
            return {k: v for k, v in data.items() if not k.startswith('@') and k != '@@locale'}
        except:
            return {}

def smart_translate(english_text, language_code):
    """Smart translation using common word mappings and context"""
    if language_code not in TRANSLATIONS:
        return english_text  # Return English if language not supported

    # Comprehensive translation mappings for railway terms
    railway_translations = {
        'hi': {
            'Re-enter OTP': 'OTP पुनः दर्ज करें',
            'Deny': 'अस्वीकार करें',
            'Enable': 'सक्षम करें',
            'Location Access Required': 'स्थान पहुंच आवश्यक',
            'Decline': 'अस्वीकार करें',
            'Accept': 'स्वीकार करें',
            'Confirm Delete': 'हटाने की पुष्टि करें',
            'Cancel': 'रद्द करें',
            'Delete': 'हटाएं',
            'Storage permission is required to download files': 'फाइलें डाउनलोड करने के लिए स्टोरेज अनुमति आवश्यक है',
            'Please select a train first': 'कृपया पहले एक ट्रेन चुनें',
            'Update Required': 'अपडेट आवश्यक',
            'Update Now': 'अभी अपडेट करें',
            'Please select a train number and date.': 'कृपया ट्रेन नंबर और तारीख चुनें।',
            'All details updated successfully.': 'सभी विवरण सफलतापूर्वक अपडेट किए गए।',
            'Data refreshed successfully': 'डेटा सफलतापूर्वक रीफ्रेश किया गया',
            'Train Location Saved Successfully': 'ट्रेन स्थान सफलतापूर्वक सहेजा गया',
            'Self': 'स्वयं',
            'Other CA': 'अन्य CA',
            'Train Tracker': 'ट्रेन ट्रैकर',
            'Assign CA': 'CA असाइन करें',
            'Assign CS': 'CS असाइन करें',
            'Passenger Feedback': 'यात्री फीडबैक',
            'Customer Care': 'ग्राहक सेवा',
            'User Management': 'उपयोगकर्ता प्रबंधन',
            'Issue Management': 'समस्या प्रबंधन',
            'Upload data': 'डेटा अपलोड करें',
            'Welcome to RailOps': 'रेलऑप्स में आपका स्वागत है',
            'Login': 'लॉगिन',
            'Logout': 'लॉगआउट',
            'Home': 'होम',
            'Settings': 'सेटिंग्स',
            'Profile': 'प्रोफाइल',
            'Help': 'सहायता',
            'About': 'के बारे में'
        },
        'bn': {
            'Re-enter OTP': 'OTP পুনরায় প্রবেশ করুন',
            'Deny': 'অস্বীকার',
            'Enable': 'সক্ষম করুন',
            'Location Access Required': 'অবস্থান অ্যাক্সেস প্রয়োজন',
            'Decline': 'প্রত্যাখ্যান',
            'Accept': 'গ্রহণ করুন',
            'Confirm Delete': 'মুছে ফেলার নিশ্চিতকরণ',
            'Cancel': 'বাতিল',
            'Delete': 'মুছুন',
            'Storage permission is required to download files': 'ফাইল ডাউনলোড করতে স্টোরেজ অনুমতি প্রয়োজন',
            'Please select a train first': 'অনুগ্রহ করে প্রথমে একটি ট্রেন নির্বাচন করুন',
            'Update Required': 'আপডেট প্রয়োজন',
            'Update Now': 'এখনই আপডেট করুন',
            'Please select a train number and date.': 'অনুগ্রহ করে ট্রেন নম্বর এবং তারিখ নির্বাচন করুন।',
            'All details updated successfully.': 'সমস্ত বিবরণ সফলভাবে আপডেট করা হয়েছে।',
            'Data refreshed successfully': 'ডেটা সফলভাবে রিফ্রেশ করা হয়েছে',
            'Train Location Saved Successfully': 'ট্রেনের অবস্থান সফলভাবে সংরক্ষিত হয়েছে',
            'Self': 'নিজে',
            'Other CA': 'অন্য CA',
            'Train Tracker': 'ট্রেন ট্র্যাকার',
            'Assign CA': 'CA নিয়োগ করুন',
            'Assign CS': 'CS নিয়োগ করুন',
            'Passenger Feedback': 'যাত্রী প্রতিক্রিয়া',
            'Customer Care': 'গ্রাহক সেবা',
            'User Management': 'ব্যবহারকারী ব্যবস্থাপনা',
            'Issue Management': 'সমস্যা ব্যবস্থাপনা',
            'Upload data': 'ডেটা আপলোড করুন',
            'Welcome to RailOps': 'রেলঅপ্স-এ স্বাগতম',
            'Login': 'লগইন',
            'Logout': 'লগআউট',
            'Home': 'হোম',
            'Settings': 'সেটিংস',
            'Profile': 'প্রোফাইল',
            'Help': 'সাহায্য',
            'About': 'সম্পর্কে'
        }
    }

    # Check for exact phrase matches first
    if language_code in railway_translations and english_text in railway_translations[language_code]:
        return railway_translations[language_code][english_text]

    # Fall back to word-by-word translation
    common_words = TRANSLATIONS[language_code]['common_words']
    translated = english_text
    for eng_word, translated_word in common_words.items():
        # Case-insensitive replacement
        pattern = re.compile(re.escape(eng_word), re.IGNORECASE)
        translated = pattern.sub(translated_word, translated)

    return translated

def generate_missing_translations(english_keys, existing_translations, language_code):
    """Generate missing translations for a language"""
    missing_translations = {}
    
    for key_info in english_keys:
        key = key_info['key']
        english_value = key_info['value']
        
        if key not in existing_translations or existing_translations[key] == "":
            # Generate translation
            translated_value = smart_translate(english_value, language_code)
            missing_translations[key] = translated_value
    
    return missing_translations

if __name__ == "__main__":
    # Extract English keys
    english_keys = extract_english_keys('lib/l10n/app_en.arb')
    print(f"Found {len(english_keys)} English translation keys")
    
    # Process each language
    languages = ['hi', 'bn', 'as', 'pa', 'mr', 'kn', 'ta', 'te', 'ml']
    
    for lang in languages:
        arb_file = f'lib/l10n/app_{lang}.arb'
        existing = get_existing_translations(arb_file)
        missing = generate_missing_translations(english_keys, existing, lang)
        
        print(f"\n{lang.upper()}: {len(existing)} existing, {len(missing)} missing translations")
        
        # Save missing translations to a file for review
        output_file = f'tools/missing_translations_{lang}.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(missing, f, ensure_ascii=False, indent=2)
        
        print(f"Missing translations saved to {output_file}")

def update_arb_file_with_translations(arb_file_path, missing_translations, english_keys):
    """Update ARB file with missing translations"""
    if not os.path.exists(arb_file_path):
        print(f"ARB file not found: {arb_file_path}")
        return False

    # Read existing ARB file
    with open(arb_file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Parse existing content to preserve structure
    lines = content.split('\n')

    # Find the last entry before the closing brace
    insert_index = -1
    for i in range(len(lines) - 1, -1, -1):
        if lines[i].strip() and not lines[i].strip() == '}':
            insert_index = i
            break

    if insert_index == -1:
        print(f"Could not find insertion point in {arb_file_path}")
        return False

    # Prepare new translations to add
    new_entries = []
    for key_info in english_keys:
        key = key_info['key']
        if key in missing_translations:
            translated_value = missing_translations[key]
            # Add the translation entry
            new_entries.append(f'  "{key}": "{translated_value}",')
            # Add the metadata entry
            new_entries.append(f'  "@{key}": {{')
            new_entries.append(f'    "description": "Translation for: {key_info["value"]}",')
            new_entries.append(f'    "context": "auto_generated"')
            new_entries.append(f'  }},')

    # Remove the comma from the last existing entry if needed
    if lines[insert_index].endswith(','):
        lines[insert_index] = lines[insert_index] + ','
    else:
        lines[insert_index] = lines[insert_index] + ','

    # Insert new entries
    for i, entry in enumerate(new_entries):
        lines.insert(insert_index + 1 + i, entry)

    # Remove comma from the very last entry
    for i in range(len(lines) - 1, -1, -1):
        if lines[i].strip().endswith(',') and not lines[i].strip() == '},':
            lines[i] = lines[i].rstrip(',')
            break

    # Write back to file
    with open(arb_file_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(lines))

    return True

def auto_update_all_arb_files():
    """Automatically update all ARB files with missing translations"""
    # Extract English keys
    english_keys = extract_english_keys('lib/l10n/app_en.arb')
    print(f"Found {len(english_keys)} English translation keys")

    # Process each language
    languages = ['hi', 'bn', 'as', 'pa', 'mr', 'kn', 'ta', 'te', 'ml']

    for lang in languages:
        print(f"\nProcessing {lang.upper()}...")
        arb_file = f'lib/l10n/app_{lang}.arb'
        existing = get_existing_translations(arb_file)
        missing = generate_missing_translations(english_keys, existing, lang)

        print(f"  - {len(existing)} existing translations")
        print(f"  - {len(missing)} missing translations")

        if missing:
            # Create backup
            backup_file = f'{arb_file}_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
            import shutil
            shutil.copy2(arb_file, backup_file)
            print(f"  - Backup created: {backup_file}")

            # Update ARB file
            if update_arb_file_with_translations(arb_file, missing, english_keys):
                print(f"  - Successfully updated {arb_file}")
            else:
                print(f"  - Failed to update {arb_file}")
        else:
            print(f"  - No missing translations for {lang}")

if __name__ == "__main__":
    import sys
    from datetime import datetime

    if len(sys.argv) > 1 and sys.argv[1] == '--auto-update':
        auto_update_all_arb_files()
    else:
        # Extract English keys
        english_keys = extract_english_keys('lib/l10n/app_en.arb')
        print(f"Found {len(english_keys)} English translation keys")

        # Process each language
        languages = ['hi', 'bn', 'as', 'pa', 'mr', 'kn', 'ta', 'te', 'ml']

        for lang in languages:
            arb_file = f'lib/l10n/app_{lang}.arb'
            existing = get_existing_translations(arb_file)
            missing = generate_missing_translations(english_keys, existing, lang)

            print(f"\n{lang.upper()}: {len(existing)} existing, {len(missing)} missing translations")

            # Save missing translations to a file for review
            output_file = f'tools/missing_translations_{lang}.json'
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(missing, f, ensure_ascii=False, indent=2)

            print(f"Missing translations saved to {output_file}")

        print(f"\nTo automatically update all ARB files, run:")
        print(f"python3 tools/mass_translation_generator.py --auto-update")
