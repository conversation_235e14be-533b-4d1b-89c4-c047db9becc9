import 'dart:io';
import 'dart:convert';
import 'automated_string_extractor.dart';

/// Main script to run Priority 2 string extraction for RailOps multi-language implementation
Future<void> main(List<String> args) async {
  print('=== RailOps String Extraction Tool - Priority 2 ===');
  print('Automated string extraction for multi-language implementation\n');

  // Determine the Flutter lib directory
  String libPath = '../lib';
  if (args.isNotEmpty) {
    libPath = args[0];
  }

  final libDir = Directory(libPath);
  if (!libDir.existsSync()) {
    print('Error: Flutter lib directory not found at: $libPath');
    print('Usage: dart run_extraction.dart [path_to_lib_directory]');
    exit(1);
  }

  print('Scanning Flutter codebase at: ${libDir.absolute.path}');
  print('Starting string extraction...\n');

  try {
    // Extract strings from all Dart files
    final results = await AutomatedStringExtractor.extractFromDirectory(libPath);
    
    if (results.isEmpty) {
      print('No extractable strings found in the codebase.');
      return;
    }

    // Generate statistics
    int totalFiles = results.length;
    int totalStrings = 0;
    Map<String, int> categoryStats = {};

    for (String filePath in results.keys) {
      for (String category in results[filePath]!.keys) {
        int count = results[filePath]![category]!.length;
        totalStrings += count;
        categoryStats[category] = (categoryStats[category] ?? 0) + count;
      }
    }

    print('\n=== EXTRACTION SUMMARY ===');
    print('Files processed: $totalFiles');
    print('Total strings extracted: $totalStrings');
    print('\nStrings by category:');
    categoryStats.forEach((category, count) {
      print('  $category: $count strings');
    });

    // Generate ARB entries
    print('\nGenerating ARB entries...');
    final arbEntries = AutomatedStringExtractor.generateARBEntries(results);
    
    // Save detailed results
    final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-').split('.')[0];
    
    // Save extraction results
    await AutomatedStringExtractor.saveResults(results, 'extraction_results_$timestamp.json');
    
    // Save ARB entries
    final arbFile = File('../lib/l10n/app_en.arb');
    
    // Check if ARB file exists and backup if needed
    if (arbFile.existsSync()) {
      final backupFile = File('../lib/l10n/app_en_backup_$timestamp.arb');
      await arbFile.copy(backupFile.path);
      print('Existing ARB file backed up to: ${backupFile.path}');
    }

    // Ensure l10n directory exists
    await arbFile.parent.create(recursive: true);
    
    // Write new ARB file
    await arbFile.writeAsString(
      JsonEncoder.withIndent('  ').convert(arbEntries),
    );
    
    print('Updated ARB file: ${arbFile.path}');
    print('ARB entries generated: ${arbEntries.length ~/ 2}'); // Divide by 2 because each entry has metadata

    // Generate summary report
    await _generateSummaryReport(results, categoryStats, totalFiles, totalStrings, timestamp);

    print('\n=== EXTRACTION COMPLETED SUCCESSFULLY ===');
    print('Next steps:');
    print('1. Review the generated app_en.arb file');
    print('2. Update your Flutter code to use AppLocalizations');
    print('3. Generate ARB files for other languages');
    print('4. Test the localization implementation');

  } catch (e) {
    print('Error during extraction: $e');
    exit(1);
  }
}

/// Generate a detailed summary report
Future<void> _generateSummaryReport(
  Map<String, Map<String, List<ExtractedString>>> results,
  Map<String, int> categoryStats,
  int totalFiles,
  int totalStrings,
  String timestamp,
) async {
  final reportFile = File('extraction_report_$timestamp.md');
  
  final buffer = StringBuffer();
  buffer.writeln('# RailOps String Extraction Report');
  buffer.writeln('Generated: ${DateTime.now()}');
  buffer.writeln();
  
  buffer.writeln('## Summary');
  buffer.writeln('- **Files processed**: $totalFiles');
  buffer.writeln('- **Total strings extracted**: $totalStrings');
  buffer.writeln();
  
  buffer.writeln('## Strings by Category');
  categoryStats.forEach((category, count) {
    buffer.writeln('- **$category**: $count strings');
  });
  buffer.writeln();
  
  buffer.writeln('## Files with Extractable Strings');
  for (String filePath in results.keys) {
    int fileTotal = 0;
    for (var category in results[filePath]!.values) {
      fileTotal += category.length;
    }
    buffer.writeln('- `$filePath` ($fileTotal strings)');
  }
  buffer.writeln();
  
  buffer.writeln('## Sample Extracted Strings by Category');
  for (String category in categoryStats.keys) {
    buffer.writeln('### $category');
    
    // Find first few examples from this category
    List<String> examples = [];
    for (String filePath in results.keys) {
      if (results[filePath]!.containsKey(category)) {
        for (var extracted in results[filePath]![category]!.take(3)) {
          examples.add('- "${extracted.text}" (${extracted.file}:${extracted.lineNumber})');
        }
        if (examples.length >= 3) break;
      }
    }
    
    if (examples.isNotEmpty) {
      buffer.writeAll(examples, '\n');
      buffer.writeln();
    }
  }
  
  buffer.writeln('## Next Steps');
  buffer.writeln('1. Review the generated `app_en.arb` file in `lib/l10n/`');
  buffer.writeln('2. Replace hardcoded strings in your Dart files with `AppLocalizations.of(context)!.keyName`');
  buffer.writeln('3. Create ARB files for other supported languages');
  buffer.writeln('4. Test the localization implementation');
  buffer.writeln('5. Update your `l10n.yaml` configuration if needed');
  
  await reportFile.writeAsString(buffer.toString());
  print('Summary report saved to: ${reportFile.path}');
}
