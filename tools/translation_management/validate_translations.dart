#!/usr/bin/env dart

import 'dart:convert';
import 'dart:io';

/// ANSI color codes for console output
class Colors {
  static const String reset = '\x1B[0m';
  static const String red = '\x1B[31m';
  static const String green = '\x1B[32m';
  static const String yellow = '\x1B[33m';
  static const String blue = '\x1B[34m';
  static const String magenta = '\x1B[35m';
  static const String cyan = '\x1B[36m';
  static const String white = '\x1B[37m';
  static const String bold = '\x1B[1m';
  static const String dim = '\x1B[2m';
}

/// Validation issue severity levels
enum IssueSeverity { error, warning }

/// Represents a validation issue found in an ARB file
class ValidationIssue {
  final String fileName;
  final String key;
  final String message;
  final IssueSeverity severity;
  final String? details;

  ValidationIssue({
    required this.fileName,
    required this.key,
    required this.message,
    required this.severity,
    this.details,
  });

  @override
  String toString() {
    final severityColor = severity == IssueSeverity.error ? Colors.red : Colors.yellow;
    final severityText = severity == IssueSeverity.error ? 'ERROR' : 'WARNING';
    final resetColor = Colors.reset;
    
    String result = '  ${severityColor}${Colors.bold}[$severityText]${resetColor} ';
    result += '${Colors.cyan}$key${resetColor}: $message';
    
    if (details != null) {
      result += '\n    ${Colors.dim}$details${resetColor}';
    }
    
    return result;
  }
}

/// Results of ARB file validation
class ValidationResult {
  final String fileName;
  final List<ValidationIssue> issues;
  final int totalKeys;
  final bool hasJsonError;
  final String? jsonErrorMessage;

  ValidationResult({
    required this.fileName,
    required this.issues,
    required this.totalKeys,
    this.hasJsonError = false,
    this.jsonErrorMessage,
  });

  List<ValidationIssue> get errors => 
      issues.where((issue) => issue.severity == IssueSeverity.error).toList();
  
  List<ValidationIssue> get warnings => 
      issues.where((issue) => issue.severity == IssueSeverity.warning).toList();

  bool get hasErrors => errors.isNotEmpty || hasJsonError;
  bool get hasWarnings => warnings.isNotEmpty;
  bool get isValid => !hasErrors && !hasWarnings;
}

/// ARB translation validator
class ArbValidator {
  static const String referenceFileName = 'app_en.arb';
  static final RegExp placeholderPattern = RegExp(r'\$\{?[\w.]+\}?');
  static final RegExp metadataKeyPattern = RegExp(r'^@');

  late Map<String, dynamic> referenceArb;
  late Map<String, String> referenceTranslations;
  late String arbDirectory;

  /// Initialize validator with ARB files directory
  Future<void> initialize(String directory) async {
    arbDirectory = directory;
    await _loadReferenceFile();
  }

  /// Load and parse the reference ARB file (app_en.arb)
  Future<void> _loadReferenceFile() async {
    final referenceFile = File('$arbDirectory/$referenceFileName');
    
    if (!await referenceFile.exists()) {
      throw Exception('Reference file $referenceFileName not found in $arbDirectory');
    }

    try {
      final content = await referenceFile.readAsString();
      referenceArb = json.decode(content) as Map<String, dynamic>;
      referenceTranslations = _extractTranslations(referenceArb);
      
      print('${Colors.green}✓${Colors.reset} Loaded reference file: $referenceFileName');
      print('  ${Colors.dim}Found ${referenceTranslations.length} translation keys${Colors.reset}');
    } catch (e) {
      throw Exception('Failed to parse reference file $referenceFileName: $e');
    }
  }

  /// Extract only translation keys (non-metadata) from ARB content
  Map<String, String> _extractTranslations(Map<String, dynamic> arb) {
    final translations = <String, String>{};
    
    arb.forEach((key, value) {
      if (!metadataKeyPattern.hasMatch(key) && key != '@@locale' && value is String) {
        translations[key] = value;
      }
    });
    
    return translations;
  }

  /// Extract placeholder variables from a string
  Set<String> _extractPlaceholders(String text) {
    return placeholderPattern.allMatches(text).map((match) => match.group(0)!).toSet();
  }

  /// Validate a single ARB file against the reference
  Future<ValidationResult> validateFile(String fileName) async {
    final file = File('$arbDirectory/$fileName');
    final issues = <ValidationIssue>[];
    
    // Skip reference file
    if (fileName == referenceFileName) {
      return ValidationResult(
        fileName: fileName,
        issues: [],
        totalKeys: referenceTranslations.length,
      );
    }

    if (!await file.exists()) {
      return ValidationResult(
        fileName: fileName,
        issues: [
          ValidationIssue(
            fileName: fileName,
            key: '',
            message: 'File not found',
            severity: IssueSeverity.error,
          ),
        ],
        totalKeys: 0,
        hasJsonError: true,
        jsonErrorMessage: 'File not found',
      );
    }

    try {
      final content = await file.readAsString();
      
      // Check for JSON syntax errors
      Map<String, dynamic> targetArb;
      try {
        targetArb = json.decode(content) as Map<String, dynamic>;
      } catch (e) {
        return ValidationResult(
          fileName: fileName,
          issues: [],
          totalKeys: 0,
          hasJsonError: true,
          jsonErrorMessage: e.toString(),
        );
      }

      final targetTranslations = _extractTranslations(targetArb);

      // Validate each reference key
      for (final referenceKey in referenceTranslations.keys) {
        if (!targetTranslations.containsKey(referenceKey)) {
          issues.add(ValidationIssue(
            fileName: fileName,
            key: referenceKey,
            message: 'Missing translation key',
            severity: IssueSeverity.error,
            details: 'Reference value: "${referenceTranslations[referenceKey]}"',
          ));
          continue;
        }

        final targetValue = targetTranslations[referenceKey]!;
        final referenceValue = referenceTranslations[referenceKey]!;

        // Check for empty values
        if (targetValue.trim().isEmpty) {
          issues.add(ValidationIssue(
            fileName: fileName,
            key: referenceKey,
            message: 'Empty translation value',
            severity: IssueSeverity.warning,
            details: 'Reference value: "$referenceValue"',
          ));
          continue;
        }

        // Check placeholder variables
        final referencePlaceholders = _extractPlaceholders(referenceValue);
        final targetPlaceholders = _extractPlaceholders(targetValue);

        if (referencePlaceholders.isNotEmpty || targetPlaceholders.isNotEmpty) {
          final missingPlaceholders = referencePlaceholders.difference(targetPlaceholders);
          final extraPlaceholders = targetPlaceholders.difference(referencePlaceholders);

          if (missingPlaceholders.isNotEmpty) {
            issues.add(ValidationIssue(
              fileName: fileName,
              key: referenceKey,
              message: 'Missing placeholder variables: ${missingPlaceholders.join(', ')}',
              severity: IssueSeverity.error,
              details: 'Reference: "$referenceValue"\nTarget: "$targetValue"',
            ));
          }

          if (extraPlaceholders.isNotEmpty) {
            issues.add(ValidationIssue(
              fileName: fileName,
              key: referenceKey,
              message: 'Extra placeholder variables: ${extraPlaceholders.join(', ')}',
              severity: IssueSeverity.error,
              details: 'Reference: "$referenceValue"\nTarget: "$targetValue"',
            ));
          }
        }
      }

      // Check for extra keys in target file
      for (final targetKey in targetTranslations.keys) {
        if (!referenceTranslations.containsKey(targetKey)) {
          issues.add(ValidationIssue(
            fileName: fileName,
            key: targetKey,
            message: 'Extra key not found in reference file',
            severity: IssueSeverity.warning,
            details: 'Value: "${targetTranslations[targetKey]}"',
          ));
        }
      }

      return ValidationResult(
        fileName: fileName,
        issues: issues,
        totalKeys: targetTranslations.length,
      );

    } catch (e) {
      return ValidationResult(
        fileName: fileName,
        issues: [],
        totalKeys: 0,
        hasJsonError: true,
        jsonErrorMessage: e.toString(),
      );
    }
  }

  /// Validate all ARB files in the directory
  Future<List<ValidationResult>> validateAllFiles() async {
    final directory = Directory(arbDirectory);
    final files = await directory
        .list()
        .where((entity) => entity is File && entity.path.endsWith('.arb'))
        .cast<File>()
        .map((file) => file.path.split('/').last)
        .toList();

    files.sort(); // Sort for consistent output

    print('\n${Colors.blue}${Colors.bold}Validating ARB files...${Colors.reset}');
    print('${Colors.dim}Found ${files.length} ARB files to validate${Colors.reset}\n');

    final results = <ValidationResult>[];
    
    for (final fileName in files) {
      stdout.write('  Validating $fileName... ');
      final result = await validateFile(fileName);
      
      if (result.hasJsonError) {
        print('${Colors.red}JSON ERROR${Colors.reset}');
      } else if (result.hasErrors) {
        print('${Colors.red}${result.errors.length} errors${Colors.reset}');
      } else if (result.hasWarnings) {
        print('${Colors.yellow}${result.warnings.length} warnings${Colors.reset}');
      } else {
        print('${Colors.green}✓${Colors.reset}');
      }
      
      results.add(result);
    }

    return results;
  }
}

/// Console reporter for validation results
class ValidationReporter {
  /// Print detailed validation results
  void printResults(List<ValidationResult> results, {bool verbose = true}) {
    var totalErrors = 0;
    var totalWarnings = 0;
    var totalFiles = results.length;
    var validFiles = 0;

    print('\n${Colors.blue}${Colors.bold}Validation Results:${Colors.reset}\n');

    for (final result in results) {
      final errors = result.errors.length;
      final warnings = result.warnings.length;
      
      totalErrors += errors;
      totalWarnings += warnings;
      
      if (result.hasJsonError) {
        totalErrors++;
        print('${Colors.red}${Colors.bold}✗ ${result.fileName}${Colors.reset} - JSON Syntax Error');
        print('  ${Colors.red}${Colors.bold}[ERROR]${Colors.reset} ${result.jsonErrorMessage}');
      } else if (result.hasErrors || result.hasWarnings) {
        final statusColor = result.hasErrors ? Colors.red : Colors.yellow;
        final statusIcon = result.hasErrors ? '✗' : '⚠';
        
        print('$statusColor$statusIcon ${result.fileName}${Colors.reset}');
        
        if (verbose) {
          for (final issue in result.issues) {
            print(issue);
          }
        } else {
          if (errors > 0) {
            print('  ${Colors.red}$errors error(s)${Colors.reset}');
          }
          if (warnings > 0) {
            print('  ${Colors.yellow}$warnings warning(s)${Colors.reset}');
          }
        }
      } else {
        validFiles++;
        print('${Colors.green}✓ ${result.fileName}${Colors.reset} - Valid');
      }
      
      if (result != results.last) {
        print('');
      }
    }

    _printSummary(totalFiles, validFiles, totalErrors, totalWarnings);
  }

  /// Print validation summary
  void _printSummary(int totalFiles, int validFiles, int totalErrors, int totalWarnings) {
    print('\n${Colors.blue}${Colors.bold}Summary:${Colors.reset}');
    print('  Total files: $totalFiles');
    print('  Valid files: ${Colors.green}$validFiles${Colors.reset}');
    
    if (totalErrors > 0) {
      print('  Errors: ${Colors.red}$totalErrors${Colors.reset}');
    }
    
    if (totalWarnings > 0) {
      print('  Warnings: ${Colors.yellow}$totalWarnings${Colors.reset}');
    }

    if (totalErrors == 0 && totalWarnings == 0) {
      print('\n${Colors.green}${Colors.bold}🎉 All translations are valid!${Colors.reset}');
    } else if (totalErrors == 0) {
      print('\n${Colors.yellow}${Colors.bold}⚠ Validation completed with warnings${Colors.reset}');
    } else {
      print('\n${Colors.red}${Colors.bold}❌ Validation failed with errors${Colors.reset}');
    }
  }
}

/// Parse command line arguments
class ArgumentParser {
  bool strict = false;
  bool verbose = true;
  bool help = false;
  String? directory;

  ArgumentParser(List<String> args) {
    for (int i = 0; i < args.length; i++) {
      switch (args[i]) {
        case '--strict':
          strict = true;
          break;
        case '--quiet':
        case '-q':
          verbose = false;
          break;
        case '--help':
        case '-h':
          help = true;
          break;
        case '--directory':
        case '-d':
          if (i + 1 < args.length) {
            directory = args[++i];
          } else {
            throw ArgumentError('--directory requires a value');
          }
          break;
        default:
          if (args[i].startsWith('-')) {
            throw ArgumentError('Unknown option: ${args[i]}');
          }
          directory ??= args[i];
      }
    }
  }

  void printUsage() {
    print('''
${Colors.bold}ARB Translation Validator${Colors.reset}

Validates ARB translation files against a reference file (app_en.arb).

${Colors.bold}Usage:${Colors.reset}
  dart validate_translations.dart [options] [directory]

${Colors.bold}Options:${Colors.reset}
  --strict              Fail on warnings (exit code 1)
  --quiet, -q           Minimal output (errors and summary only)
  --directory, -d DIR   ARB files directory (default: lib/l10n)
  --help, -h            Show this help message

${Colors.bold}Validation Checks:${Colors.reset}
  • Missing translation keys
  • Extra translation keys
  • Placeholder variable mismatches (\$variable, \${variable})
  • JSON syntax errors
  • Empty string values

${Colors.bold}Exit Codes:${Colors.reset}
  0 - Success (no errors, warnings allowed unless --strict)
  1 - Validation failed (errors found or warnings with --strict)
  2 - Script error (file not found, parsing error, etc.)

${Colors.bold}Examples:${Colors.reset}
  dart validate_translations.dart
  dart validate_translations.dart --strict
  dart validate_translations.dart --directory ./assets/l10n
  dart validate_translations.dart --quiet --strict

${Colors.bold}CI Integration:${Colors.reset}
  Use --strict flag for CI to fail on warnings:
  dart validate_translations.dart --strict --quiet
''');
  }
}

/// Main entry point
Future<void> main(List<String> arguments) async {
  try {
    final args = ArgumentParser(arguments);
    
    if (args.help) {
      args.printUsage();
      exit(0);
    }

    // Determine ARB directory
    final arbDirectory = args.directory ?? 'lib/l10n';
    
    // Check if directory exists
    if (!await Directory(arbDirectory).exists()) {
      print('${Colors.red}Error: Directory "$arbDirectory" not found${Colors.reset}');
      print('Use --directory to specify the correct path to ARB files.');
      exit(2);
    }

    print('${Colors.bold}ARB Translation Validator${Colors.reset}');
    print('${Colors.dim}Validating translations in: $arbDirectory${Colors.reset}');

    // Initialize validator
    final validator = ArbValidator();
    await validator.initialize(arbDirectory);

    // Validate all files
    final results = await validator.validateAllFiles();

    // Report results
    final reporter = ValidationReporter();
    reporter.printResults(results, verbose: args.verbose);

    // Determine exit code
    final hasErrors = results.any((r) => r.hasErrors);
    final hasWarnings = results.any((r) => r.hasWarnings);

    if (hasErrors) {
      exit(1);
    } else if (hasWarnings && args.strict) {
      print('\n${Colors.yellow}Exiting with error code due to --strict flag${Colors.reset}');
      exit(1);
    }

    exit(0);

  } catch (e) {
    print('${Colors.red}${Colors.bold}Error:${Colors.reset} $e');
    print('\nUse --help for usage information.');
    exit(2);
  }
}
