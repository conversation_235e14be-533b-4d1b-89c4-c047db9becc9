# Translation Management Tooling Directory

This directory contains Dart scripts for managing ARB localization files.

## Available Tools

### validate_translations.dart ✅

Comprehensive ARB translation validator that verifies the integrity and completeness of translation files.

**Features:**
- ✅ **Missing key detection**: Identifies keys present in reference but missing in translations
- ✅ **Extra key detection**: Finds keys in translations not present in reference
- ✅ **Placeholder validation**: Ensures placeholder variables ($variable, ${variable}) match between files
- ✅ **JSON syntax validation**: Detects malformed JSON files
- ✅ **Empty value detection**: Identifies empty translation strings
- ✅ **Colorized output**: Easy-to-read console reports with color coding
- ✅ **CI/CD integration**: Non-zero exit codes and --strict mode for automated pipelines
- ✅ **Flexible reporting**: Verbose and quiet modes

**Usage:**
```bash
# Basic validation
dart tools/translation_management/validate_translations.dart

# Strict mode (fail on warnings)
dart tools/translation_management/validate_translations.dart --strict

# Quiet mode (minimal output)
dart tools/translation_management/validate_translations.dart --quiet

# Custom directory
dart tools/translation_management/validate_translations.dart --directory ./assets/l10n

# CI integration
dart tools/translation_management/validate_translations.dart --strict --quiet
```

**Exit Codes:**
- `0`: Success (no errors, warnings allowed unless --strict)
- `1`: Validation failed (errors found or warnings with --strict)
- `2`: Script error (file not found, parsing error, etc.)

## Planned Future Components

- Translation Workflow Manager
- Translation Gap Analyzer
- Key Normalization Tool
- Translation Export/Import

