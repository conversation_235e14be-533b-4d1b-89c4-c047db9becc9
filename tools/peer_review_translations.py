#!/usr/bin/env python3
"""
Peer Review System for Translation Verification
==============================================

This script performs comprehensive peer review of translations, verifying:
- Translation accuracy and naturalness
- Tone consistency across languages
- Placeholder integrity (exact preservation)
- Cultural appropriateness
- Technical term consistency
- Unicode encoding correctness

Issues are logged to translations/review_notes.md for manual review and correction.
"""

import csv
import json
import re
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, field
from enum import Enum


class IssueSeverity(Enum):
    """Issue severity levels for translation problems"""
    CRITICAL = "CRITICAL"    # Placeholder corruption, encoding issues
    MAJOR = "MAJOR"         # Accuracy problems, inappropriate tone
    MINOR = "MINOR"         # Minor improvements, style suggestions
    INFO = "INFO"           # General observations


@dataclass
class TranslationIssue:
    """Represents a translation issue found during review"""
    key: str
    language: str
    severity: IssueSeverity
    category: str
    description: str
    original_text: str
    translated_text: str
    suggested_fix: Optional[str] = None
    reviewer_notes: Optional[str] = None


@dataclass
class ReviewStats:
    """Statistics for a language review"""
    language: str
    total_keys: int
    issues_found: int
    critical_issues: int
    major_issues: int
    minor_issues: int
    info_issues: int
    pass_rate: float = field(init=False)
    
    def __post_init__(self):
        self.pass_rate = ((self.total_keys - self.critical_issues - self.major_issues) / self.total_keys) * 100


class TranslationPeerReviewer:
    """Main class for comprehensive translation peer review"""
    
    def __init__(self, translations_dir: str = "translations"):
        self.translations_dir = Path(translations_dir)
        self.issues: List[TranslationIssue] = []
        self.stats: Dict[str, ReviewStats] = {}
        
        # Language-specific validation rules
        self.language_rules = {
            'hi': {'script': 'Devanagari', 'direction': 'ltr', 'sample_chars': ['अ', 'आ', 'इ']},
            'bn': {'script': 'Bengali', 'direction': 'ltr', 'sample_chars': ['অ', 'আ', 'ই']},
            'gu': {'script': 'Gujarati', 'direction': 'ltr', 'sample_chars': ['અ', 'આ', 'ઇ']},
            'as': {'script': 'Assamese', 'direction': 'ltr', 'sample_chars': ['অ', 'আ', 'ই']},
            'kn': {'script': 'Kannada', 'direction': 'ltr', 'sample_chars': ['ಅ', 'ಆ', 'ಇ']},
            'ml': {'script': 'Malayalam', 'direction': 'ltr', 'sample_chars': ['അ', 'ആ', 'ഇ']},
            'mr': {'script': 'Marathi', 'direction': 'ltr', 'sample_chars': ['अ', 'आ', 'इ']},
            'pa': {'script': 'Gurmukhi', 'direction': 'ltr', 'sample_chars': ['ਅ', 'ਆ', 'ਇ']},
            'ta': {'script': 'Tamil', 'direction': 'ltr', 'sample_chars': ['அ', 'ஆ', 'இ']},
            'te': {'script': 'Telugu', 'direction': 'ltr', 'sample_chars': ['అ', 'ఆ', 'ఇ']},
        }
        
        # Railway technical terms that should be transliterated
        self.railway_terms = {
            'train', 'coach', 'berth', 'station', 'chart', 'charting', 
            'depot', 'ehk', 'ca', 'obhs', 'sleeper', 'non-sleeper'
        }
        
        # UI terms that should be translated naturally
        self.ui_terms = {
            'cancel', 'update', 'loading', 'error', 'ok', 'go', 'select',
            'available', 'inside', 'outside', 'today', 'daily'
        }

    def extract_placeholders(self, text: str) -> List[str]:
        """Extract all placeholders from text"""
        # Match {placeholder} and $placeholder patterns
        pattern = r'(\{[^}]+\}|\$\w+)'
        return re.findall(pattern, text)

    def validate_placeholder_integrity(self, key: str, original: str, translated: str, language: str) -> None:
        """Validate that placeholders are preserved exactly"""
        original_placeholders = self.extract_placeholders(original)
        translated_placeholders = self.extract_placeholders(translated)
        
        # Check if placeholders match exactly
        if set(original_placeholders) != set(translated_placeholders):
            missing = set(original_placeholders) - set(translated_placeholders)
            extra = set(translated_placeholders) - set(original_placeholders)
            
            description = f"Placeholder mismatch:"
            if missing:
                description += f" Missing: {', '.join(missing)}"
            if extra:
                description += f" Extra: {', '.join(extra)}"
            
            self.issues.append(TranslationIssue(
                key=key,
                language=language,
                severity=IssueSeverity.CRITICAL,
                category="placeholder_integrity",
                description=description,
                original_text=original,
                translated_text=translated,
                suggested_fix=f"Ensure exact placeholder preservation: {', '.join(original_placeholders)}"
            ))

    def validate_encoding(self, key: str, text: str, language: str) -> None:
        """Validate Unicode encoding and character set"""
        try:
            # Check if text can be properly encoded/decoded
            text.encode('utf-8').decode('utf-8')
        except UnicodeError:
            self.issues.append(TranslationIssue(
                key=key,
                language=language,
                severity=IssueSeverity.CRITICAL,
                category="encoding",
                description="Unicode encoding error",
                original_text="",
                translated_text=text,
                suggested_fix="Fix character encoding to proper UTF-8"
            ))
            return
        
        # Check for appropriate script usage
        if language in self.language_rules:
            script_chars = self.language_rules[language]['sample_chars']
            # Allow mixed scripts for technical terms, but flag if no native script
            has_native_script = any(char in text for char in script_chars)
            has_only_english = text.replace(' ', '').isascii()
            
            if has_only_english and len(text) > 5:  # Short terms like "OK" are acceptable
                self.issues.append(TranslationIssue(
                    key=key,
                    language=language,
                    severity=IssueSeverity.MAJOR,
                    category="script_usage",
                    description=f"Text appears to be untranslated (only English/ASCII)",
                    original_text="",
                    translated_text=text,
                    suggested_fix=f"Provide proper {language} translation"
                ))

    def validate_length_reasonableness(self, key: str, original: str, translated: str, language: str) -> None:
        """Check if translation length is reasonable"""
        original_len = len(original.strip())
        translated_len = len(translated.strip())
        
        if original_len == 0 or translated_len == 0:
            return
        
        ratio = translated_len / original_len
        
        # Flag extreme length differences
        if ratio > 3.0:
            self.issues.append(TranslationIssue(
                key=key,
                language=language,
                severity=IssueSeverity.MINOR,
                category="length",
                description=f"Translation significantly longer than original (ratio: {ratio:.2f})",
                original_text=original,
                translated_text=translated,
                reviewer_notes="Review if translation is unnecessarily verbose"
            ))
        elif ratio < 0.3:
            self.issues.append(TranslationIssue(
                key=key,
                language=language,
                severity=IssueSeverity.MINOR,
                category="length",
                description=f"Translation significantly shorter than original (ratio: {ratio:.2f})",
                original_text=original,
                translated_text=translated,
                reviewer_notes="Review if translation is complete"
            ))

    def validate_technical_terms(self, key: str, original: str, translated: str, language: str) -> None:
        """Validate proper handling of technical terms"""
        original_lower = original.lower()
        translated_lower = translated.lower()
        
        # Check railway terms (should be transliterated)
        for term in self.railway_terms:
            if term in original_lower:
                # Flag if railway term is translated instead of transliterated
                if term not in translated_lower:
                    # This might be OK if properly transliterated
                    self.issues.append(TranslationIssue(
                        key=key,
                        language=language,
                        severity=IssueSeverity.INFO,
                        category="technical_terms",
                        description=f"Railway term '{term}' handling - verify transliteration",
                        original_text=original,
                        translated_text=translated,
                        reviewer_notes="Confirm railway terms are properly transliterated"
                    ))

    def validate_consistency(self, key: str, translated: str, language: str) -> None:
        """Check for consistency issues within translation"""
        # Flag mixed language usage (too much English in translation)
        words = translated.split()
        english_words = [w for w in words if w.replace(':', '').replace(',', '').isascii() and len(w) > 2]
        
        if len(english_words) > len(words) * 0.5:  # More than 50% English words
            self.issues.append(TranslationIssue(
                key=key,
                language=language,
                severity=IssueSeverity.MAJOR,
                category="consistency",
                description="Translation contains too many English words",
                original_text="",
                translated_text=translated,
                suggested_fix="Translate more terms to native language",
                reviewer_notes="Review mixed language usage"
            ))

    def review_translation_file(self, file_path: Path) -> None:
        """Review a single translation CSV file"""
        language = file_path.stem.split('_')[1]  # Extract language code
        
        print(f"Reviewing {language} translations...")
        
        total_keys = 0
        issues_before = len(self.issues)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                
                for row in reader:
                    key = row['key']
                    original = row['english_text']
                    translated = row['translated_text']
                    
                    total_keys += 1
                    
                    # Run all validation checks
                    self.validate_placeholder_integrity(key, original, translated, language)
                    self.validate_encoding(key, translated, language)
                    self.validate_length_reasonableness(key, original, translated, language)
                    self.validate_technical_terms(key, original, translated, language)
                    self.validate_consistency(key, translated, language)
                    
        except Exception as e:
            self.issues.append(TranslationIssue(
                key="FILE_ERROR",
                language=language,
                severity=IssueSeverity.CRITICAL,
                category="file_access",
                description=f"Error reading file: {str(e)}",
                original_text="",
                translated_text="",
                suggested_fix="Fix file format or encoding issues"
            ))
        
        # Calculate stats for this language
        language_issues = [issue for issue in self.issues[issues_before:] if issue.language == language]
        critical_count = sum(1 for issue in language_issues if issue.severity == IssueSeverity.CRITICAL)
        major_count = sum(1 for issue in language_issues if issue.severity == IssueSeverity.MAJOR)
        minor_count = sum(1 for issue in language_issues if issue.severity == IssueSeverity.MINOR)
        info_count = sum(1 for issue in language_issues if issue.severity == IssueSeverity.INFO)
        
        self.stats[language] = ReviewStats(
            language=language,
            total_keys=total_keys,
            issues_found=len(language_issues),
            critical_issues=critical_count,
            major_issues=major_count,
            minor_issues=minor_count,
            info_issues=info_count
        )

    def review_all_translations(self) -> None:
        """Review all translation files in the translations directory"""
        csv_files = list(self.translations_dir.glob("attendance_*.csv"))
        
        if not csv_files:
            print("No translation CSV files found!")
            return
        
        print(f"Found {len(csv_files)} translation files to review")
        
        for file_path in sorted(csv_files):
            self.review_translation_file(file_path)

    def generate_review_report(self) -> str:
        """Generate comprehensive review report"""
        report = []
        report.append("# Translation Peer Review Report")
        report.append("=" * 50)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Overall summary
        total_keys = sum(stats.total_keys for stats in self.stats.values())
        total_issues = len(self.issues)
        critical_issues = sum(1 for issue in self.issues if issue.severity == IssueSeverity.CRITICAL)
        major_issues = sum(1 for issue in self.issues if issue.severity == IssueSeverity.MAJOR)
        
        report.append("## Overall Summary")
        report.append(f"- Total Keys Reviewed: {total_keys}")
        report.append(f"- Total Issues Found: {total_issues}")
        report.append(f"- Critical Issues: {critical_issues}")
        report.append(f"- Major Issues: {major_issues}")
        report.append("")
        
        # Language-specific stats
        report.append("## Language Review Status")
        report.append("| Language | Total Keys | Issues | Critical | Major | Pass Rate |")
        report.append("|----------|------------|--------|----------|-------|-----------|")
        
        for lang in sorted(self.stats.keys()):
            stats = self.stats[lang]
            report.append(f"| {lang.upper()} | {stats.total_keys} | {stats.issues_found} | "
                         f"{stats.critical_issues} | {stats.major_issues} | {stats.pass_rate:.1f}% |")
        
        report.append("")
        
        # Issues by category
        if self.issues:
            report.append("## Issues by Category")
            
            categories = {}
            for issue in self.issues:
                if issue.category not in categories:
                    categories[issue.category] = []
                categories[issue.category].append(issue)
            
            for category, issues in sorted(categories.items()):
                report.append(f"### {category.replace('_', ' ').title()}")
                
                for issue in issues:
                    report.append(f"- **{issue.severity.value}** [{issue.language.upper()}] {issue.key}")
                    report.append(f"  - {issue.description}")
                    if issue.suggested_fix:
                        report.append(f"  - **Suggested Fix**: {issue.suggested_fix}")
                    if issue.reviewer_notes:
                        report.append(f"  - **Notes**: {issue.reviewer_notes}")
                    report.append("")
        
        return "\n".join(report)

    def save_review_notes(self) -> None:
        """Save review notes to markdown file"""
        notes_path = self.translations_dir / "review_notes.md"
        
        with open(notes_path, 'w', encoding='utf-8') as f:
            f.write(self.generate_review_report())
        
        print(f"Review notes saved to: {notes_path}")

    def run_review(self) -> None:
        """Run complete peer review process"""
        print("Starting Translation Peer Review...")
        print("=" * 50)
        
        self.review_all_translations()
        self.save_review_notes()
        
        # Print summary
        print("\nReview Summary:")
        print(f"- Total languages reviewed: {len(self.stats)}")
        print(f"- Total issues found: {len(self.issues)}")
        
        critical_count = sum(1 for issue in self.issues if issue.severity == IssueSeverity.CRITICAL)
        major_count = sum(1 for issue in self.issues if issue.severity == IssueSeverity.MAJOR)
        
        if critical_count > 0:
            print(f"- ⚠️  CRITICAL issues requiring immediate attention: {critical_count}")
        if major_count > 0:
            print(f"- ⚠️  MAJOR issues requiring review: {major_count}")
        
        print("\nReview complete! Check translations/review_notes.md for detailed findings.")


def main():
    """Main entry point"""
    reviewer = TranslationPeerReviewer()
    reviewer.run_review()


if __name__ == "__main__":
    main()
