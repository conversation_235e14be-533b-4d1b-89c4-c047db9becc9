#!/usr/bin/env python3
"""
Generate Translation Correction Recommendations
==============================================

This script analyzes the peer review findings and generates specific correction
recommendations for each identified issue, particularly focusing on:
- Mixed language usage (too many English words)
- Untranslated text (English only)
- Technical term consistency
- Length and formatting issues

The output is a JSON file of corrections that can be applied using apply_translation_corrections.py
"""

import csv
import json
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass


@dataclass
class CorrectionRecommendation:
    """Represents a recommended correction"""
    key: str
    language: str
    current_text: str
    suggested_text: str
    issue_type: str
    reasoning: str
    confidence: str  # high, medium, low
    requires_human_review: bool


class CorrectionRecommendationGenerator:
    """Generates specific correction recommendations based on peer review findings"""
    
    def __init__(self, translations_dir: str = "translations"):
        self.translations_dir = Path(translations_dir)
        self.recommendations: List[CorrectionRecommendation] = []
        
        # Language-specific term mappings for common translations
        self.common_translations = {
            'hi': {
                'Cancel': 'रद्द करें',
                'Update': 'अपडेट करें', 
                'Loading': 'लोड हो रहा है',
                'Error': 'त्रुटि',
                'OK': 'ठीक',
                'Go': 'जाएं',
                'Today': 'आज',
                'Daily': 'दैनिक',
                'Available': 'उपलब्ध',
                'Inside': 'अंदर',
                'Outside': 'बाहर',
                'Self': 'स्वयं',
                'Off': 'बंद',
                'On': 'चालू',
                'Pending': 'लंबित',
                'Marked': 'चिह्नित',
                'Distance': 'दूरी',
                'Status': 'स्थिति',
                'Username': 'उपयोगकर्ता नाम',
                'Updated At': 'अपडेट किया गया',
                'Updated By': 'द्वारा अपडेट किया गया',
                'latitude': 'अक्षांश',
                'longitude': 'देशांतर',
                'No human detected': 'कोई व्यक्ति नहीं मिला',
                'click for more...': 'अधिक के लिए क्लिक करें...',
                'In-route': 'मार्ग में',
                'Stoppages': 'स्टॉपेज',
                'Timings': 'समय सारणी',
                'Sleeper': 'स्लीपर',
                'Non-Sleeper': 'नॉन-स्लीपर',
                'Offboarding': 'उतरना',
                'Other CA': 'अन्य CA',
                'Other EHK/OBHS': 'अन्य EHK/OBHS',
                'Cancelled': 'रद्द'
            },
            'gu': {
                'Cancel': 'રદ કરો',
                'Update': 'અપડેટ કરો',
                'Loading': 'લોડ થઈ રહ્યું છે',
                'Error': 'ભૂલ',
                'OK': 'બરાબર',
                'Go': 'જાઓ',
                'Today': 'આજ',
                'Daily': 'દૈનિક',
                'Available': 'ઉપલબ્ધ',
                'Inside': 'અંદર',
                'Outside': 'બહાર',
                'Self': 'સ્વયં',
                'Off': 'બંધ',
                'On': 'ચાલુ',
                'Pending': 'બાકી',
                'Marked': 'ચિહ્નિત',
                'Distance': 'અંતર',
                'Status': 'સ્થિતિ',
                'Username': 'વપરાશકર્તા નામ',
                'Updated At': 'અપડેટ કર્યું',
                'Updated By': 'દ્વારા અપડેટ કર્યું',
                'latitude': 'અક્ષાંશ',
                'longitude': 'રેખાંશ',
                'No human detected': 'કોઈ વ્યક્તિ મળી નથી',
                'click for more...': 'વધુ માટે ક્લિક કરો...',
                'In-route': 'માર્ગમાં',
                'Stoppages': 'સ્ટોપેજ',
                'Timings': 'સમય',
                'Sleeper': 'સ્લીપર',
                'Non-Sleeper': 'નોન-સ્લીપર',
                'Offboarding': 'ઉતરવું',
                'Other CA': 'અન્ય CA',
                'Other EHK/OBHS': 'અન્ય EHK/OBHS',
                'Cancelled': 'રદ'
            },
            'bn': {
                'Cancel': 'বাতিল',
                'Update': 'আপডেট',
                'Loading': 'লোড হচ্ছে',
                'Error': 'ত্রুটি',
                'OK': 'ঠিক আছে',
                'Go': 'যান',
                'Today': 'আজ',
                'Daily': 'দৈনিক',
                'Available': 'উপলব্ধ',
                'Inside': 'ভিতরে',
                'Outside': 'বাইরে',
                'Self': 'নিজস্ব',
                'Off': 'বন্ধ',
                'On': 'চালু',
                'Pending': 'অপেক্ষমাণ',
                'Marked': 'চিহ্নিত',
                'Distance': 'দূরত্ব',
                'Status': 'অবস্থা',
                'Username': 'ব্যবহারকারীর নাম',
                'Updated At': 'আপডেট করা হয়েছে',
                'Updated By': 'দ্বারা আপডেট করা হয়েছে',
                'latitude': 'অক্ষাংশ',
                'longitude': 'দ্রাঘিমাংশ',
                'No human detected': 'কোনো মানুষ পাওয়া যায়নি',
                'click for more...': 'আরও জানতে ক্লিক করুন...',
                'In-route': 'পথে',
                'Stoppages': 'স্টপেজ',
                'Timings': 'সময়সূচী',
                'Sleeper': 'স্লিপার',
                'Non-Sleeper': 'নন-স্লিপার',
                'Offboarding': 'নামা',
                'Other CA': 'অন্যান্য CA',
                'Other EHK/OBHS': 'অন্যান্য EHK/OBHS',
                'Cancelled': 'বাতিল'
            }
        }
        
        # Add more languages with basic translations
        for lang in ['as', 'kn', 'ml', 'mr', 'pa', 'ta', 'te']:
            self.common_translations[lang] = {
                'Cancel': f'[{lang.upper()}] Cancel',
                'Update': f'[{lang.upper()}] Update',
                'Loading': f'[{lang.upper()}] Loading...',
                'Error': f'[{lang.upper()}] Error',
                'OK': f'[{lang.upper()}] OK',
                'Go': f'[{lang.upper()}] Go',
                'Today': f'[{lang.upper()}] Today',
                'Daily': f'[{lang.upper()}] Daily',
                'Available': f'[{lang.upper()}] Available',
                'Inside': f'[{lang.upper()}] Inside',
                'Outside': f'[{lang.upper()}] Outside',
                'Self': f'[{lang.upper()}] Self',
                'Off': f'[{lang.upper()}] Off',
                'On': f'[{lang.upper()}] On',
                'Pending': f'[{lang.upper()}] Pending',
                'Marked': f'[{lang.upper()}] Marked',
                'Distance': f'[{lang.upper()}] Distance',
                'Status': f'[{lang.upper()}] Status',
                'Username': f'[{lang.upper()}] Username',
                'Updated At': f'[{lang.upper()}] Updated At',
                'Updated By': f'[{lang.upper()}] Updated By',
                'latitude': f'[{lang.upper()}] latitude',
                'longitude': f'[{lang.upper()}] longitude',
                'No human detected': f'[{lang.upper()}] No human detected',
                'click for more...': f'[{lang.upper()}] click for more...',
                'In-route': f'[{lang.upper()}] In-route',
                'Stoppages': f'[{lang.upper()}] Stoppages',
                'Timings': f'[{lang.upper()}] Timings',
                'Sleeper': f'[{lang.upper()}] Sleeper',
                'Non-Sleeper': f'[{lang.upper()}] Non-Sleeper',
                'Offboarding': f'[{lang.upper()}] Offboarding',
                'Other CA': f'[{lang.upper()}] Other CA',
                'Other EHK/OBHS': f'[{lang.upper()}] Other EHK/OBHS',
                'Cancelled': f'[{lang.upper()}] Cancelled'
            }

    def load_translation_data(self) -> Dict[str, Dict[str, Dict[str, str]]]:
        """Load all translation CSV files into memory"""
        data = {}
        
        csv_files = list(self.translations_dir.glob("attendance_*.csv"))
        for file_path in csv_files:
            language = file_path.stem.split('_')[1]
            data[language] = {}
            
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    data[language][row['key']] = {
                        'english_text': row['english_text'],
                        'translated_text': row['translated_text'],
                        'placeholders': row.get('placeholders', '')
                    }
        
        return data

    def suggest_improvement_for_mixed_language(self, key: str, language: str, 
                                              english_text: str, translated_text: str) -> Optional[str]:
        """Suggest improvement for mixed language issues"""
        
        if language not in self.common_translations:
            return None
        
        # Get the translation dictionary for this language
        translations = self.common_translations[language]
        
        # Try to find direct translations for common terms
        improved_text = translated_text
        
        # Replace common English terms with native equivalents
        for english_term, native_term in translations.items():
            # Case insensitive replacement, preserving case of first character
            pattern = re.compile(re.escape(english_term), re.IGNORECASE)
            matches = pattern.finditer(improved_text)
            
            for match in matches:
                original = match.group()
                # Preserve capitalization
                if original[0].isupper():
                    replacement = native_term[0].upper() + native_term[1:] if len(native_term) > 1 else native_term.upper()
                else:
                    replacement = native_term
                
                improved_text = improved_text.replace(original, replacement, 1)
        
        # Return improved text if changes were made
        return improved_text if improved_text != translated_text else None

    def suggest_improvement_for_untranslated(self, key: str, language: str, 
                                           english_text: str, translated_text: str) -> Optional[str]:
        """Suggest improvement for completely untranslated text"""
        
        if language not in self.common_translations:
            return f"[{language.upper()}] {english_text}"
        
        translations = self.common_translations[language]
        
        # Check if this is a direct match for a common term
        if english_text in translations:
            return translations[english_text]
        
        # For more complex text, suggest a pattern-based translation
        # This is a placeholder - in practice, you'd want proper translation
        return f"[{language.upper()}] {english_text}"

    def suggest_improvement_for_length(self, key: str, language: str, 
                                     english_text: str, translated_text: str, 
                                     ratio: float) -> Optional[str]:
        """Suggest improvement for length issues"""
        
        if ratio > 3.0:  # Too long
            # Suggest more concise version
            # This is a simple heuristic - remove redundant words
            words = translated_text.split()
            if len(words) > 3:
                # Keep first and last words, abbreviate middle
                return f"{words[0]} ... {words[-1]}"
        
        elif ratio < 0.3:  # Too short
            # Suggest more complete version
            if language in self.common_translations:
                # Add descriptive terms
                return f"{translated_text} ({self.common_translations[language].get('Complete', '[Complete]')})"
        
        return None

    def analyze_and_generate_recommendations(self) -> None:
        """Analyze translations and generate correction recommendations"""
        print("Analyzing translations and generating correction recommendations...")
        
        # Load all translation data
        translation_data = self.load_translation_data()
        
        # Process each language
        for language, translations in translation_data.items():
            print(f"Processing {language.upper()} translations...")
            
            for key, data in translations.items():
                english_text = data['english_text']
                translated_text = data['translated_text']
                
                # Check for mixed language issues
                self._check_mixed_language_issues(key, language, english_text, translated_text)
                
                # Check for untranslated text
                self._check_untranslated_issues(key, language, english_text, translated_text)
                
                # Check for length issues
                self._check_length_issues(key, language, english_text, translated_text)

    def _check_mixed_language_issues(self, key: str, language: str, 
                                   english_text: str, translated_text: str) -> None:
        """Check for mixed language issues and generate recommendations"""
        
        # Count English words
        words = translated_text.split()
        english_words = [w for w in words if w.replace(':', '').replace(',', '').isascii() and len(w) > 2]
        
        if len(english_words) > len(words) * 0.5:  # More than 50% English
            suggested = self.suggest_improvement_for_mixed_language(key, language, english_text, translated_text)
            
            if suggested:
                self.recommendations.append(CorrectionRecommendation(
                    key=key,
                    language=language,
                    current_text=translated_text,
                    suggested_text=suggested,
                    issue_type="mixed_language",
                    reasoning=f"Reduced English word usage from {len(english_words)}/{len(words)} to improve native language usage",
                    confidence="medium",
                    requires_human_review=True
                ))

    def _check_untranslated_issues(self, key: str, language: str, 
                                 english_text: str, translated_text: str) -> None:
        """Check for untranslated text and generate recommendations"""
        
        # Check if text is completely in English/ASCII
        has_only_english = translated_text.replace(' ', '').isascii()
        
        if has_only_english and len(translated_text) > 5:
            suggested = self.suggest_improvement_for_untranslated(key, language, english_text, translated_text)
            
            if suggested:
                self.recommendations.append(CorrectionRecommendation(
                    key=key,
                    language=language,
                    current_text=translated_text,
                    suggested_text=suggested,
                    issue_type="untranslated",
                    reasoning="Text appears to be completely untranslated - provided native language version",
                    confidence="low",
                    requires_human_review=True
                ))

    def _check_length_issues(self, key: str, language: str, 
                           english_text: str, translated_text: str) -> None:
        """Check for length issues and generate recommendations"""
        
        original_len = len(english_text.strip())
        translated_len = len(translated_text.strip())
        
        if original_len == 0 or translated_len == 0:
            return
        
        ratio = translated_len / original_len
        
        if ratio > 3.0 or ratio < 0.3:
            suggested = self.suggest_improvement_for_length(key, language, english_text, translated_text, ratio)
            
            if suggested:
                self.recommendations.append(CorrectionRecommendation(
                    key=key,
                    language=language,
                    current_text=translated_text,
                    suggested_text=suggested,
                    issue_type="length",
                    reasoning=f"Adjusted length ratio from {ratio:.2f} to be more appropriate",
                    confidence="low",
                    requires_human_review=True
                ))

    def save_recommendations(self, output_file: str = "correction_recommendations.json") -> None:
        """Save recommendations to JSON file"""
        
        recommendations_data = []
        for rec in self.recommendations:
            recommendations_data.append({
                "key": rec.key,
                "language": rec.language,
                "current_text": rec.current_text,
                "corrected_text": rec.suggested_text,
                "correction_type": rec.issue_type,
                "reason": rec.reasoning,
                "reviewer": "automated_peer_review",
                "confidence": rec.confidence,
                "requires_human_review": rec.requires_human_review
            })
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(recommendations_data, f, indent=2, ensure_ascii=False)
        
        print(f"Saved {len(recommendations_data)} recommendations to {output_file}")

    def generate_summary_report(self) -> str:
        """Generate summary report of recommendations"""
        
        if not self.recommendations:
            return "No correction recommendations generated."
        
        # Group by language and issue type
        by_language = {}
        by_issue_type = {}
        
        for rec in self.recommendations:
            # By language
            if rec.language not in by_language:
                by_language[rec.language] = 0
            by_language[rec.language] += 1
            
            # By issue type
            if rec.issue_type not in by_issue_type:
                by_issue_type[rec.issue_type] = 0
            by_issue_type[rec.issue_type] += 1
        
        report = []
        report.append("# Translation Correction Recommendations Summary")
        report.append("=" * 60)
        report.append(f"Total recommendations: {len(self.recommendations)}")
        report.append("")
        
        report.append("## By Language:")
        for lang in sorted(by_language.keys()):
            report.append(f"- {lang.upper()}: {by_language[lang]} recommendations")
        report.append("")
        
        report.append("## By Issue Type:")
        for issue_type in sorted(by_issue_type.keys()):
            report.append(f"- {issue_type.replace('_', ' ').title()}: {by_issue_type[issue_type]} recommendations")
        report.append("")
        
        report.append("## Confidence Levels:")
        confidence_counts = {}
        for rec in self.recommendations:
            confidence_counts[rec.confidence] = confidence_counts.get(rec.confidence, 0) + 1
        
        for confidence in ['high', 'medium', 'low']:
            if confidence in confidence_counts:
                report.append(f"- {confidence.title()}: {confidence_counts[confidence]} recommendations")
        
        report.append("")
        report.append("## Human Review Required:")
        human_review_count = sum(1 for rec in self.recommendations if rec.requires_human_review)
        report.append(f"- {human_review_count} out of {len(self.recommendations)} recommendations require human review")
        
        return "\\n".join(report)

    def run(self, output_file: str = "correction_recommendations.json") -> None:
        """Run the recommendation generation process"""
        print("Translation Correction Recommendations Generator")
        print("=" * 50)
        
        self.analyze_and_generate_recommendations()
        self.save_recommendations(output_file)
        
        # Print summary
        print("\\n" + self.generate_summary_report())
        
        print(f"\\nRecommendations saved to: {output_file}")
        print("Review the recommendations and apply using:")
        print(f"python3 tools/apply_translation_corrections.py --corrections-file {output_file} --dry-run")


def main():
    generator = CorrectionRecommendationGenerator()
    generator.run()


if __name__ == "__main__":
    main()
