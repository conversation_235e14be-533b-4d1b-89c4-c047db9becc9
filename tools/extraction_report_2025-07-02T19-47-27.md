# RailOps String Extraction Report
Generated: 2025-07-02 19:47:27.323008

## Summary
- **Files processed**: 114
- **Total strings extracted**: 839

## Strings by Category
- **text_widgets**: 498 strings
- **app_bar_titles**: 15 strings
- **form_labels**: 212 strings
- **button_labels**: 49 strings
- **snackbar_messages**: 65 strings
- **dialog_content**: 0 strings

## Files with Extractable Strings
- `../lib/core/comman_widgets/input_fields/otp_boxes.dart` (1 strings)
- `../lib/utils/permission_handler_service.dart` (4 strings)
- `../lib/utils/show_location_permission_disclosure.dart` (6 strings)
- `../lib/screens/attendance/widget/delete_button.dart` (3 strings)
- `../lib/screens/attendance/attendance_screen.dart` (27 strings)
- `../lib/screens/attendance/image_upload.dart` (23 strings)
- `../lib/screens/attendance/attendance_details.dart` (10 strings)
- `../lib/screens/pdf_screen/pdf_screen.dart` (4 strings)
- `../lib/screens/pdf_screen/widgets/date_select.dart` (1 strings)
- `../lib/screens/pdf_screen/widgets/pdf_buttons.dart` (7 strings)
- `../lib/screens/rail_sathi_qr/rail_sathi_qr_screen.dart` (2 strings)
- `../lib/screens/enable_disable_user/enable_disable_user.dart` (8 strings)
- `../lib/screens/add_user/add_user_from.dart` (12 strings)
- `../lib/screens/add_user/widget/new_user_email_field.dart` (2 strings)
- `../lib/screens/user_screen/form/signup_form.dart` (13 strings)
- `../lib/screens/user_screen/form/forgot_password_form.dart` (2 strings)
- `../lib/screens/user_screen/form/login_form.dart` (1 strings)
- `../lib/screens/user_screen/form/otp_form.dart` (2 strings)
- `../lib/screens/user_screen/login_mobile_screen.dart` (4 strings)
- `../lib/screens/user_screen/widgets/otp/send_otp_button.dart` (1 strings)
- `../lib/screens/user_screen/widgets/otp/otp_boxes.dart` (1 strings)
- `../lib/screens/user_screen/widgets/otp/verify_button.dart` (1 strings)
- `../lib/screens/user_screen/widgets/mobile_login/mobile_field.dart` (1 strings)
- `../lib/screens/user_screen/widgets/login_page/google_login_button.dart` (1 strings)
- `../lib/screens/user_screen/widgets/login_page/password_field.dart` (1 strings)
- `../lib/screens/user_screen/widgets/login_page/mobile_number_field.dart` (1 strings)
- `../lib/screens/user_screen/widgets/login_page/signup_button.dart` (1 strings)
- `../lib/screens/user_screen/widgets/signup_page/train_number_dropdown.dart` (1 strings)
- `../lib/screens/user_screen/widgets/signup_page/first_name_field.dart` (1 strings)
- `../lib/screens/user_screen/widgets/signup_page/zone_dropdown.dart` (2 strings)
- `../lib/screens/user_screen/widgets/signup_page/emp_number_field.dart` (1 strings)
- `../lib/screens/user_screen/widgets/signup_page/depot_dropdown.dart` (1 strings)
- `../lib/screens/user_screen/widgets/signup_page/last_name_field.dart` (1 strings)
- `../lib/screens/user_screen/widgets/signup_page/re_password.dart` (1 strings)
- `../lib/screens/user_screen/widgets/signup_page/email_field.dart` (3 strings)
- `../lib/screens/user_screen/widgets/signup_page/division_dropdown.dart` (2 strings)
- `../lib/screens/user_screen/widgets/signup_page/coach_dropdown.dart` (1 strings)
- `../lib/screens/user_screen/widgets/signup_page/mobile_field_signup.dart` (3 strings)
- `../lib/screens/user_screen/widgets/signup_page/middle_name_field.dart` (1 strings)
- `../lib/screens/pnr_screen/pnr_status.dart` (5 strings)
- `../lib/screens/map_screen/map_screen.dart` (9 strings)
- `../lib/screens/train_details/train_details_screen.dart` (18 strings)
- `../lib/screens/train_details/widgets/train_popup.dart` (2 strings)
- `../lib/screens/train_details/widgets/station_details_table.dart` (1 strings)
- `../lib/screens/train_details/widgets/train_details_table.dart` (7 strings)
- `../lib/screens/train_details/widgets/train_filter_with_station.dart` (5 strings)
- `../lib/screens/update_user/widget/update_user_whatsapp_field.dart` (1 strings)
- `../lib/screens/update_user/widget/update_user_email_field.dart` (2 strings)
- `../lib/screens/update_user/widget/update_secondary_phone_field.dart` (2 strings)
- `../lib/screens/update_user/widget/update_user_mobile_field.dart` (2 strings)
- `../lib/screens/update_user/update_user_screen.dart` (1 strings)
- `../lib/screens/edit_train/form/edit_train_form.dart` (13 strings)
- `../lib/screens/edit_train/add_train_screen.dart` (20 strings)
- `../lib/screens/edit_train/edit_train_screen.dart` (12 strings)
- `../lib/screens/edit_train/widgets/coaches_input_field.dart` (3 strings)
- `../lib/screens/edit_train/widgets/frequency_checkboxes.dart` (1 strings)
- `../lib/screens/edit_train/widgets/train_type.dart` (1 strings)
- `../lib/screens/edit_train/widgets/stoppages_sequence_input.dart` (2 strings)
- `../lib/screens/edit_train/widgets/custom_drop_down.dart` (1 strings)
- `../lib/screens/edit_train/widgets/stoppage_sequence.dart` (2 strings)
- `../lib/screens/edit_train/widgets/train_name.dart` (1 strings)
- `../lib/screens/edit_train/widgets/frequency.dart` (1 strings)
- `../lib/screens/edit_train/widgets/coach_in_sequence.dart` (4 strings)
- `../lib/screens/mcc_to_obhs_handover/mcc_to_obhs_handover_screen.dart` (8 strings)
- `../lib/screens/mcc_to_obhs_handover/widget/CoachHandoverImageUpload.dart` (30 strings)
- `../lib/screens/request_user_management/requested_user_screen.dart` (3 strings)
- `../lib/screens/request_user_management/widgets/user_request_list_tile.dart` (2 strings)
- `../lib/screens/request_user_management/widgets/new_user_request_list_title.dart` (2 strings)
- `../lib/screens/request_user_management/widgets/new_user_request_details_screen.dart` (13 strings)
- `../lib/screens/request_user_management/widgets/update_request_list_tile.dart` (2 strings)
- `../lib/screens/request_user_management/widgets/update_request_details_screen.dart` (11 strings)
- `../lib/screens/rail_sathi/view_complaints.dart` (46 strings)
- `../lib/screens/rail_sathi/write_complaint.dart` (29 strings)
- `../lib/screens/upload_screen/upload_screen.dart` (1 strings)
- `../lib/screens/upload_screen/widgets/upload_widget.dart` (8 strings)
- `../lib/screens/trip_report/widget/IssueScreen.dart` (30 strings)
- `../lib/screens/trip_report/widget/CoachIssueImageUpload.dart` (32 strings)
- `../lib/screens/trip_report/widget/TripReportFilePreview.dart` (10 strings)
- `../lib/screens/trip_report/widget/CoachIssueStatus.dart` (6 strings)
- `../lib/screens/trip_report/widget/StatusSelectionModal.dart` (14 strings)
- `../lib/screens/trip_report/trip_report_screen.dart` (9 strings)
- `../lib/screens/feedback_screens/passenger_feedback_screen.dart` (30 strings)
- `../lib/screens/feedback_screens/rm_feedback_screen.dart` (34 strings)
- `../lib/screens/feedback_screens/widgets/rm_review_feedback_dailogue.dart` (33 strings)
- `../lib/screens/feedback_screens/widgets/review_feedback.dart` (9 strings)
- `../lib/screens/feedback_screens/widgets/normal_review_feedback_dailogue.dart` (23 strings)
- `../lib/screens/assign_obhs/assign_obhs_screen.dart` (7 strings)
- `../lib/screens/assign_obhs/widgets/assign_obhs_filters.dart` (4 strings)
- `../lib/screens/assign_obhs/widgets/assign_obhs_table.dart` (10 strings)
- `../lib/screens/pages/image_detail_page.dart` (10 strings)
- `../lib/screens/profile_screen/change_email_screen.dart` (1 strings)
- `../lib/screens/profile_screen/add_train_screen.dart` (3 strings)
- `../lib/screens/profile_screen/change_whatsapp_screen.dart` (1 strings)
- `../lib/screens/profile_screen/change_password_screen.dart` (1 strings)
- `../lib/screens/profile_screen/edit_profile_screen.dart` (1 strings)
- `../lib/screens/profile_screen/change_mobile_screen.dart` (1 strings)
- `../lib/screens/profile_screen/widgets/edit_profile_form.dart` (17 strings)
- `../lib/screens/profile_screen/widgets/add_train_form.dart` (5 strings)
- `../lib/screens/profile_screen/widgets/change_whatsapp_form.dart` (5 strings)
- `../lib/screens/profile_screen/widgets/change_mobile_form.dart` (5 strings)
- `../lib/screens/profile_screen/widgets/change_email_modal.dart` (15 strings)
- `../lib/screens/profile_screen/widgets/change_password_form.dart` (5 strings)
- `../lib/screens/profile_screen/widgets/change_email_form.dart` (4 strings)
- `../lib/screens/assign_ehk_ca_screen/assign_ehk_ca_screen.dart` (7 strings)
- `../lib/screens/assign_ehk_ca_screen/widgets/jobchart_uploaded_image.dart` (9 strings)
- `../lib/screens/assign_ehk_ca_screen/widgets/return_gap.dart` (4 strings)
- `../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_table.dart` (9 strings)
- `../lib/screens/assign_ehk_ca_screen/widgets/assign_ehk_ca_filters.dart` (4 strings)
- `../lib/screens/assign_ehk_ca_screen/widgets/jobchart_image_upload.dart` (20 strings)
- `../lib/widgets/success_modal.dart` (1 strings)
- `../lib/widgets/custom_app_bar.dart` (8 strings)
- `../lib/widgets/error_modal.dart` (1 strings)
- `../lib/widgets/language_selector.dart` (3 strings)
- `../lib/widgets/custom_drawer.dart` (19 strings)

## Sample Extracted Strings by Category
### text_widgets
- "Re-enter OTP" (../lib/core/comman_widgets/input_fields/otp_boxes.dart:67)
- "Deny" (../lib/utils/permission_handler_service.dart:98)
- "Enable" (../lib/utils/permission_handler_service.dart:111)
### app_bar_titles
- "Location Access Required" (../lib/utils/show_location_permission_disclosure.dart:8)
- "Upload Status" (../lib/screens/attendance/image_upload.dart:781)
- "Compressing image" (../lib/screens/attendance/image_upload.dart:795)
- "Upload ${entry.key.substring(0, 6)}..." (../lib/screens/attendance/image_upload.dart:816)
### form_labels
- "Train Number" (../lib/screens/attendance/attendance_screen.dart:1324)
- "Date" (../lib/screens/attendance/attendance_screen.dart:1379)
- "Select Date (DD-MMM-YYYY)" (../lib/screens/pdf_screen/widgets/date_select.dart:213)
### button_labels
- "Deny" (../lib/utils/permission_handler_service.dart:98)
- "Enable" (../lib/utils/permission_handler_service.dart:111)
- "Decline" (../lib/utils/show_location_permission_disclosure.dart:27)
- "Accept" (../lib/utils/show_location_permission_disclosure.dart:34)
### snackbar_messages
- "Please select a train first" (../lib/screens/attendance/attendance_screen.dart:209)
- "Data refreshed successfully" (../lib/screens/attendance/attendance_screen.dart:1030)
- "Train Location Saved Successfully" (../lib/screens/attendance/attendance_screen.dart:1059)
### dialog_content
## Next Steps
1. Review the generated `app_en.arb` file in `lib/l10n/`
2. Replace hardcoded strings in your Dart files with `AppLocalizations.of(context)!.keyName`
3. Create ARB files for other supported languages
4. Test the localization implementation
5. Update your `l10n.yaml` configuration if needed
