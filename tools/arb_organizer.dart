import 'dart:io';
import 'dart:convert';

/// Tool to organize and filter extracted ARB strings for the main ARB file
class ARBOrganizer {
  /// Categories of strings to prioritize for immediate localization
  static const Map<String, int> categoryPriority = {
    'text_widgets': 1,       // Highest priority - visible text
    'app_bar_titles': 1,     // Highest priority - screen titles
    'button_labels': 1,      // Highest priority - button text
    'form_labels': 2,        // Medium priority - form fields
    'general_strings': 3,    // Lower priority - general strings
  };

  /// Keywords that indicate UI text that should be localized
  static const List<String> uiKeywords = [
    'login', 'signup', 'submit', 'cancel', 'save', 'delete', 'edit', 'update',
    'search', 'refresh', 'loading', 'error', 'success', 'warning', 'info',
    'home', 'profile', 'settings', 'help', 'about', 'contact', 'feedback',
    'yes', 'no', 'ok', 'confirm', 'continue', 'next', 'previous', 'skip',
    'train', 'station', 'passenger', 'ticket', 'coach', 'seat', 'journey',
    'upload', 'download', 'report', 'attendance', 'schedule', 'status',
    'details', 'summary', 'history', 'notification', 'message', 'alert'
  ];

  /// Process extracted ARB file and filter relevant strings
  static Future<Map<String, dynamic>> processExtractedARB(String extractedARBPath) async {
    final file = File(extractedARBPath);
    if (!file.existsSync()) {
      throw Exception('Extracted ARB file not found: $extractedARBPath');
    }

    final content = await file.readAsString();
    final Map<String, dynamic> extractedARB = jsonDecode(content);
    
    Map<String, dynamic> prioritizedStrings = {'@@locale': 'en'};
    Map<String, List<Map<String, String>>> categorizedStrings = {};

    // Process each string entry
    for (String key in extractedARB.keys) {
      if (key.startsWith('@@') || key.startsWith('@')) continue; // Skip metadata
      
      final String text = extractedARB[key];
      final Map<String, dynamic>? metadata = extractedARB['@$key'];
      
      if (metadata != null) {
        final String category = metadata['context'] ?? 'general_strings';
        final int priority = categoryPriority[category] ?? 3;
        
        // Check if this string should be prioritized
        if (_shouldPrioritizeString(text, category, priority)) {
          prioritizedStrings[key] = text;
          prioritizedStrings['@$key'] = metadata;
          
          // Categorize for reporting
          if (!categorizedStrings.containsKey(category)) {
            categorizedStrings[category] = [];
          }
          categorizedStrings[category]!.add({
            'key': key,
            'text': text,
            'priority': priority.toString(),
          });
        }
      }
    }

    return {
      'prioritized_arb': prioritizedStrings,
      'categorized_summary': categorizedStrings,
      'total_original': extractedARB.length ~/ 2, // Rough count excluding metadata
      'total_prioritized': prioritizedStrings.length ~/ 2,
    };
  }

  /// Check if a string should be prioritized for localization
  static bool _shouldPrioritizeString(String text, String category, int priority) {
    // Always include high priority categories
    if (priority <= 2) {
      // Filter out obvious non-UI strings
      if (_isNonUIString(text)) return false;
      
      // Include UI-relevant strings
      if (_containsUIKeywords(text.toLowerCase())) return true;
      
      // Include short, likely UI strings
      if (text.length <= 30 && !text.contains('/') && !text.contains('_')) return true;
    }
    
    // For lower priority, be more selective
    if (priority == 3) {
      return _containsUIKeywords(text.toLowerCase()) && 
             text.length <= 20 && 
             !_isNonUIString(text);
    }
    
    return false;
  }

  /// Check if text contains UI-relevant keywords
  static bool _containsUIKeywords(String text) {
    return uiKeywords.any((keyword) => text.contains(keyword));
  }

  /// Check if string is obviously non-UI (routes, technical terms, etc.)
  static bool _isNonUIString(String text) {
    return text.startsWith('/') ||           // Routes
           text.contains('_') && text.length > 10 ||  // Long technical names
           text.contains('@') ||             // Emails
           text.contains('http') ||          // URLs
           text.contains('.dart') ||         // File names
           text.contains('()') ||            // Function calls
           text.contains('{}') ||            // Object notation
           RegExp(r'^\d+$').hasMatch(text) || // Pure numbers
           text.contains('API') ||           // API related
           text.contains('JSON') ||          // Data formats
           text.length > 50;                 // Very long strings
  }

  /// Merge prioritized strings with existing ARB file
  static Future<Map<String, dynamic>> mergeWithExistingARB(
    Map<String, dynamic> prioritizedData,
    String existingARBPath
  ) async {
    final existingFile = File(existingARBPath);
    Map<String, dynamic> existingARB = {'@@locale': 'en'};
    
    if (existingFile.existsSync()) {
      final content = await existingFile.readAsString();
      existingARB = jsonDecode(content);
    }

    final Map<String, dynamic> prioritizedARB = prioritizedData['prioritized_arb'];
    Map<String, dynamic> mergedARB = Map.from(existingARB);
    int newStringsAdded = 0;

    // Add new strings that don't exist in the current ARB
    for (String key in prioritizedARB.keys) {
      if (!mergedARB.containsKey(key)) {
        mergedARB[key] = prioritizedARB[key];
        if (!key.startsWith('@') && !key.startsWith('@@')) {
          newStringsAdded++;
        }
      }
    }

    return {
      'merged_arb': mergedARB,
      'new_strings_added': newStringsAdded,
      'total_strings': mergedARB.length ~/ 2,
    };
  }

  /// Generate a summary report of the ARB organization process
  static String generateSummaryReport(
    Map<String, dynamic> prioritizedData,
    Map<String, dynamic> mergeResult
  ) {
    final StringBuffer report = StringBuffer();
    
    report.writeln('📊 ARB Organization Summary Report');
    report.writeln('===================================');
    report.writeln();
    
    report.writeln('📈 String Statistics:');
    report.writeln('   Original extracted strings: ${prioritizedData['total_original']}');
    report.writeln('   Prioritized for localization: ${prioritizedData['total_prioritized']}');
    report.writeln('   New strings added to ARB: ${mergeResult['new_strings_added']}');
    report.writeln('   Total strings in final ARB: ${mergeResult['total_strings']}');
    report.writeln();
    
    report.writeln('📋 Strings by Category:');
    final Map<String, dynamic> categorized = prioritizedData['categorized_summary'];
    for (String category in categorized.keys) {
      final List<dynamic> strings = categorized[category];
      report.writeln('   ${category.padRight(20)}: ${strings.length} strings');
    }
    report.writeln();
    
    report.writeln('🎯 Prioritization Criteria Used:');
    report.writeln('   ✅ Text widgets and UI labels (Priority 1)');
    report.writeln('   ✅ AppBar titles and button labels (Priority 1)');
    report.writeln('   ✅ Form field labels (Priority 2)');
    report.writeln('   ✅ UI-relevant keywords detected');
    report.writeln('   ✅ Short, user-facing strings');
    report.writeln('   ❌ Route definitions excluded');
    report.writeln('   ❌ Technical/API strings excluded');
    report.writeln('   ❌ Very long strings excluded');
    
    return report.toString();
  }

  /// Save organized ARB file
  static Future<void> saveARBFile(Map<String, dynamic> arbData, String outputPath) async {
    final file = File(outputPath);
    await file.writeAsString(
      JsonEncoder.withIndent('  ').convert(arbData),
    );
    print('📝 Updated ARB file saved to: $outputPath');
  }

  /// Save summary report
  static Future<void> saveSummaryReport(String report, String outputPath) async {
    final file = File(outputPath);
    await file.writeAsString(report);
    print('📊 Summary report saved to: $outputPath');
  }
}

/// Main execution function
Future<void> main(List<String> args) async {
  print('🔧 RailOps ARB File Organizer');
  print('=============================');
  
  String extractedARBPath = args.isNotEmpty ? args[0] : 'lib/l10n/app_en_extracted.arb';
  String existingARBPath = args.length > 1 ? args[1] : 'lib/l10n/app_en.arb';
  String outputARBPath = args.length > 2 ? args[2] : 'lib/l10n/app_en.arb';
  String reportPath = args.length > 3 ? args[3] : 'arb_organization_report.txt';

  print('📁 Input files:');
  print('   Extracted ARB: $extractedARBPath');
  print('   Existing ARB: $existingARBPath');
  print('📁 Output files:');
  print('   Updated ARB: $outputARBPath');
  print('   Summary report: $reportPath');
  print('');

  try {
    // Process extracted ARB file
    print('🔍 Processing extracted ARB file...');
    final prioritizedData = await ARBOrganizer.processExtractedARB(extractedARBPath);
    
    // Merge with existing ARB
    print('🔀 Merging with existing ARB file...');
    final mergeResult = await ARBOrganizer.mergeWithExistingARB(prioritizedData, existingARBPath);
    
    // Save updated ARB file
    await ARBOrganizer.saveARBFile(mergeResult['merged_arb'], outputARBPath);
    
    // Generate and save summary report
    final report = ARBOrganizer.generateSummaryReport(prioritizedData, mergeResult);
    await ARBOrganizer.saveSummaryReport(report, reportPath);
    
    print('✅ ARB organization completed successfully!');
    print('');
    print('📊 Quick Summary:');
    print('   🔢 Processed ${prioritizedData['total_original']} extracted strings');
    print('   ✨ Prioritized ${prioritizedData['total_prioritized']} strings for localization');
    print('   ➕ Added ${mergeResult['new_strings_added']} new strings to ARB');
    print('   📝 Final ARB contains ${mergeResult['total_strings']} localizable strings');
    print('');
    print('🔄 Next steps:');
    print('   1. Review the updated ARB file: $outputARBPath');
    print('   2. Check the summary report: $reportPath');
    print('   3. Run "flutter pub get" to regenerate localization files');
    print('   4. Test the app with the new ARB structure');
    
  } catch (e) {
    print('❌ Error during ARB organization: $e');
    exit(1);
  }
}
