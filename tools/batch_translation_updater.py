#!/usr/bin/env python3
"""
Batch Translation Updater for RailOps Flutter App
Updates ARB files with critical translations in batches
"""

import json
import re
import os
from datetime import datetime

# Critical translations for Phase 1 & 2 implementation
CRITICAL_TRANSLATIONS = {
    'hi': {
        # Status and Error Messages
        "text_loading": "लोड हो रहा है",
        "text_error": "त्रुटि",
        "text_success": "सफलता",
        "text_warning": "चेतावनी",
        "text_info": "जानकारी",
        "text_no_data": "कोई डेटा नहीं",
        "text_please_wait": "कृपया प्रतीक्षा करें",
        "text_try_again": "पुनः प्रयास करें",
        "text_something_went_wrong": "कुछ गलत हुआ",
        "text_network_error": "नेटवर्क त्रुटि",
        "text_connection_failed": "कनेक्शन विफल",
        "text_timeout": "समय समाप्त",
        
        # Authentication & User Management
        "text_sign_in": "साइन इन करें",
        "text_sign_out": "साइन आउट करें",
        "text_sign_up": "साइन अप करें",
        "text_forgot_password": "पासवर्ड भूल गए",
        "text_reset_password": "पासवर्ड रीसेट करें",
        "text_change_password": "पासवर्ड बदलें",
        "text_current_password": "वर्तमान पासवर्ड",
        "text_new_password": "नया पासवर्ड",
        "text_confirm_password": "पासवर्ड की पुष्टि करें",
        "text_invalid_credentials": "अमान्य क्रेडेंशियल",
        "text_account_locked": "खाता लॉक है",
        "text_session_expired": "सत्र समाप्त हो गया",
        
        # Train Management
        "text_train_details": "ट्रेन विवरण",
        "text_train_status": "ट्रेन स्थिति",
        "text_train_schedule": "ट्रेन समय सारणी",
        "text_departure_time": "प्रस्थान समय",
        "text_arrival_time": "आगमन समय",
        "text_platform_number": "प्लेटफॉर्म संख्या",
        "text_coach_position": "कोच स्थिति",
        "text_seat_availability": "सीट उपलब्धता",
        "text_passenger_list": "यात्री सूची",
        "text_reservation_chart": "आरक्षण चार्ट",
        "text_waiting_list": "प्रतीक्षा सूची",
        "text_confirmed": "पुष्ट",
        "text_waitlisted": "प्रतीक्षा सूची में",
        "text_rac": "RAC",
        "text_cancelled": "रद्द",
        
        # Location & GPS
        "text_current_location": "वर्तमान स्थान",
        "text_location_permission": "स्थान अनुमति",
        "text_enable_location": "स्थान सक्षम करें",
        "text_location_disabled": "स्थान अक्षम",
        "text_gps_not_available": "GPS उपलब्ध नहीं",
        "text_getting_location": "स्थान प्राप्त कर रहे हैं",
        "text_location_accuracy": "स्थान सटीकता",
        "text_distance": "दूरी",
        "text_latitude": "अक्षांश",
        "text_longitude": "देशांतर",
        
        # Notifications
        "text_notifications": "सूचनाएं",
        "text_notification_settings": "सूचना सेटिंग्स",
        "text_push_notifications": "पुश सूचनाएं",
        "text_notification_permission": "सूचना अनुमति",
        "text_mark_as_read": "पढ़ा हुआ चिह्नित करें",
        "text_mark_all_read": "सभी को पढ़ा हुआ चिह्नित करें",
        "text_clear_notifications": "सूचनाएं साफ़ करें",
        "text_no_notifications": "कोई सूचना नहीं",
        
        # File Operations
        "text_download_file": "फाइल डाउनलोड करें",
        "text_upload_file": "फाइल अपलोड करें",
        "text_file_size": "फाइल आकार",
        "text_file_type": "फाइल प्रकार",
        "text_download_complete": "डाउनलोड पूर्ण",
        "text_upload_complete": "अपलोड पूर्ण",
        "text_download_failed": "डाउनलोड विफल",
        "text_upload_failed": "अपलोड विफल",
        "text_file_not_found": "फाइल नहीं मिली",
        "text_invalid_file": "अमान्य फाइल",
        
        # Camera & Gallery
        "text_take_photo": "फोटो लें",
        "text_choose_from_gallery": "गैलरी से चुनें",
        "text_camera_permission": "कैमरा अनुमति",
        "text_storage_permission": "स्टोरेज अनुमति",
        "text_photo_captured": "फोटो कैप्चर किया गया",
        "text_image_selected": "छवि चुनी गई",
        "text_compress_image": "छवि संपीड़ित करें",
        "text_image_quality": "छवि गुणवत्ता",
        
        # Date & Time
        "text_select_date": "तारीख चुनें",
        "text_select_time": "समय चुनें",
        "text_start_date": "प्रारंभ तारीख",
        "text_end_date": "समाप्ति तारीख",
        "text_start_time": "प्रारंभ समय",
        "text_end_time": "समाप्ति समय",
        "text_duration": "अवधि",
        "text_hours": "घंटे",
        "text_minutes": "मिनट",
        "text_seconds": "सेकंड",
        
        # Search & Filter
        "text_search_placeholder": "खोजें...",
        "text_search_results": "खोज परिणाम",
        "text_no_results": "कोई परिणाम नहीं",
        "text_filter_by": "द्वारा फिल्टर करें",
        "text_sort_by": "द्वारा क्रमबद्ध करें",
        "text_ascending": "आरोही",
        "text_descending": "अवरोही",
        "text_clear_filter": "फिल्टर साफ़ करें",
        "text_apply_filter": "फिल्टर लागू करें",
        
        # Validation Messages
        "text_required_field": "आवश्यक फील्ड",
        "text_invalid_email": "अमान्य ईमेल",
        "text_invalid_phone": "अमान्य फोन नंबर",
        "text_password_too_short": "पासवर्ड बहुत छोटा",
        "text_passwords_dont_match": "पासवर्ड मेल नहीं खाते",
        "text_invalid_format": "अमान्य प्रारूप",
        "text_field_cannot_be_empty": "फील्ड खाली नहीं हो सकता",
        
        # Permissions
        "text_permission_required": "अनुमति आवश्यक",
        "text_grant_permission": "अनुमति दें",
        "text_permission_denied": "अनुमति अस्वीकृत",
        "text_go_to_settings": "सेटिंग्स में जाएं",
        "text_enable_in_settings": "सेटिंग्स में सक्षम करें",
        
        # Connectivity
        "text_no_internet": "इंटरनेट नहीं",
        "text_poor_connection": "खराब कनेक्शन",
        "text_connecting": "कनेक्ट हो रहा है",
        "text_connected": "जुड़ा हुआ",
        "text_disconnected": "डिस्कनेक्ट",
        "text_reconnecting": "पुनः कनेक्ट हो रहा है",
        
        # Coach & Passenger Management
        "text_coach_details": "कोच विवरण",
        "text_passenger_details": "यात्री विवरण",
        "text_seat_number": "सीट संख्या",
        "text_berth_type": "बर्थ प्रकार",
        "text_upper_berth": "ऊपरी बर्थ",
        "text_middle_berth": "मध्य बर्थ",
        "text_lower_berth": "निचली बर्थ",
        "text_side_upper": "साइड ऊपरी",
        "text_side_lower": "साइड निचली",
        "text_occupied": "कब्जे में",
        "text_vacant": "खाली",
        "text_reserved": "आरक्षित",
        
        # Reports & Analytics
        "text_generate_report": "रिपोर्ट जेनरेट करें",
        "text_export_data": "डेटा निर्यात करें",
        "text_print_report": "रिपोर्ट प्रिंट करें",
        "text_share_report": "रिपोर्ट साझा करें",
        "text_report_generated": "रिपोर्ट जेनरेट हुई",
        "text_no_data_to_export": "निर्यात के लिए कोई डेटा नहीं",
        
        # Settings
        "text_app_settings": "ऐप सेटिंग्स",
        "text_language_settings": "भाषा सेटिंग्स",
        "text_theme_settings": "थीम सेटिंग्स",
        "text_privacy_settings": "गोपनीयता सेटिंग्स",
        "text_security_settings": "सुरक्षा सेटिंग्स",
        "text_backup_settings": "बैकअप सेटिंग्स",
        "text_restore_settings": "सेटिंग्स पुनर्स्थापित करें",
        "text_reset_settings": "सेटिंग्स रीसेट करें",
        
        # Common UI Elements
        "text_next": "अगला",
        "text_previous": "पिछला",
        "text_finish": "समाप्त",
        "text_skip": "छोड़ें",
        "text_continue": "जारी रखें",
        "text_back": "वापस",
        "text_forward": "आगे",
        "text_up": "ऊपर",
        "text_down": "नीचे",
        "text_left": "बाएं",
        "text_right": "दाएं",
        "text_expand": "विस्तार करें",
        "text_collapse": "संक्षिप्त करें",
        "text_maximize": "अधिकतम करें",
        "text_minimize": "न्यूनतम करें"
    },
    'bn': {
        # Status and Error Messages
        "text_loading": "লোড হচ্ছে",
        "text_error": "ত্রুটি",
        "text_success": "সফল",
        "text_warning": "সতর্কতা",
        "text_info": "তথ্য",
        "text_no_data": "কোন ডেটা নেই",
        "text_please_wait": "অনুগ্রহ করে অপেক্ষা করুন",
        "text_try_again": "আবার চেষ্টা করুন",
        "text_something_went_wrong": "কিছু ভুল হয়েছে",
        "text_network_error": "নেটওয়ার্ক ত্রুটি",
        "text_connection_failed": "সংযোগ ব্যর্থ",
        "text_timeout": "সময় শেষ",

        # Authentication & User Management
        "text_sign_in": "সাইন ইন করুন",
        "text_sign_out": "সাইন আউট করুন",
        "text_sign_up": "সাইন আপ করুন",
        "text_forgot_password": "পাসওয়ার্ড ভুলে গেছেন",
        "text_reset_password": "পাসওয়ার্ড রিসেট করুন",
        "text_change_password": "পাসওয়ার্ড পরিবর্তন করুন",
        "text_current_password": "বর্তমান পাসওয়ার্ড",
        "text_new_password": "নতুন পাসওয়ার্ড",
        "text_confirm_password": "পাসওয়ার্ড নিশ্চিত করুন",
        "text_invalid_credentials": "অবৈধ শংসাপত্র",
        "text_account_locked": "অ্যাকাউন্ট লক",
        "text_session_expired": "সেশন শেষ হয়েছে",

        # Train Management
        "text_train_details": "ট্রেনের বিবরণ",
        "text_train_status": "ট্রেনের অবস্থা",
        "text_train_schedule": "ট্রেনের সময়সূচী",
        "text_departure_time": "প্রস্থানের সময়",
        "text_arrival_time": "আগমনের সময়",
        "text_platform_number": "প্ল্যাটফর্ম নম্বর",
        "text_coach_position": "কোচের অবস্থান",
        "text_seat_availability": "আসন উপলব্ধতা",
        "text_passenger_list": "যাত্রী তালিকা",
        "text_reservation_chart": "সংরক্ষণ চার্ট",
        "text_waiting_list": "অপেক্ষার তালিকা",
        "text_confirmed": "নিশ্চিত",
        "text_waitlisted": "অপেক্ষার তালিকায়",
        "text_rac": "RAC",
        "text_cancelled": "বাতিল",

        # Location & GPS
        "text_current_location": "বর্তমান অবস্থান",
        "text_location_permission": "অবস্থান অনুমতি",
        "text_enable_location": "অবস্থান সক্ষম করুন",
        "text_location_disabled": "অবস্থান নিষ্ক্রিয়",
        "text_gps_not_available": "GPS উপলব্ধ নয়",
        "text_getting_location": "অবস্থান পাচ্ছি",
        "text_location_accuracy": "অবস্থানের নির্ভুলতা",
        "text_distance": "দূরত্ব",
        "text_latitude": "অক্ষাংশ",
        "text_longitude": "দ্রাঘিমাংশ"
    },
    'as': {
        # Status and Error Messages
        "text_loading": "লোড হৈ আছে",
        "text_error": "ত্ৰুটি",
        "text_success": "সফল",
        "text_warning": "সতৰ্কতা",
        "text_info": "তথ্য",
        "text_no_data": "কোনো ডেটা নাই",
        "text_please_wait": "অনুগ্ৰহ কৰি অপেক্ষা কৰক",
        "text_try_again": "আকৌ চেষ্টা কৰক",
        "text_something_went_wrong": "কিবা ভুল হৈছে",
        "text_network_error": "নেটৱৰ্ক ত্ৰুটি",
        "text_connection_failed": "সংযোগ ব্যৰ্থ",
        "text_timeout": "সময় শেষ",

        # Train Management
        "text_train_details": "ৰেলৰ বিৱৰণ",
        "text_train_status": "ৰেলৰ অৱস্থা",
        "text_train_schedule": "ৰেলৰ সময়সূচী",
        "text_departure_time": "প্ৰস্থানৰ সময়",
        "text_arrival_time": "আগমনৰ সময়",
        "text_platform_number": "প্লেটফৰ্ম নম্বৰ",
        "text_coach_position": "কোচৰ অৱস্থান",
        "text_seat_availability": "আসন উপলব্ধতা",
        "text_passenger_list": "যাত্ৰী তালিকা",
        "text_reservation_chart": "সংৰক্ষণ চাৰ্ট"
    }
}

def update_arb_with_critical_translations(language_code):
    """Update ARB file with critical translations"""
    arb_file = f'lib/l10n/app_{language_code}.arb'
    
    if not os.path.exists(arb_file):
        print(f"ARB file not found: {arb_file}")
        return False
    
    # Create backup
    backup_file = f'{arb_file}_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
    import shutil
    shutil.copy2(arb_file, backup_file)
    print(f"Backup created: {backup_file}")
    
    # Read existing content
    with open(arb_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Parse existing translations
    existing_keys = set()
    for line in content.split('\n'):
        if re.match(r'^  "[^@].*": ', line):
            match = re.match(r'^  "([^"]+)": ', line)
            if match:
                existing_keys.add(match.group(1))
    
    # Get translations for this language
    translations = CRITICAL_TRANSLATIONS.get(language_code, {})
    new_translations = {k: v for k, v in translations.items() if k not in existing_keys}
    
    if not new_translations:
        print(f"No new translations to add for {language_code}")
        return True
    
    # Find insertion point (before closing brace)
    lines = content.split('\n')
    insert_index = -1
    for i in range(len(lines) - 1, -1, -1):
        if lines[i].strip() and not lines[i].strip() == '}':
            insert_index = i
            break
    
    if insert_index == -1:
        print(f"Could not find insertion point in {arb_file}")
        return False
    
    # Add comma to last existing entry if needed
    if not lines[insert_index].endswith(','):
        lines[insert_index] = lines[insert_index] + ','
    
    # Prepare new entries
    new_entries = []
    for key, value in new_translations.items():
        new_entries.append(f'  "{key}": "{value}",')
        new_entries.append(f'  "@{key}": {{')
        new_entries.append(f'    "description": "Critical translation for: {key}",')
        new_entries.append(f'    "context": "critical_translations"')
        new_entries.append(f'  }},')
    
    # Remove comma from the last new entry
    if new_entries:
        new_entries[-1] = new_entries[-1].rstrip(',')
    
    # Insert new entries
    for i, entry in enumerate(new_entries):
        lines.insert(insert_index + 1 + i, entry)
    
    # Write back to file
    with open(arb_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(lines))
    
    print(f"Added {len(new_translations)} critical translations to {arb_file}")
    return True

if __name__ == "__main__":
    # Update all languages with critical translations
    languages = ['hi', 'bn', 'as']  # Start with these three languages

    for lang in languages:
        print(f"\nProcessing {lang.upper()}...")
        if update_arb_with_critical_translations(lang):
            print(f"Successfully updated {lang.upper()} ARB file with critical translations")
        else:
            print(f"Failed to update {lang.upper()} ARB file")

    print(f"\nCompleted updating {len(languages)} languages with critical translations")
