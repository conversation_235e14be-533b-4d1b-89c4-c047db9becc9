#!/usr/bin/env python3
"""
Comprehensive Multi-Language Translation Generator
Adds critical translations for all 9 Indian languages
"""

import json
import re
import os
from datetime import datetime
import shutil

# Core translations that are actually used in Phase 1 & 2
CORE_TRANSLATIONS = {
    'hi': {
        # Most critical UI elements from Phase 1 & 2
        "cancel": "रद्द करें",
        "confirm": "पुष्टि करें", 
        "delete": "हटाएं",
        "edit": "संपादित करें",
        "error": "त्रुटि",
        "home": "होम",
        "loading": "लोड हो रहा है",
        "logout": "लॉगआउट",
        "next": "अगला",
        "no": "नहीं",
        "ok": "ठीक है",
        "password": "पासवर्ड",
        "previous": "पिछला",
        "refresh": "रीफ्रेश",
        "retry": "पुनः प्रयास करें",
        "save": "सहेजें",
        "search": "खोजें",
        "settings": "सेटिंग्स",
        "submit": "जमा करें",
        "username": "उपयोगकर्ता नाम",
        "welcome": "स्वागत है",
        "yes": "हां"
    },
    'bn': {
        "cancel": "বাতিল",
        "confirm": "নিশ্চিত করুন",
        "delete": "মুছুন",
        "edit": "সম্পাদনা",
        "error": "ত্রুটি",
        "home": "হোম",
        "loading": "লোড হচ্ছে",
        "logout": "লগআউট",
        "next": "পরবর্তী",
        "no": "না",
        "ok": "ঠিক আছে",
        "password": "পাসওয়ার্ড",
        "previous": "পূর্ববর্তী",
        "refresh": "রিফ্রেশ",
        "retry": "আবার চেষ্টা করুন",
        "save": "সংরক্ষণ",
        "search": "অনুসন্ধান",
        "settings": "সেটিংস",
        "submit": "জমা দিন",
        "username": "ব্যবহারকারীর নাম",
        "welcome": "স্বাগতম",
        "yes": "হ্যাঁ"
    },
    'as': {
        "cancel": "বাতিল",
        "confirm": "নিশ্চিত কৰক",
        "delete": "মচক",
        "edit": "সম্পাদনা",
        "error": "ত্ৰুটি",
        "home": "ঘৰ",
        "loading": "লোড হৈ আছে",
        "logout": "লগআউট",
        "next": "পৰৱৰ্তী",
        "no": "নহয়",
        "ok": "ঠিক আছে",
        "password": "পাছৱৰ্ড",
        "previous": "পূৰ্ববৰ্তী",
        "refresh": "ৰিফ্ৰেছ",
        "retry": "আকৌ চেষ্টা কৰক",
        "save": "সংৰক্ষণ",
        "search": "অনুসন্ধান",
        "settings": "ছেটিংছ",
        "submit": "জমা দিয়ক",
        "username": "ব্যৱহাৰকাৰীৰ নাম",
        "welcome": "স্বাগতম",
        "yes": "হয়"
    },
    'pa': {
        "cancel": "ਰੱਦ ਕਰੋ",
        "confirm": "ਪੁਸ਼ਟੀ ਕਰੋ",
        "delete": "ਮਿਟਾਓ",
        "edit": "ਸੰਪਾਦਨ",
        "error": "ਗਲਤੀ",
        "home": "ਘਰ",
        "loading": "ਲੋਡ ਹੋ ਰਿਹਾ ਹੈ",
        "logout": "ਲਾਗਆਉਟ",
        "next": "ਅਗਲਾ",
        "no": "ਨਹੀਂ",
        "ok": "ਠੀਕ ਹੈ",
        "password": "ਪਾਸਵਰਡ",
        "previous": "ਪਿਛਲਾ",
        "refresh": "ਰਿਫਰੈਸ਼",
        "retry": "ਦੁਬਾਰਾ ਕੋਸ਼ਿਸ਼ ਕਰੋ",
        "save": "ਸੇਵ ਕਰੋ",
        "search": "ਖੋਜ",
        "settings": "ਸੈਟਿੰਗਜ਼",
        "submit": "ਜਮ੍ਹਾਂ ਕਰੋ",
        "username": "ਯੂਜ਼ਰਨੇਮ",
        "welcome": "ਸਵਾਗਤ ਹੈ",
        "yes": "ਹਾਂ"
    },
    'mr': {
        "cancel": "रद्द करा",
        "confirm": "पुष्टी करा",
        "delete": "हटवा",
        "edit": "संपादन",
        "error": "त्रुटी",
        "home": "घर",
        "loading": "लोड होत आहे",
        "logout": "लॉगआउट",
        "next": "पुढे",
        "no": "नाही",
        "ok": "ठीक आहे",
        "password": "पासवर्ड",
        "previous": "मागे",
        "refresh": "रिफ्रेश",
        "retry": "पुन्हा प्रयत्न करा",
        "save": "जतन करा",
        "search": "शोध",
        "settings": "सेटिंग्ज",
        "submit": "सबमिट",
        "username": "वापरकर्तानाव",
        "welcome": "स्वागत आहे",
        "yes": "होय"
    },
    'kn': {
        "cancel": "ರದ್ದುಗೊಳಿಸಿ",
        "confirm": "ದೃಢೀಕರಿಸಿ",
        "delete": "ಅಳಿಸಿ",
        "edit": "ಸಂಪಾದಿಸಿ",
        "error": "ದೋಷ",
        "home": "ಮನೆ",
        "loading": "ಲೋಡ್ ಆಗುತ್ತಿದೆ",
        "logout": "ಲಾಗ್ಔಟ್",
        "next": "ಮುಂದೆ",
        "no": "ಇಲ್ಲ",
        "ok": "ಸರಿ",
        "password": "ಪಾಸ್‌ವರ್ಡ್",
        "previous": "ಹಿಂದೆ",
        "refresh": "ರಿಫ್ರೆಶ್",
        "retry": "ಮತ್ತೆ ಪ್ರಯತ್ನಿಸಿ",
        "save": "ಉಳಿಸಿ",
        "search": "ಹುಡುಕಿ",
        "settings": "ಸೆಟ್ಟಿಂಗ್‌ಗಳು",
        "submit": "ಸಲ್ಲಿಸಿ",
        "username": "ಬಳಕೆದಾರ ಹೆಸರು",
        "welcome": "ಸ್ವಾಗತ",
        "yes": "ಹೌದು"
    },
    'ta': {
        "cancel": "ரத்து",
        "confirm": "உறுதி",
        "delete": "நீக்கு",
        "edit": "திருத்து",
        "error": "பிழை",
        "home": "வீடு",
        "loading": "ஏற்றுகிறது",
        "logout": "வெளியேறு",
        "next": "அடுத்து",
        "no": "இல்லை",
        "ok": "சரி",
        "password": "கடவுச்சொல்",
        "previous": "முந்தைய",
        "refresh": "புதுப்பி",
        "retry": "மீண்டும் முயற்சி",
        "save": "சேமி",
        "search": "தேடு",
        "settings": "அமைப்புகள்",
        "submit": "சமர்ப்பி",
        "username": "பயனர்பெயர்",
        "welcome": "வரவேற்கிறோம்",
        "yes": "ஆம்"
    },
    'te': {
        "cancel": "రద్దు",
        "confirm": "ధృవీకరించు",
        "delete": "తొలగించు",
        "edit": "సవరించు",
        "error": "లోపం",
        "home": "ఇల్లు",
        "loading": "లోడవుతోంది",
        "logout": "లాగ్‌అవుట్",
        "next": "తదుపరి",
        "no": "లేదు",
        "ok": "సరే",
        "password": "పాస్‌వర్డ్",
        "previous": "మునుపటి",
        "refresh": "రిఫ్రెష్",
        "retry": "మళ్లీ ప్రయత్నించండి",
        "save": "సేవ్",
        "search": "వెతకు",
        "settings": "సెట్టింగ్‌లు",
        "submit": "సమర్పించు",
        "username": "వినియోగదారు పేరు",
        "welcome": "స్వాగతం",
        "yes": "అవును"
    },
    'ml': {
        "cancel": "റദ്ദാക്കുക",
        "confirm": "സ്ഥിരീകരിക്കുക",
        "delete": "ഇല്ലാതാക്കുക",
        "edit": "എഡിറ്റ് ചെയ്യുക",
        "error": "പിശക്",
        "home": "വീട്",
        "loading": "ലോഡ് ചെയ്യുന്നു",
        "logout": "ലോഗൗട്ട്",
        "next": "അടുത്തത്",
        "no": "ഇല്ല",
        "ok": "ശരി",
        "password": "പാസ്‌വേഡ്",
        "previous": "മുമ്പത്തെ",
        "refresh": "പുതുക്കുക",
        "retry": "വീണ്ടും ശ്രമിക്കുക",
        "save": "സേവ് ചെയ്യുക",
        "search": "തിരയുക",
        "settings": "ക്രമീകരണങ്ങൾ",
        "submit": "സമർപ്പിക്കുക",
        "username": "ഉപയോക്തൃനാമം",
        "welcome": "സ്വാഗതം",
        "yes": "അതെ"
    }
}

def update_arb_with_core_translations(language_code):
    """Update ARB file with core translations"""
    arb_file = f'lib/l10n/app_{language_code}.arb'
    
    if not os.path.exists(arb_file):
        print(f"ARB file not found: {arb_file}")
        return False
    
    # Create backup
    backup_file = f'{arb_file}_backup_core_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
    shutil.copy2(arb_file, backup_file)
    print(f"Backup created: {backup_file}")
    
    # Read existing content
    with open(arb_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Parse existing translations
    existing_keys = set()
    for line in content.split('\n'):
        if re.match(r'^  "[^@].*": ', line):
            match = re.match(r'^  "([^"]+)": ', line)
            if match:
                existing_keys.add(match.group(1))
    
    # Get translations for this language
    translations = CORE_TRANSLATIONS.get(language_code, {})
    new_translations = {k: v for k, v in translations.items() if k not in existing_keys}
    
    if not new_translations:
        print(f"No new core translations to add for {language_code}")
        return True
    
    # Find insertion point (before closing brace)
    lines = content.split('\n')
    insert_index = -1
    for i in range(len(lines) - 1, -1, -1):
        if lines[i].strip() and not lines[i].strip() == '}':
            insert_index = i
            break
    
    if insert_index == -1:
        print(f"Could not find insertion point in {arb_file}")
        return False
    
    # Add comma to last existing entry if needed
    if not lines[insert_index].endswith(','):
        lines[insert_index] = lines[insert_index] + ','
    
    # Prepare new entries
    new_entries = []
    for key, value in new_translations.items():
        new_entries.append(f'  "{key}": "{value}",')
        new_entries.append(f'  "@{key}": {{')
        new_entries.append(f'    "description": "Core translation for: {key}",')
        new_entries.append(f'    "context": "core_ui_elements"')
        new_entries.append(f'  }},')
    
    # Remove comma from the last new entry
    if new_entries:
        new_entries[-1] = new_entries[-1].rstrip(',')
    
    # Insert new entries
    for i, entry in enumerate(new_entries):
        lines.insert(insert_index + 1 + i, entry)
    
    # Write back to file
    with open(arb_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(lines))
    
    print(f"Added {len(new_translations)} core translations to {arb_file}")
    return True

if __name__ == "__main__":
    # Update all 9 languages with core translations
    languages = ['hi', 'bn', 'as', 'pa', 'mr', 'kn', 'ta', 'te', 'ml']
    
    print("Adding core translations to all 9 languages...")
    
    for lang in languages:
        print(f"\nProcessing {lang.upper()}...")
        if update_arb_with_core_translations(lang):
            print(f"Successfully updated {lang.upper()} ARB file")
        else:
            print(f"Failed to update {lang.upper()} ARB file")
    
    print(f"\nCompleted updating all {len(languages)} languages with core translations")
    print("\nNext steps:")
    print("1. Run 'flutter gen-l10n' to regenerate localization files")
    print("2. Test language switching in the app")
    print("3. Verify that critical UI elements are translated")
