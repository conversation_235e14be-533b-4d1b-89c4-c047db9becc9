#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Translation Quality Verification Script

This script verifies the quality and integrity of attendance key translations.
"""

import csv
import re
import os
from typing import Dict, List, Set

LOCALES = ['hi', 'bn', 'gu', 'as', 'kn', 'ml', 'mr', 'pa', 'ta', 'te']

def verify_placeholders(english_text: str, translated_text: str, recorded_placeholders: str) -> bool:
    """Verify that all placeholders are preserved exactly."""
    # Extract placeholders from both texts
    english_placeholders = set(re.findall(r'\{[^}]+\}|\$[a-zA-Z_][a-zA-Z0-9_]*', english_text))
    translated_placeholders = set(re.findall(r'\{[^}]+\}|\$[a-zA-Z_][a-zA-Z0-9_]*', translated_text))
    
    # Check if placeholders match
    return english_placeholders == translated_placeholders

def verify_translation_file(locale: str) -> Dict[str, any]:
    """Verify a single translation file."""
    file_path = f'translations/attendance_{locale}.csv'
    results = {
        'locale': locale,
        'total_keys': 0,
        'placeholder_errors': [],
        'empty_translations': [],
        'quality_issues': [],
        'success': True
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.reader(f)
            header = next(reader)  # Skip header
            
            for row_num, row in enumerate(reader, 2):  # Start from row 2 (after header)
                if len(row) < 3:
                    continue
                    
                key, english_text, translated_text = row[0], row[1], row[2]
                placeholders = row[3] if len(row) > 3 else ''
                
                results['total_keys'] += 1
                
                # Check for empty translations
                if not translated_text.strip():
                    results['empty_translations'].append(key)
                    results['success'] = False
                
                # Verify placeholder integrity
                if not verify_placeholders(english_text, translated_text, placeholders):
                    results['placeholder_errors'].append({
                        'key': key,
                        'row': row_num,
                        'english': english_text,
                        'translated': translated_text
                    })
                    results['success'] = False
                
                # Check for quality issues (untranslated common words)
                if translated_text == english_text and len(english_text.split()) > 1:
                    # Allow single technical terms to remain untranslated
                    if not any(term in english_text.lower() for term in ['api', 'ehk', 'ca', 'obhs', 'n/a']):
                        results['quality_issues'].append({
                            'key': key,
                            'issue': 'Completely untranslated',
                            'text': english_text
                        })
    
    except FileNotFoundError:
        results['success'] = False
        results['error'] = f"File not found: {file_path}"
    except Exception as e:
        results['success'] = False
        results['error'] = f"Error reading file: {str(e)}"
    
    return results

def verify_all_translations():
    """Verify all translation files and generate a report."""
    print("🔍 Verifying Attendance Key Translations...")
    print("=" * 60)
    
    all_results = []
    total_errors = 0
    
    for locale in LOCALES:
        print(f"\n📋 Checking {locale.upper()} translations...")
        results = verify_translation_file(locale)
        all_results.append(results)
        
        if results['success']:
            print(f"✅ {locale}: {results['total_keys']} keys - All checks passed!")
        else:
            print(f"❌ {locale}: Issues found")
            total_errors += 1
            
            if 'error' in results:
                print(f"   🚨 Critical Error: {results['error']}")
                continue
            
            if results['empty_translations']:
                print(f"   📝 Empty translations: {len(results['empty_translations'])}")
                for key in results['empty_translations'][:3]:  # Show first 3
                    print(f"      - {key}")
                if len(results['empty_translations']) > 3:
                    print(f"      ... and {len(results['empty_translations']) - 3} more")
            
            if results['placeholder_errors']:
                print(f"   🔧 Placeholder errors: {len(results['placeholder_errors'])}")
                for error in results['placeholder_errors'][:2]:  # Show first 2
                    print(f"      - {error['key']}: Placeholder mismatch")
                if len(results['placeholder_errors']) > 2:
                    print(f"      ... and {len(results['placeholder_errors']) - 2} more")
            
            if results['quality_issues']:
                print(f"   ⚠️  Quality issues: {len(results['quality_issues'])}")
    
    # Summary Report
    print("\n" + "=" * 60)
    print("📊 TRANSLATION VERIFICATION SUMMARY")
    print("=" * 60)
    
    successful_locales = sum(1 for r in all_results if r['success'])
    total_keys = sum(r['total_keys'] for r in all_results if 'total_keys' in r)
    
    print(f"✅ Successful locales: {successful_locales}/{len(LOCALES)}")
    print(f"📝 Total keys processed: {total_keys}")
    print(f"🎯 Expected total: {len(LOCALES) * 100} (100 keys × {len(LOCALES)} locales)")
    
    if total_errors == 0:
        print("\n🎉 ALL TRANSLATIONS VERIFIED SUCCESSFULLY!")
        print("✅ All placeholders preserved")
        print("✅ All locales generated")
        print("✅ High translation quality maintained")
        print("\n🚀 Ready for production deployment!")
    else:
        print(f"\n⚠️  Found issues in {total_errors} locale(s)")
        print("🔧 Please review and fix the reported issues")
    
    # Generate detailed report file
    with open('translations/verification_report.txt', 'w', encoding='utf-8') as f:
        f.write("ATTENDANCE KEYS TRANSLATION VERIFICATION REPORT\\n")
        f.write("=" * 50 + "\\n\\n")
        
        for results in all_results:
            f.write(f"Locale: {results['locale'].upper()}\\n")
            f.write(f"Status: {'PASS' if results['success'] else 'FAIL'}\\n")
            f.write(f"Total Keys: {results.get('total_keys', 0)}\\n")
            
            if not results['success']:
                if results.get('placeholder_errors'):
                    f.write(f"Placeholder Errors: {len(results['placeholder_errors'])}\\n")
                if results.get('empty_translations'):
                    f.write(f"Empty Translations: {len(results['empty_translations'])}\\n")
                if results.get('quality_issues'):
                    f.write(f"Quality Issues: {len(results['quality_issues'])}\\n")
            
            f.write("\\n" + "-" * 30 + "\\n\\n")
    
    print(f"\\n📋 Detailed report saved to: translations/verification_report.txt")

if __name__ == "__main__":
    verify_all_translations()
