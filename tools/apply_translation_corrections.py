#!/usr/bin/env python3
"""
Translation Corrections Application Script
=========================================

This script applies corrections to translation CSV files based on peer review findings.
It provides both automated fixes for common issues and a framework for manual corrections.

Usage:
    python apply_translation_corrections.py [--dry-run] [--language LANG] [--category CATEGORY]
"""

import csv
import json
import argparse
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime


@dataclass
class TranslationCorrection:
    """Represents a correction to apply to a translation"""
    key: str
    language: str
    current_text: str
    corrected_text: str
    correction_type: str
    reason: str
    reviewer: str = "automated"
    confidence: str = "medium"
    requires_human_review: bool = False


class TranslationCorrector:
    """Handles application of translation corrections"""
    
    def __init__(self, translations_dir: str = "translations"):
        self.translations_dir = Path(translations_dir)
        self.corrections: List[TranslationCorrection] = []
        self.backup_dir = self.translations_dir / "backups"
        self.backup_dir.mkdir(exist_ok=True)
    
    def load_corrections_from_json(self, corrections_file: str) -> None:
        """Load corrections from a JSON file"""
        with open(corrections_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        for correction_data in data:
            correction = TranslationCorrection(**correction_data)
            self.corrections.append(correction)
    
    def add_correction(self, key: str, language: str, current_text: str, 
                      corrected_text: str, correction_type: str, reason: str) -> None:
        """Add a correction to the list"""
        correction = TranslationCorrection(
            key=key,
            language=language,
            current_text=current_text,
            corrected_text=corrected_text,
            correction_type=correction_type,
            reason=reason
        )
        self.corrections.append(correction)
    
    def generate_automatic_corrections(self) -> None:
        """Generate automatic corrections for common issues"""
        print("Generating automatic corrections for common issues...")
        
        # Load all CSV files and analyze for common fixable issues
        csv_files = list(self.translations_dir.glob("attendance_*.csv"))
        
        for file_path in csv_files:
            language = file_path.stem.split('_')[1]
            self._analyze_file_for_corrections(file_path, language)
    
    def _analyze_file_for_corrections(self, file_path: Path, language: str) -> None:
        """Analyze a file for automatic corrections"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
            
            for row in rows:
                key = row['key']
                original = row['english_text']
                translated = row['translated_text']
                
                # Check for common fixable issues
                self._check_placeholder_spacing(key, language, original, translated)
                self._check_punctuation_consistency(key, language, original, translated)
                self._check_capitalization_consistency(key, language, original, translated)
                
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
    
    def _check_placeholder_spacing(self, key: str, language: str, original: str, translated: str) -> None:
        """Check and fix placeholder spacing issues"""
        # Find placeholders with spacing issues
        placeholder_pattern = r'(\\{[^}]+\\})'
        
        # Fix common spacing issues around placeholders
        corrected = translated
        
        # Fix: "{ placeholder }" -> "{placeholder}"
        corrected = re.sub(r'\\{\\s+([^}]+)\\s+\\}', r'{\\1}', corrected)
        
        # Fix: "word{placeholder}" -> "word {placeholder}" (for better readability)
        corrected = re.sub(r'(\\w)(\\{[^}]+\\})', r'\\1 \\2', corrected)
        
        # Fix: "{placeholder}word" -> "{placeholder} word"
        corrected = re.sub(r'(\\{[^}]+\\})(\\w)', r'\\1 \\2', corrected)
        
        if corrected != translated:
            self.add_correction(
                key=key,
                language=language,
                current_text=translated,
                corrected_text=corrected,
                correction_type="placeholder_spacing",
                reason="Fixed placeholder spacing for better readability"
            )
    
    def _check_punctuation_consistency(self, key: str, language: str, original: str, translated: str) -> None:
        """Check and fix punctuation consistency"""
        # Ensure translated text has same ending punctuation as original
        original_ends = original.rstrip()[-1:] if original.rstrip() else ""
        translated_ends = translated.rstrip()[-1:] if translated.rstrip() else ""
        
        if original_ends in '.!?:,' and translated_ends != original_ends:
            corrected = translated.rstrip() + original_ends
            
            self.add_correction(
                key=key,
                language=language,
                current_text=translated,
                corrected_text=corrected,
                correction_type="punctuation_consistency",
                reason=f"Added missing punctuation '{original_ends}' to match original"
            )
    
    def _check_capitalization_consistency(self, key: str, language: str, original: str, translated: str) -> None:
        """Check capitalization consistency for certain patterns"""
        # Check if original starts with capital and translated doesn't (for certain contexts)
        if original and original[0].isupper() and translated and translated[0].islower():
            # Only apply to UI elements that should maintain capitalization
            ui_indicators = ['button', 'label', 'title', 'screen', 'update', 'cancel']
            
            if any(indicator in key.lower() for indicator in ui_indicators):
                corrected = translated[0].upper() + translated[1:]
                
                self.add_correction(
                    key=key,
                    language=language,
                    current_text=translated,
                    corrected_text=corrected,
                    correction_type="capitalization",
                    reason="Capitalized first letter to match UI convention"
                )
    
    def backup_file(self, file_path: Path) -> Path:
        """Create a backup of the file before modification"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{file_path.stem}_{timestamp}.csv"
        backup_path = self.backup_dir / backup_name
        
        import shutil
        shutil.copy2(file_path, backup_path)
        return backup_path
    
    def apply_corrections_to_file(self, file_path: Path, dry_run: bool = False) -> int:
        """Apply corrections to a specific file"""
        language = file_path.stem.split('_')[1]
        
        # Get corrections for this language
        file_corrections = [c for c in self.corrections if c.language == language]
        
        if not file_corrections:
            return 0
        
        if not dry_run:
            backup_path = self.backup_file(file_path)
            print(f"Backed up {file_path} to {backup_path}")
        
        # Load current file
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
        
        corrections_applied = 0
        
        # Apply corrections
        for i, row in enumerate(rows):
            key = row['key']
            current_text = row['translated_text']
            
            # Find applicable corrections
            applicable_corrections = [c for c in file_corrections 
                                    if c.key == key and c.current_text == current_text]
            
            for correction in applicable_corrections:
                if dry_run:
                    print(f"  [DRY RUN] Would correct {key}: '{current_text}' -> '{correction.corrected_text}'")
                    print(f"    Reason: {correction.reason}")
                else:
                    rows[i]['translated_text'] = correction.corrected_text
                    print(f"  Applied correction to {key}: {correction.reason}")
                
                corrections_applied += 1
        
        # Write back to file if not dry run
        if not dry_run and corrections_applied > 0:
            with open(file_path, 'w', encoding='utf-8', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=reader.fieldnames)
                writer.writeheader()
                writer.writerows(rows)
        
        return corrections_applied
    
    def apply_all_corrections(self, dry_run: bool = False, language_filter: Optional[str] = None) -> None:
        """Apply all corrections to CSV files"""
        csv_files = list(self.translations_dir.glob("attendance_*.csv"))
        
        if language_filter:
            csv_files = [f for f in csv_files if f.stem.split('_')[1] == language_filter]
        
        total_corrections = 0
        
        for file_path in sorted(csv_files):
            language = file_path.stem.split('_')[1]
            print(f"\\n{'[DRY RUN] ' if dry_run else ''}Processing {language} translations...")
            
            corrections_applied = self.apply_corrections_to_file(file_path, dry_run)
            total_corrections += corrections_applied
            
            if corrections_applied > 0:
                print(f"  Applied {corrections_applied} corrections")
            else:
                print(f"  No corrections needed")
        
        print(f"\\nTotal corrections {'that would be ' if dry_run else ''}applied: {total_corrections}")
    
    def generate_correction_template(self, output_file: str = "correction_template.json") -> None:
        """Generate a template JSON file for manual corrections"""
        template = [
            {
                "key": "example_key",
                "language": "hi",
                "current_text": "Current translation text",
                "corrected_text": "Corrected translation text",
                "correction_type": "accuracy|tone|terminology|formatting",
                "reason": "Explanation of why this correction is needed",
                "reviewer": "reviewer_name"
            }
        ]
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(template, f, indent=2, ensure_ascii=False)
        
        print(f"Correction template generated: {output_file}")
    
    def validate_corrections(self) -> bool:
        """Validate that corrections are properly formatted"""
        valid = True
        
        for correction in self.corrections:
            # Check that all required fields are present
            if not all([correction.key, correction.language, correction.current_text, 
                       correction.corrected_text, correction.correction_type]):
                print(f"Invalid correction: Missing required fields for {correction.key}")
                valid = False
            
            # Check that the correction actually changes something
            if correction.current_text == correction.corrected_text:
                print(f"Warning: No change in correction for {correction.key}")
            
            # Check that language code is valid
            valid_languages = ['hi', 'bn', 'gu', 'as', 'kn', 'ml', 'mr', 'pa', 'ta', 'te']
            if correction.language not in valid_languages:
                print(f"Invalid language code: {correction.language}")
                valid = False
        
        return valid
    
    def run_corrections(self, dry_run: bool = False, language_filter: Optional[str] = None, 
                       corrections_file: Optional[str] = None) -> None:
        """Run the correction process"""
        print("Translation Corrections Application")
        print("=" * 40)
        
        # Load manual corrections if provided
        if corrections_file:
            print(f"Loading corrections from: {corrections_file}")
            self.load_corrections_from_json(corrections_file)
        
        # Generate automatic corrections
        self.generate_automatic_corrections()
        
        print(f"Total corrections loaded: {len(self.corrections)}")
        
        # Validate corrections
        if not self.validate_corrections():
            print("Validation failed. Please fix the corrections and try again.")
            return
        
        # Apply corrections
        self.apply_all_corrections(dry_run, language_filter)
        
        if dry_run:
            print("\\nDry run completed. Use --apply to actually apply corrections.")
        else:
            print("\\nCorrections applied successfully!")
            print(f"Backups saved to: {self.backup_dir}")


def main():
    parser = argparse.ArgumentParser(description="Apply translation corrections")
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show what would be corrected without applying changes')
    parser.add_argument('--language', type=str, 
                       help='Apply corrections only to specified language')
    parser.add_argument('--corrections-file', type=str,
                       help='JSON file containing manual corrections')
    parser.add_argument('--generate-template', action='store_true',
                       help='Generate a template for manual corrections')
    
    args = parser.parse_args()
    
    corrector = TranslationCorrector()
    
    if args.generate_template:
        corrector.generate_correction_template()
        return
    
    corrector.run_corrections(
        dry_run=args.dry_run,
        language_filter=args.language,
        corrections_file=args.corrections_file
    )


if __name__ == "__main__":
    main()
