import 'dart:io';
import 'dart:convert';

/// Automated String Extraction Tool for RailOps Multi-Language Implementation
/// Priority 2: Complete string extraction with categorization and ARB generation
class AutomatedStringExtractor {
  /// Simple regex patterns that work reliably
  static final Map<String, RegExp> patterns = {
    // Text widget strings - matches Text('string') or Text("string")
    'text_widgets': RegExp(r"Text\s*\(\s*'([^']+)'\s*[,\)]"),
    'text_widgets_double': RegExp(r'Text\s*\(\s*"([^"]+)"\s*[,\)]'),

    // AppBar titles - matches title: Text('string')
    'app_bar_titles': RegExp(r"title\s*:\s*Text\s*\(\s*'([^']+)'\s*[,\)]"),
    'app_bar_titles_double':
        RegExp(r'title\s*:\s*Text\s*\(\s*"([^"]+)"\s*[,\)]'),

    // Form field labels - matches labelText: 'string'
    'form_labels': RegExp(r"labelText\s*:\s*'([^']+)'"),
    'form_labels_double': RegExp(r'labelText\s*:\s*"([^"]+)"'),
    'hint_text': RegExp(r"hintText\s*:\s*'([^']+)'"),
    'hint_text_double': RegExp(r'hintText\s*:\s*"([^"]+)"'),
    'helper_text': RegExp(r"helperText\s*:\s*'([^']+)'"),
    'helper_text_double': RegExp(r'helperText\s*:\s*"([^"]+)"'),

    // Button labels - matches child: Text('string')
    'button_labels': RegExp(r"child\s*:\s*Text\s*\(\s*'([^']+)'\s*[,\)]"),
    'button_labels_double':
        RegExp(r'child\s*:\s*Text\s*\(\s*"([^"]+)"\s*[,\)]'),

    // Snackbar messages
    'snackbar_messages': RegExp(r"SnackBar.*?Text\s*\(\s*'([^']+)'\s*[,\)]"),
    'snackbar_messages_double':
        RegExp(r'SnackBar.*?Text\s*\(\s*"([^"]+)"\s*[,\)]'),

    // Dialog content
    'dialog_content': RegExp(r"AlertDialog.*?Text\s*\(\s*'([^']+)'\s*[,\)]"),
    'dialog_content_double':
        RegExp(r'AlertDialog.*?Text\s*\(\s*"([^"]+)"\s*[,\)]'),
  };

  /// Extract strings from a single file
  static Map<String, List<ExtractedString>> extractFromFile(String filePath) {
    final file = File(filePath);
    if (!file.existsSync()) return {};

    final content = file.readAsStringSync();
    final Map<String, List<ExtractedString>> extracted = {};

    // Skip files that already use localization
    if (content.contains('AppLocalizations.of(context)') ||
        content.contains('AppLocalizations.of(')) {
      print('Skipping already localized file: $filePath');
      return {};
    }

    // Initialize categories
    for (String category in [
      'text_widgets',
      'app_bar_titles',
      'form_labels',
      'button_labels',
      'snackbar_messages',
      'dialog_content'
    ]) {
      extracted[category] = [];
    }

    final lines = content.split('\n');

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];

      // Process each pattern
      patterns.forEach((patternName, pattern) {
        final matches = pattern.allMatches(line);
        for (final match in matches) {
          String extractedText = match.group(1) ?? '';

          if (_isValidString(extractedText)) {
            String category = _getCategoryFromPattern(patternName);
            extracted[category]!.add(ExtractedString(
              text: extractedText,
              file: filePath,
              lineNumber: i + 1,
              category: category,
              suggestedKey: _generateKey(extractedText, category),
            ));
          }
        }
      });
    }

    return extracted;
  }

  /// Get category from pattern name
  static String _getCategoryFromPattern(String patternName) {
    if (patternName.startsWith('text_widgets')) return 'text_widgets';
    if (patternName.startsWith('app_bar_titles')) return 'app_bar_titles';
    if (patternName.startsWith('form_labels') ||
        patternName.startsWith('hint_text') ||
        patternName.startsWith('helper_text')) return 'form_labels';
    if (patternName.startsWith('button_labels')) return 'button_labels';
    if (patternName.startsWith('snackbar_messages')) return 'snackbar_messages';
    if (patternName.startsWith('dialog_content')) return 'dialog_content';
    return 'general_strings';
  }

  /// Extract strings from all Dart files in a directory
  static Future<Map<String, Map<String, List<ExtractedString>>>>
      extractFromDirectory(String dirPath) async {
    final directory = Directory(dirPath);
    if (!directory.existsSync()) {
      throw Exception('Directory does not exist: $dirPath');
    }

    final Map<String, Map<String, List<ExtractedString>>> allResults = {};
    int totalFiles = 0;

    await for (FileSystemEntity entity in directory.list(recursive: true)) {
      if (entity is File && entity.path.endsWith('.dart')) {
        // Skip generated files and test files
        if (entity.path.contains('.g.dart') ||
            entity.path.contains('.freezed.dart') ||
            entity.path.contains('.part.dart') ||
            entity.path.contains('_test.dart') ||
            entity.path.contains('/test/') ||
            entity.path.contains('/.dart_tool/')) {
          continue;
        }

        print('Scanning: ${entity.path}');
        final extracted = extractFromFile(entity.path);

        if (extracted.isNotEmpty) {
          // Only add files that have extractable strings
          bool hasStrings = false;
          for (var category in extracted.values) {
            if (category.isNotEmpty) {
              hasStrings = true;
              break;
            }
          }
          if (hasStrings) {
            allResults[entity.path] = extracted;
            totalFiles++;
          }
        }
      }
    }

    print('Completed scanning $totalFiles files');
    return allResults;
  }

  /// Generate a meaningful key from extracted text
  static String _generateKey(String text, String category) {
    // Clean the text for key generation
    String cleanText =
        text.replaceAll(RegExp(r'[^a-zA-Z0-9\s]'), '').trim().toLowerCase();

    // Split into words and take first few words
    List<String> words = cleanText.split(RegExp(r'\s+'));
    words = words.where((word) => word.isNotEmpty).take(3).toList();

    String baseKey = words.join('_');

    // Add category prefix for context
    String prefix = _getCategoryPrefix(category);
    return '${prefix}_$baseKey';
  }

  /// Get prefix for category
  static String _getCategoryPrefix(String category) {
    switch (category) {
      case 'app_bar_titles':
        return 'title';
      case 'button_labels':
        return 'btn';
      case 'form_labels':
        return 'form';
      case 'text_widgets':
        return 'text';
      case 'snackbar_messages':
        return 'snackbar';
      case 'dialog_content':
        return 'dialog';
      default:
        return 'str';
    }
  }

  /// Check if string is valid for localization
  static bool _isValidString(String text) {
    // Skip empty strings, very short strings, or obvious non-localizable content
    if (text.trim().isEmpty || text.length < 3) return false;

    // Skip URLs, numbers, short codes, etc.
    if (RegExp(r'^https?://').hasMatch(text) ||
        RegExp(r'^\d+$').hasMatch(text) ||
        RegExp(r'^[A-Z]{2,4}$').hasMatch(text) ||
        text.contains('@') ||
        text.contains('://') ||
        text.contains('http') ||
        text.contains('www.') ||
        text.startsWith('assets/') ||
        text.startsWith('images/') ||
        text.contains('.png') ||
        text.contains('.jpg') ||
        text.contains('.svg') ||
        text.contains('.dart') ||
        text.contains('()') ||
        text.contains('{}')) {
      return false;
    }

    return true;
  }

  /// Generate ARB entries from extracted strings
  static Map<String, dynamic> generateARBEntries(
      Map<String, Map<String, List<ExtractedString>>> allResults) {
    Map<String, dynamic> arbEntries = {
      '@@locale': 'en',
    };

    Set<String> usedKeys = <String>{};
    Map<String, Set<String>> uniqueStrings = {};

    // Collect unique strings by category
    for (String filePath in allResults.keys) {
      for (String category in allResults[filePath]!.keys) {
        if (!uniqueStrings.containsKey(category)) {
          uniqueStrings[category] = <String>{};
        }

        for (ExtractedString extracted in allResults[filePath]![category]!) {
          uniqueStrings[category]!.add(extracted.text);
        }
      }
    }

    // Generate ARB entries for unique strings
    for (String category in uniqueStrings.keys) {
      for (String text in uniqueStrings[category]!) {
        String key = _generateUniqueKey(text, category, usedKeys);
        usedKeys.add(key);

        arbEntries[key] = text;
        arbEntries['@$key'] = {
          'description':
              'Text from $category: ${text.length > 50 ? '${text.substring(0, 50)}...' : text}',
          'context': category,
        };
      }
    }

    return arbEntries;
  }

  /// Generate unique key to avoid conflicts
  static String _generateUniqueKey(
      String text, String category, Set<String> usedKeys) {
    String baseKey = _generateKey(text, category);
    String key = baseKey;
    int counter = 1;

    while (usedKeys.contains(key)) {
      key = '${baseKey}_$counter';
      counter++;
    }

    return key;
  }

  /// Save results to JSON file
  static Future<void> saveResults(
      Map<String, dynamic> results, String outputPath) async {
    final file = File(outputPath);
    await file.writeAsString(
      const JsonEncoder.withIndent('  ').convert(results),
    );
    print('Results saved to: $outputPath');
  }
}

/// Class to represent an extracted string with metadata
class ExtractedString {
  final String text;
  final String file;
  final int lineNumber;
  final String category;
  final String suggestedKey;

  ExtractedString({
    required this.text,
    required this.file,
    required this.lineNumber,
    required this.category,
    required this.suggestedKey,
  });

  Map<String, dynamic> toJson() => {
        'text': text,
        'file': file,
        'line_number': lineNumber,
        'category': category,
        'suggested_key': suggestedKey,
      };
}
